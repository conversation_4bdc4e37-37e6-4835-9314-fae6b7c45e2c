// Copyright 2013 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package objabi

// This file defines the IDs for PCDATA and FUNCDATA instructions
// in Go binaries.
//
// These must agree with ../../../runtime/funcdata.h and
// ../../../runtime/symtab.go.

const (
	PCDATA_RegMapIndex   = 0 // if !go115ReduceLiveness
	PCDATA_UnsafePoint   = 0 // if go115ReduceLiveness
	PCDATA_StackMapIndex = 1
	PCDATA_InlTreeIndex  = 2

	FUNCDATA_ArgsPointerMaps    = 0
	FUNCDATA_LocalsPointerMaps  = 1
	FUNCDATA_RegPointerMaps     = 2 // if !go115ReduceLiveness
	FUNCDATA_StackObjects       = 3
	FUNCDATA_InlTree            = 4
	FUNCDATA_OpenCodedDeferInfo = 5

	// ArgsSizeUnknown is set in Func.argsize to mark all functions
	// whose argument size is unknown (C vararg functions, and
	// assembly code without an explicit specification).
	// This value is generated by the compiler, assembler, or linker.
	ArgsSizeUnknown = -0x80000000
)

// Special PCDATA values.
const (
	// PCDATA_RegMapIndex values.
	//
	// Only if !go115ReduceLiveness.
	PCDATA_RegMapUnsafe = PCDATA_UnsafePointUnsafe // Unsafe for async preemption

	// PCDATA_UnsafePoint values.
	PCDATA_UnsafePointSafe   = -1 // Safe for async preemption
	PCDATA_UnsafePointUnsafe = -2 // Unsafe for async preemption

	// PCDATA_Restart1(2) apply on a sequence of instructions, within
	// which if an async preemption happens, we should back off the PC
	// to the start of the sequence when resuming.
	// We need two so we can distinguish the start/end of the sequence
	// in case that two sequences are next to each other.
	PCDATA_Restart1 = -3
	PCDATA_Restart2 = -4

	// Like PCDATA_Restart1, but back to function entry if async preempted.
	PCDATA_RestartAtEntry = -5
)
