// +build codecgen.exec

// Copyright (c) 2012-2020 Ugorji Nwoke. All rights reserved.
// Use of this source code is governed by a MIT license found in the LICENSE file.

package codec

// DO NOT EDIT. THIS FILE IS AUTO-GENERATED FROM gen-dec-(map|array).go.tmpl

const genDecMapTmpl = `
{{var "v"}} := *{{ .Varname }}
{{var "l"}} := z.DecReadMapStart()
if {{var "l"}} == codecSelferDecContainerLenNil{{xs}} {
	*{{ .Varname }} = nil
} else {
if {{var "v"}} == nil {
	{{var "rl"}} := z.DecIn<PERSON>({{var "l"}}, z.Dec<PERSON>asic<PERSON>andle().MaxInitLen, {{ .Size }})
	{{var "v"}} = make(map[{{ .KTyp }}]{{ .Typ }}, {{var "rl"}})
	*{{ .Varname }} = {{var "v"}}
}
{{ $mk := var "mk" -}}
var {{ $mk }} {{ .KTyp }}
var {{var "mv"}} {{ .Typ }}
var {{var "mg"}}, {{var "mdn"}} {{if decElemKindPtr}}, {{var "ms"}}, {{var "mok"}}{{end}} bool
if z.DecBasicHandle().MapValueReset {
	{{if decElemKindPtr}}{{var "mg"}} = true
	{{else if decElemKindIntf}}if !z.DecBasicHandle().InterfaceReset { {{var "mg"}} = true }
	{{else if not decElemKindImmutable}}{{var "mg"}} = true
	{{end}} }
if {{var "l"}} != 0 {
	{{var "hl"}} := {{var "l"}} > 0 
	for {{var "j"}} := 0; z.DecContainerNext({{var "j"}}, {{var "l"}}, {{var "hl"}}); {{var "j"}}++ {
	z.DecReadMapElemKey()
	{{ if eq .KTyp "string" -}}
		{{ decLineVarK $mk -}}{{- /* decLineVarKStrZC $mk */ -}}
	{{ else -}}
		{{ decLineVarK $mk -}}
	{{ end -}}
	{{ if eq .KTyp "interface{}" }}{{/* // special case if a byte array. */ -}}
    if {{var "bv"}}, {{var "bok"}} := {{var "mk"}}.([]byte); {{var "bok"}} {
		{{var "mk"}} = z.DecStringZC({{var "bv"}})
	}
    {{ end -}}
    {{if decElemKindPtr -}}
	{{var "ms"}} = true
    {{end -}}
	if {{var "mg"}} {
		{{if decElemKindPtr -}}
        {{var "mv"}}, {{var "mok"}} = {{var "v"}}[{{ $mk }}]
		if {{var "mok"}} {
			{{var "ms"}} = false
		}
        {{else -}}
        {{var "mv"}} = {{var "v"}}[{{ $mk }}]
        {{end -}}
	} {{if not decElemKindImmutable}}else { {{var "mv"}} = {{decElemZero}} }{{end}}
	z.DecReadMapElemValue()
	{{var "mdn"}} = false
	{{ $x := printf "%vmv%v" .TempVar .Rand }}{{ $y := printf "%vmdn%v" .TempVar .Rand }}{{ decLineVar $x $y -}}
	if {{var "mdn"}} {
		{{var "v"}}[{{ $mk }}] = {{decElemZero}}
	} else {{if decElemKindPtr}} if {{var "ms"}} {{end}} {
		{{var "v"}}[{{ $mk }}] = {{var "mv"}}
	}
}
} // else len==0: leave as-is (do not clear map entries)
z.DecReadMapEnd()
}
`

const genDecListTmpl = `
{{var "v"}} := {{if not isArray}}*{{end}}{{ .Varname }}
{{var "h"}}, {{var "l"}} := z.DecSliceHelperStart() {{/* // helper, containerLenS */}}
{{if not isArray -}}
var {{var "c"}} bool {{/* // changed */}}
_ = {{var "c"}}
if {{var "h"}}.IsNil {
	if {{var "v"}} != nil {
		{{var "v"}} = nil
		{{var "c"}} = true
	}
} else {{end -}}
if {{var "l"}} == 0 {
	{{if isSlice -}}
	if {{var "v"}} == nil {
		{{var "v"}} = []{{ .Typ }}{}
		{{var "c"}} = true
	} else if len({{var "v"}}) != 0 {
		{{var "v"}} = {{var "v"}}[:0]
		{{var "c"}} = true
	} {{else if isChan }}if {{var "v"}} == nil {
		{{var "v"}} = make({{ .CTyp }}, 0)
		{{var "c"}} = true
	}
    {{end -}}
} else {
	{{var "hl"}} := {{var "l"}} > 0
	var {{var "rl"}} int
	_ =  {{var "rl"}}
	{{if isSlice }} if {{var "hl"}} {
	if {{var "l"}} > cap({{var "v"}}) {
		{{var "rl"}} = z.DecInferLen({{var "l"}}, z.DecBasicHandle().MaxInitLen, {{ .Size }})
		if {{var "rl"}} <= cap({{var "v"}}) {
			{{var "v"}} = {{var "v"}}[:{{var "rl"}}]
		} else {
			{{var "v"}} = make([]{{ .Typ }}, {{var "rl"}})
		}
		{{var "c"}} = true
	} else if {{var "l"}} != len({{var "v"}}) {
		{{var "v"}} = {{var "v"}}[:{{var "l"}}]
		{{var "c"}} = true
	}
	}
    {{end -}}
	var {{var "j"}} int 
	{{/* // var {{var "dn"}} bool */ -}}
	for {{var "j"}} = 0; z.DecContainerNext({{var "j"}}, {{var "l"}}, {{var "hl"}}); {{var "j"}}++ {
		{{if not isArray}} if {{var "j"}} == 0 && {{var "v"}} == nil {
			if {{var "hl"}} {
				{{var "rl"}} = z.DecInferLen({{var "l"}}, z.DecBasicHandle().MaxInitLen, {{ .Size }})
			} else {
				{{var "rl"}} = {{if isSlice}}8{{else if isChan}}64{{end}}
			}
			{{var "v"}} = make({{if isSlice}}[]{{ .Typ }}{{else if isChan}}{{.CTyp}}{{end}}, {{var "rl"}})
			{{var "c"}} = true 
		}
        {{end -}}
		{{var "h"}}.ElemContainerState({{var "j"}})
        {{/* {{var "dn"}} = r.TryDecodeAsNil() */}}{{/* commented out, as decLineVar handles this already each time */ -}}
        {{if isChan}}{{ $x := printf "%[1]vvcx%[2]v" .TempVar .Rand }}var {{$x}} {{ .Typ }}
		{{ decLineVar $x -}}
		{{var "v"}} <- {{ $x }}
        {{else}}{{/* // if indefinite, etc, then expand the slice if necessary */ -}}
		var {{var "db"}} bool
		if {{var "j"}} >= len({{var "v"}}) {
			{{if isSlice }} {{var "v"}} = append({{var "v"}}, {{ zero }})
			{{var "c"}} = true
			{{else}} z.DecArrayCannotExpand(len(v), {{var "j"}}+1); {{var "db"}} = true
			{{end -}}
		}
		if {{var "db"}} {
			z.DecSwallow()
		} else {
			{{ $x := printf "%[1]vv%[2]v[%[1]vj%[2]v]" .TempVar .Rand }}{{ decLineVar $x -}}
		}
        {{end -}}
	}
	{{if isSlice}} if {{var "j"}} < len({{var "v"}}) {
		{{var "v"}} = {{var "v"}}[:{{var "j"}}]
		{{var "c"}} = true
	} else if {{var "j"}} == 0 && {{var "v"}} == nil {
		{{var "v"}} = []{{ .Typ }}{}
		{{var "c"}} = true
	}
    {{end -}}
}
{{var "h"}}.End()
{{if not isArray }}if {{var "c"}} { 
	*{{ .Varname }} = {{var "v"}}
}
{{end -}}
`

const genEncChanTmpl = `
{{.Label}}:
switch timeout{{.Sfx}} :=  z.EncBasicHandle().ChanRecvTimeout; {
case timeout{{.Sfx}} == 0: // only consume available
	for {
		select {
		case b{{.Sfx}} := <-{{.Chan}}:
			{{ .Slice }} = append({{.Slice}}, b{{.Sfx}})
		default:
			break {{.Label}}
		}
	}
case timeout{{.Sfx}} > 0: // consume until timeout
	tt{{.Sfx}} := time.NewTimer(timeout{{.Sfx}})
	for {
		select {
		case b{{.Sfx}} := <-{{.Chan}}:
			{{.Slice}} = append({{.Slice}}, b{{.Sfx}})
		case <-tt{{.Sfx}}.C:
			// close(tt.C)
			break {{.Label}}
		}
	}
default: // consume until close
	for b{{.Sfx}} := range {{.Chan}} {
		{{.Slice}} = append({{.Slice}}, b{{.Sfx}})
	}
}
`
