// Code generated by x86map -fmt=decoder x86.csv DO NOT EDIT.

package x86asm

var decoder = [...]uint16{
	uint16(xFail),
	/*1*/ uint16(xCondByte), 243,
	0x00, 490,
	0x01, 496,
	0x02, 525,
	0x03, 531,
	0x04, 560,
	0x05, 566,
	0x06, 595,
	0x07, 602,
	0x08, 609,
	0x09, 615,
	0x0A, 644,
	0x0B, 650,
	0x0C, 679,
	0x0D, 685,
	0x0E, 714,
	0x0F, 721,
	0x10, 8045,
	0x11, 8051,
	0x12, 8080,
	0x13, 8086,
	0x14, 8115,
	0x15, 8121,
	0x16, 8150,
	0x17, 8157,
	0x18, 8164,
	0x19, 8170,
	0x1A, 8199,
	0x1B, 8205,
	0x1C, 8234,
	0x1D, 8240,
	0x1E, 8269,
	0x1F, 8276,
	0x20, 8283,
	0x21, 8289,
	0x22, 8318,
	0x23, 8324,
	0x24, 8353,
	0x25, 8359,
	0x27, 8388,
	0x28, 8394,
	0x29, 8400,
	0x2A, 8429,
	0x2B, 8471,
	0x2C, 8500,
	0x2D, 8506,
	0x2F, 8535,
	0x30, 8541,
	0x31, 8547,
	0x32, 8576,
	0x33, 8582,
	0x34, 8611,
	0x35, 8617,
	0x37, 8646,
	0x38, 8652,
	0x39, 8658,
	0x3A, 8687,
	0x3B, 8693,
	0x3C, 8722,
	0x3D, 8728,
	0x3F, 8757,
	0x40, 8763,
	0x41, 8763,
	0x42, 8763,
	0x43, 8763,
	0x44, 8763,
	0x45, 8763,
	0x46, 8763,
	0x47, 8763,
	0x48, 8778,
	0x49, 8778,
	0x4a, 8778,
	0x4b, 8778,
	0x4c, 8778,
	0x4d, 8778,
	0x4e, 8778,
	0x4f, 8778,
	0x50, 8793,
	0x51, 8793,
	0x52, 8793,
	0x53, 8793,
	0x54, 8793,
	0x55, 8793,
	0x56, 8793,
	0x57, 8793,
	0x58, 8820,
	0x59, 8820,
	0x5a, 8820,
	0x5b, 8820,
	0x5c, 8820,
	0x5d, 8820,
	0x5e, 8820,
	0x5f, 8820,
	0x60, 8847,
	0x61, 8860,
	0x62, 8873,
	0x63, 8892,
	0x68, 8923,
	0x69, 8942,
	0x6A, 8977,
	0x6B, 8982,
	0x6C, 9017,
	0x6D, 9020,
	0x6E, 9033,
	0x6F, 9036,
	0x70, 9109,
	0x71, 9114,
	0x72, 9119,
	0x73, 9124,
	0x74, 9129,
	0x75, 9134,
	0x76, 9139,
	0x77, 9144,
	0x78, 9171,
	0x79, 9176,
	0x7A, 9181,
	0x7B, 9186,
	0x7C, 9191,
	0x7D, 9196,
	0x7E, 9201,
	0x7F, 9206,
	0x80, 9271,
	0x81, 9328,
	0x83, 9569,
	0x84, 9810,
	0x85, 9816,
	0x86, 9845,
	0x87, 9851,
	0x88, 9880,
	0x89, 9886,
	0x8A, 9908,
	0x8B, 9914,
	0x8C, 9936,
	0x8D, 9965,
	0x8E, 9994,
	0x8F, 10023,
	0x90, 10059,
	0x91, 10059,
	0x92, 10059,
	0x93, 10059,
	0x94, 10059,
	0x95, 10059,
	0x96, 10059,
	0x97, 10059,
	0x98, 10085,
	0x99, 10105,
	0x9A, 10125,
	0x9B, 10142,
	0x9C, 10145,
	0x9D, 10168,
	0x9E, 10191,
	0x9F, 10194,
	0xA0, 10197,
	0xA1, 10216,
	0xA2, 10238,
	0xA3, 10257,
	0xA4, 10279,
	0xA5, 10282,
	0xA6, 10302,
	0xA7, 10305,
	0xA8, 10325,
	0xA9, 10331,
	0xAA, 10360,
	0xAB, 10363,
	0xAC, 10383,
	0xAD, 10386,
	0xAE, 10406,
	0xAF, 10409,
	0xb0, 10429,
	0xb1, 10429,
	0xb2, 10429,
	0xb3, 10429,
	0xb4, 10429,
	0xb5, 10429,
	0xb6, 10429,
	0xb7, 10429,
	0xb8, 10435,
	0xb9, 10435,
	0xba, 10435,
	0xbb, 10435,
	0xbc, 10435,
	0xbd, 10435,
	0xbe, 10435,
	0xbf, 10435,
	0xC0, 10464,
	0xC1, 10515,
	0xC2, 10713,
	0xC3, 10718,
	0xC4, 10721,
	0xC5, 10740,
	0xC6, 10759,
	0xC7, 10783,
	0xC8, 10844,
	0xC9, 10851,
	0xCA, 10874,
	0xCB, 10879,
	0xCC, 10882,
	0xCD, 10886,
	0xCE, 10891,
	0xCF, 10897,
	0xD0, 10917,
	0xD1, 10961,
	0xD2, 11152,
	0xD3, 11196,
	0xD4, 11387,
	0xD5, 11395,
	0xD7, 11403,
	0xD8, 11416,
	0xD9, 11625,
	0xDA, 11844,
	0xDB, 11976,
	0xDC, 12147,
	0xDD, 12316,
	0xDE, 12455,
	0xDF, 12629,
	0xE0, 12740,
	0xE1, 12745,
	0xE2, 12750,
	0xE3, 12755,
	0xE4, 12781,
	0xE5, 12787,
	0xE6, 12809,
	0xE7, 12815,
	0xE8, 12873,
	0xE9, 12904,
	0xEA, 12935,
	0xEB, 12952,
	0xEC, 12957,
	0xED, 12962,
	0xEE, 12981,
	0xEF, 12986,
	0xF1, 13005,
	0xF4, 13008,
	0xF5, 13011,
	0xF6, 13014,
	0xF7, 13053,
	0xF8, 13229,
	0xF9, 13232,
	0xFA, 13235,
	0xFB, 13238,
	0xFC, 13241,
	0xFD, 13244,
	0xFE, 13247,
	0xFF, 13264,
	uint16(xFail),
	/*490*/ uint16(xSetOp), uint16(ADD),
	/*492*/ uint16(xReadSlashR),
	/*493*/ uint16(xArgRM8),
	/*494*/ uint16(xArgR8),
	/*495*/ uint16(xMatch),
	/*496*/ uint16(xCondIs64), 499, 515,
	/*499*/ uint16(xCondDataSize), 503, 509, 0,
	/*503*/ uint16(xSetOp), uint16(ADD),
	/*505*/ uint16(xReadSlashR),
	/*506*/ uint16(xArgRM16),
	/*507*/ uint16(xArgR16),
	/*508*/ uint16(xMatch),
	/*509*/ uint16(xSetOp), uint16(ADD),
	/*511*/ uint16(xReadSlashR),
	/*512*/ uint16(xArgRM32),
	/*513*/ uint16(xArgR32),
	/*514*/ uint16(xMatch),
	/*515*/ uint16(xCondDataSize), 503, 509, 519,
	/*519*/ uint16(xSetOp), uint16(ADD),
	/*521*/ uint16(xReadSlashR),
	/*522*/ uint16(xArgRM64),
	/*523*/ uint16(xArgR64),
	/*524*/ uint16(xMatch),
	/*525*/ uint16(xSetOp), uint16(ADD),
	/*527*/ uint16(xReadSlashR),
	/*528*/ uint16(xArgR8),
	/*529*/ uint16(xArgRM8),
	/*530*/ uint16(xMatch),
	/*531*/ uint16(xCondIs64), 534, 550,
	/*534*/ uint16(xCondDataSize), 538, 544, 0,
	/*538*/ uint16(xSetOp), uint16(ADD),
	/*540*/ uint16(xReadSlashR),
	/*541*/ uint16(xArgR16),
	/*542*/ uint16(xArgRM16),
	/*543*/ uint16(xMatch),
	/*544*/ uint16(xSetOp), uint16(ADD),
	/*546*/ uint16(xReadSlashR),
	/*547*/ uint16(xArgR32),
	/*548*/ uint16(xArgRM32),
	/*549*/ uint16(xMatch),
	/*550*/ uint16(xCondDataSize), 538, 544, 554,
	/*554*/ uint16(xSetOp), uint16(ADD),
	/*556*/ uint16(xReadSlashR),
	/*557*/ uint16(xArgR64),
	/*558*/ uint16(xArgRM64),
	/*559*/ uint16(xMatch),
	/*560*/ uint16(xSetOp), uint16(ADD),
	/*562*/ uint16(xReadIb),
	/*563*/ uint16(xArgAL),
	/*564*/ uint16(xArgImm8u),
	/*565*/ uint16(xMatch),
	/*566*/ uint16(xCondIs64), 569, 585,
	/*569*/ uint16(xCondDataSize), 573, 579, 0,
	/*573*/ uint16(xSetOp), uint16(ADD),
	/*575*/ uint16(xReadIw),
	/*576*/ uint16(xArgAX),
	/*577*/ uint16(xArgImm16),
	/*578*/ uint16(xMatch),
	/*579*/ uint16(xSetOp), uint16(ADD),
	/*581*/ uint16(xReadId),
	/*582*/ uint16(xArgEAX),
	/*583*/ uint16(xArgImm32),
	/*584*/ uint16(xMatch),
	/*585*/ uint16(xCondDataSize), 573, 579, 589,
	/*589*/ uint16(xSetOp), uint16(ADD),
	/*591*/ uint16(xReadId),
	/*592*/ uint16(xArgRAX),
	/*593*/ uint16(xArgImm32),
	/*594*/ uint16(xMatch),
	/*595*/ uint16(xCondIs64), 598, 0,
	/*598*/ uint16(xSetOp), uint16(PUSH),
	/*600*/ uint16(xArgES),
	/*601*/ uint16(xMatch),
	/*602*/ uint16(xCondIs64), 605, 0,
	/*605*/ uint16(xSetOp), uint16(POP),
	/*607*/ uint16(xArgES),
	/*608*/ uint16(xMatch),
	/*609*/ uint16(xSetOp), uint16(OR),
	/*611*/ uint16(xReadSlashR),
	/*612*/ uint16(xArgRM8),
	/*613*/ uint16(xArgR8),
	/*614*/ uint16(xMatch),
	/*615*/ uint16(xCondIs64), 618, 634,
	/*618*/ uint16(xCondDataSize), 622, 628, 0,
	/*622*/ uint16(xSetOp), uint16(OR),
	/*624*/ uint16(xReadSlashR),
	/*625*/ uint16(xArgRM16),
	/*626*/ uint16(xArgR16),
	/*627*/ uint16(xMatch),
	/*628*/ uint16(xSetOp), uint16(OR),
	/*630*/ uint16(xReadSlashR),
	/*631*/ uint16(xArgRM32),
	/*632*/ uint16(xArgR32),
	/*633*/ uint16(xMatch),
	/*634*/ uint16(xCondDataSize), 622, 628, 638,
	/*638*/ uint16(xSetOp), uint16(OR),
	/*640*/ uint16(xReadSlashR),
	/*641*/ uint16(xArgRM64),
	/*642*/ uint16(xArgR64),
	/*643*/ uint16(xMatch),
	/*644*/ uint16(xSetOp), uint16(OR),
	/*646*/ uint16(xReadSlashR),
	/*647*/ uint16(xArgR8),
	/*648*/ uint16(xArgRM8),
	/*649*/ uint16(xMatch),
	/*650*/ uint16(xCondIs64), 653, 669,
	/*653*/ uint16(xCondDataSize), 657, 663, 0,
	/*657*/ uint16(xSetOp), uint16(OR),
	/*659*/ uint16(xReadSlashR),
	/*660*/ uint16(xArgR16),
	/*661*/ uint16(xArgRM16),
	/*662*/ uint16(xMatch),
	/*663*/ uint16(xSetOp), uint16(OR),
	/*665*/ uint16(xReadSlashR),
	/*666*/ uint16(xArgR32),
	/*667*/ uint16(xArgRM32),
	/*668*/ uint16(xMatch),
	/*669*/ uint16(xCondDataSize), 657, 663, 673,
	/*673*/ uint16(xSetOp), uint16(OR),
	/*675*/ uint16(xReadSlashR),
	/*676*/ uint16(xArgR64),
	/*677*/ uint16(xArgRM64),
	/*678*/ uint16(xMatch),
	/*679*/ uint16(xSetOp), uint16(OR),
	/*681*/ uint16(xReadIb),
	/*682*/ uint16(xArgAL),
	/*683*/ uint16(xArgImm8u),
	/*684*/ uint16(xMatch),
	/*685*/ uint16(xCondIs64), 688, 704,
	/*688*/ uint16(xCondDataSize), 692, 698, 0,
	/*692*/ uint16(xSetOp), uint16(OR),
	/*694*/ uint16(xReadIw),
	/*695*/ uint16(xArgAX),
	/*696*/ uint16(xArgImm16),
	/*697*/ uint16(xMatch),
	/*698*/ uint16(xSetOp), uint16(OR),
	/*700*/ uint16(xReadId),
	/*701*/ uint16(xArgEAX),
	/*702*/ uint16(xArgImm32),
	/*703*/ uint16(xMatch),
	/*704*/ uint16(xCondDataSize), 692, 698, 708,
	/*708*/ uint16(xSetOp), uint16(OR),
	/*710*/ uint16(xReadId),
	/*711*/ uint16(xArgRAX),
	/*712*/ uint16(xArgImm32),
	/*713*/ uint16(xMatch),
	/*714*/ uint16(xCondIs64), 717, 0,
	/*717*/ uint16(xSetOp), uint16(PUSH),
	/*719*/ uint16(xArgCS),
	/*720*/ uint16(xMatch),
	/*721*/ uint16(xCondByte), 229,
	0x00, 1182,
	0x01, 1239,
	0x02, 1347,
	0x03, 1369,
	0x05, 1391,
	0x06, 1397,
	0x07, 1400,
	0x08, 1406,
	0x09, 1409,
	0x0B, 1412,
	0x0D, 1415,
	0x10, 1428,
	0x11, 1462,
	0x12, 1496,
	0x13, 1539,
	0x14, 1557,
	0x15, 1575,
	0x16, 1593,
	0x17, 1628,
	0x18, 1646,
	0x1F, 1671,
	0x20, 1692,
	0x21, 1707,
	0x22, 1722,
	0x23, 1737,
	0x24, 1752,
	0x26, 1767,
	0x28, 1782,
	0x29, 1800,
	0x2A, 1818,
	0x2B, 1905,
	0x2C, 1939,
	0x2D, 2026,
	0x2E, 2113,
	0x2F, 2131,
	0x30, 2149,
	0x31, 2152,
	0x32, 2155,
	0x33, 2158,
	0x34, 2161,
	0x35, 2164,
	0x38, 2174,
	0x3A, 3075,
	0x40, 3486,
	0x41, 3515,
	0x42, 3544,
	0x43, 3573,
	0x44, 3602,
	0x45, 3631,
	0x46, 3660,
	0x47, 3689,
	0x48, 3718,
	0x49, 3747,
	0x4A, 3776,
	0x4B, 3805,
	0x4C, 3834,
	0x4D, 3863,
	0x4E, 3892,
	0x4F, 3921,
	0x50, 3950,
	0x51, 3968,
	0x52, 4002,
	0x53, 4020,
	0x54, 4038,
	0x55, 4056,
	0x56, 4074,
	0x57, 4092,
	0x58, 4110,
	0x59, 4144,
	0x5A, 4178,
	0x5B, 4212,
	0x5C, 4238,
	0x5D, 4272,
	0x5E, 4306,
	0x5F, 4340,
	0x60, 4374,
	0x61, 4392,
	0x62, 4410,
	0x63, 4428,
	0x64, 4446,
	0x65, 4464,
	0x66, 4482,
	0x67, 4500,
	0x68, 4518,
	0x69, 4536,
	0x6A, 4554,
	0x6B, 4572,
	0x6C, 4590,
	0x6D, 4600,
	0x6E, 4610,
	0x6F, 4677,
	0x70, 4703,
	0x71, 4745,
	0x72, 4808,
	0x73, 4871,
	0x74, 4936,
	0x75, 4954,
	0x76, 4972,
	0x77, 4990,
	0x7C, 4993,
	0x7D, 5011,
	0x7E, 5029,
	0x7F, 5106,
	0x80, 5132,
	0x81, 5163,
	0x82, 5194,
	0x83, 5225,
	0x84, 5256,
	0x85, 5287,
	0x86, 5318,
	0x87, 5349,
	0x88, 5380,
	0x89, 5411,
	0x8A, 5442,
	0x8B, 5473,
	0x8C, 5504,
	0x8D, 5535,
	0x8E, 5566,
	0x8F, 5597,
	0x90, 5628,
	0x91, 5633,
	0x92, 5638,
	0x93, 5643,
	0x94, 5648,
	0x95, 5653,
	0x96, 5658,
	0x97, 5663,
	0x98, 5668,
	0x99, 5673,
	0x9A, 5678,
	0x9B, 5683,
	0x9C, 5688,
	0x9D, 5693,
	0x9E, 5698,
	0x9F, 5703,
	0xA0, 5708,
	0xA1, 5712,
	0xA2, 5739,
	0xA3, 5742,
	0xA4, 5771,
	0xA5, 5806,
	0xA8, 5838,
	0xA9, 5842,
	0xAA, 5869,
	0xAB, 5872,
	0xAC, 5901,
	0xAD, 5936,
	0xAE, 5968,
	0xAF, 6226,
	0xB0, 6255,
	0xB1, 6261,
	0xB2, 6290,
	0xB3, 6319,
	0xB4, 6348,
	0xB5, 6377,
	0xB6, 6406,
	0xB7, 6435,
	0xB8, 6464,
	0xB9, 6501,
	0xBA, 6511,
	0xBB, 6636,
	0xBC, 6665,
	0xBD, 6732,
	0xBE, 6799,
	0xBF, 6828,
	0xC0, 6857,
	0xC1, 6863,
	0xC2, 6892,
	0xC3, 6934,
	0xC4, 6963,
	0xC5, 6985,
	0xC6, 7007,
	0xC7, 7029,
	0xc8, 7158,
	0xc9, 7158,
	0xca, 7158,
	0xcb, 7158,
	0xcc, 7158,
	0xcd, 7158,
	0xce, 7158,
	0xcf, 7158,
	0xD0, 7181,
	0xD1, 7199,
	0xD2, 7217,
	0xD3, 7235,
	0xD4, 7253,
	0xD5, 7271,
	0xD6, 7289,
	0xD7, 7315,
	0xD8, 7333,
	0xD9, 7351,
	0xDA, 7369,
	0xDB, 7387,
	0xDC, 7405,
	0xDD, 7423,
	0xDE, 7441,
	0xDF, 7459,
	0xE0, 7477,
	0xE1, 7495,
	0xE2, 7513,
	0xE3, 7531,
	0xE4, 7549,
	0xE5, 7567,
	0xE6, 7585,
	0xE7, 7611,
	0xE8, 7629,
	0xE9, 7647,
	0xEA, 7665,
	0xEB, 7683,
	0xEC, 7701,
	0xED, 7719,
	0xEE, 7737,
	0xEF, 7755,
	0xF0, 7773,
	0xF1, 7783,
	0xF2, 7801,
	0xF3, 7819,
	0xF4, 7837,
	0xF5, 7855,
	0xF6, 7873,
	0xF7, 7891,
	0xF8, 7909,
	0xF9, 7927,
	0xFA, 7945,
	0xFB, 7963,
	0xFC, 7981,
	0xFD, 7999,
	0xFE, 8017,
	0xFF, 8035,
	uint16(xFail),
	/*1182*/ uint16(xCondSlashR),
	1191, // 0
	1207, // 1
	1223, // 2
	1227, // 3
	1231, // 4
	1235, // 5
	0,    // 6
	0,    // 7
	/*1191*/ uint16(xCondDataSize), 1195, 1199, 1203,
	/*1195*/ uint16(xSetOp), uint16(SLDT),
	/*1197*/ uint16(xArgRM16),
	/*1198*/ uint16(xMatch),
	/*1199*/ uint16(xSetOp), uint16(SLDT),
	/*1201*/ uint16(xArgR32M16),
	/*1202*/ uint16(xMatch),
	/*1203*/ uint16(xSetOp), uint16(SLDT),
	/*1205*/ uint16(xArgR64M16),
	/*1206*/ uint16(xMatch),
	/*1207*/ uint16(xCondDataSize), 1211, 1215, 1219,
	/*1211*/ uint16(xSetOp), uint16(STR),
	/*1213*/ uint16(xArgRM16),
	/*1214*/ uint16(xMatch),
	/*1215*/ uint16(xSetOp), uint16(STR),
	/*1217*/ uint16(xArgR32M16),
	/*1218*/ uint16(xMatch),
	/*1219*/ uint16(xSetOp), uint16(STR),
	/*1221*/ uint16(xArgR64M16),
	/*1222*/ uint16(xMatch),
	/*1223*/ uint16(xSetOp), uint16(LLDT),
	/*1225*/ uint16(xArgRM16),
	/*1226*/ uint16(xMatch),
	/*1227*/ uint16(xSetOp), uint16(LTR),
	/*1229*/ uint16(xArgRM16),
	/*1230*/ uint16(xMatch),
	/*1231*/ uint16(xSetOp), uint16(VERR),
	/*1233*/ uint16(xArgRM16),
	/*1234*/ uint16(xMatch),
	/*1235*/ uint16(xSetOp), uint16(VERW),
	/*1237*/ uint16(xArgRM16),
	/*1238*/ uint16(xMatch),
	/*1239*/ uint16(xCondByte), 8,
	0xC8, 1320,
	0xC9, 1323,
	0xD0, 1326,
	0xD1, 1329,
	0xD5, 1332,
	0xD6, 1335,
	0xF8, 1338,
	0xF9, 1344,
	/*1257*/ uint16(xCondSlashR),
	1266, // 0
	1270, // 1
	1274, // 2
	1285, // 3
	1296, // 4
	0,    // 5
	1312, // 6
	1316, // 7
	/*1266*/ uint16(xSetOp), uint16(SGDT),
	/*1268*/ uint16(xArgM),
	/*1269*/ uint16(xMatch),
	/*1270*/ uint16(xSetOp), uint16(SIDT),
	/*1272*/ uint16(xArgM),
	/*1273*/ uint16(xMatch),
	/*1274*/ uint16(xCondIs64), 1277, 1281,
	/*1277*/ uint16(xSetOp), uint16(LGDT),
	/*1279*/ uint16(xArgM16and32),
	/*1280*/ uint16(xMatch),
	/*1281*/ uint16(xSetOp), uint16(LGDT),
	/*1283*/ uint16(xArgM16and64),
	/*1284*/ uint16(xMatch),
	/*1285*/ uint16(xCondIs64), 1288, 1292,
	/*1288*/ uint16(xSetOp), uint16(LIDT),
	/*1290*/ uint16(xArgM16and32),
	/*1291*/ uint16(xMatch),
	/*1292*/ uint16(xSetOp), uint16(LIDT),
	/*1294*/ uint16(xArgM16and64),
	/*1295*/ uint16(xMatch),
	/*1296*/ uint16(xCondDataSize), 1300, 1304, 1308,
	/*1300*/ uint16(xSetOp), uint16(SMSW),
	/*1302*/ uint16(xArgRM16),
	/*1303*/ uint16(xMatch),
	/*1304*/ uint16(xSetOp), uint16(SMSW),
	/*1306*/ uint16(xArgR32M16),
	/*1307*/ uint16(xMatch),
	/*1308*/ uint16(xSetOp), uint16(SMSW),
	/*1310*/ uint16(xArgR64M16),
	/*1311*/ uint16(xMatch),
	/*1312*/ uint16(xSetOp), uint16(LMSW),
	/*1314*/ uint16(xArgRM16),
	/*1315*/ uint16(xMatch),
	/*1316*/ uint16(xSetOp), uint16(INVLPG),
	/*1318*/ uint16(xArgM),
	/*1319*/ uint16(xMatch),
	/*1320*/ uint16(xSetOp), uint16(MONITOR),
	/*1322*/ uint16(xMatch),
	/*1323*/ uint16(xSetOp), uint16(MWAIT),
	/*1325*/ uint16(xMatch),
	/*1326*/ uint16(xSetOp), uint16(XGETBV),
	/*1328*/ uint16(xMatch),
	/*1329*/ uint16(xSetOp), uint16(XSETBV),
	/*1331*/ uint16(xMatch),
	/*1332*/ uint16(xSetOp), uint16(XEND),
	/*1334*/ uint16(xMatch),
	/*1335*/ uint16(xSetOp), uint16(XTEST),
	/*1337*/ uint16(xMatch),
	/*1338*/ uint16(xCondIs64), 0, 1341,
	/*1341*/ uint16(xSetOp), uint16(SWAPGS),
	/*1343*/ uint16(xMatch),
	/*1344*/ uint16(xSetOp), uint16(RDTSCP),
	/*1346*/ uint16(xMatch),
	/*1347*/ uint16(xCondDataSize), 1351, 1357, 1363,
	/*1351*/ uint16(xSetOp), uint16(LAR),
	/*1353*/ uint16(xReadSlashR),
	/*1354*/ uint16(xArgR16),
	/*1355*/ uint16(xArgRM16),
	/*1356*/ uint16(xMatch),
	/*1357*/ uint16(xSetOp), uint16(LAR),
	/*1359*/ uint16(xReadSlashR),
	/*1360*/ uint16(xArgR32),
	/*1361*/ uint16(xArgR32M16),
	/*1362*/ uint16(xMatch),
	/*1363*/ uint16(xSetOp), uint16(LAR),
	/*1365*/ uint16(xReadSlashR),
	/*1366*/ uint16(xArgR64),
	/*1367*/ uint16(xArgR64M16),
	/*1368*/ uint16(xMatch),
	/*1369*/ uint16(xCondDataSize), 1373, 1379, 1385,
	/*1373*/ uint16(xSetOp), uint16(LSL),
	/*1375*/ uint16(xReadSlashR),
	/*1376*/ uint16(xArgR16),
	/*1377*/ uint16(xArgRM16),
	/*1378*/ uint16(xMatch),
	/*1379*/ uint16(xSetOp), uint16(LSL),
	/*1381*/ uint16(xReadSlashR),
	/*1382*/ uint16(xArgR32),
	/*1383*/ uint16(xArgR32M16),
	/*1384*/ uint16(xMatch),
	/*1385*/ uint16(xSetOp), uint16(LSL),
	/*1387*/ uint16(xReadSlashR),
	/*1388*/ uint16(xArgR64),
	/*1389*/ uint16(xArgR32M16),
	/*1390*/ uint16(xMatch),
	/*1391*/ uint16(xCondIs64), 0, 1394,
	/*1394*/ uint16(xSetOp), uint16(SYSCALL),
	/*1396*/ uint16(xMatch),
	/*1397*/ uint16(xSetOp), uint16(CLTS),
	/*1399*/ uint16(xMatch),
	/*1400*/ uint16(xCondIs64), 0, 1403,
	/*1403*/ uint16(xSetOp), uint16(SYSRET),
	/*1405*/ uint16(xMatch),
	/*1406*/ uint16(xSetOp), uint16(INVD),
	/*1408*/ uint16(xMatch),
	/*1409*/ uint16(xSetOp), uint16(WBINVD),
	/*1411*/ uint16(xMatch),
	/*1412*/ uint16(xSetOp), uint16(UD2),
	/*1414*/ uint16(xMatch),
	/*1415*/ uint16(xCondSlashR),
	0,    // 0
	1424, // 1
	0,    // 2
	0,    // 3
	0,    // 4
	0,    // 5
	0,    // 6
	0,    // 7
	/*1424*/ uint16(xSetOp), uint16(PREFETCHW),
	/*1426*/ uint16(xArgM8),
	/*1427*/ uint16(xMatch),
	/*1428*/ uint16(xCondPrefix), 4,
	0xF3, 1456,
	0xF2, 1450,
	0x66, 1444,
	0x0, 1438,
	/*1438*/ uint16(xSetOp), uint16(MOVUPS),
	/*1440*/ uint16(xReadSlashR),
	/*1441*/ uint16(xArgXmm1),
	/*1442*/ uint16(xArgXmm2M128),
	/*1443*/ uint16(xMatch),
	/*1444*/ uint16(xSetOp), uint16(MOVUPD),
	/*1446*/ uint16(xReadSlashR),
	/*1447*/ uint16(xArgXmm1),
	/*1448*/ uint16(xArgXmm2M128),
	/*1449*/ uint16(xMatch),
	/*1450*/ uint16(xSetOp), uint16(MOVSD_XMM),
	/*1452*/ uint16(xReadSlashR),
	/*1453*/ uint16(xArgXmm1),
	/*1454*/ uint16(xArgXmm2M64),
	/*1455*/ uint16(xMatch),
	/*1456*/ uint16(xSetOp), uint16(MOVSS),
	/*1458*/ uint16(xReadSlashR),
	/*1459*/ uint16(xArgXmm1),
	/*1460*/ uint16(xArgXmm2M32),
	/*1461*/ uint16(xMatch),
	/*1462*/ uint16(xCondPrefix), 4,
	0xF3, 1490,
	0xF2, 1484,
	0x66, 1478,
	0x0, 1472,
	/*1472*/ uint16(xSetOp), uint16(MOVUPS),
	/*1474*/ uint16(xReadSlashR),
	/*1475*/ uint16(xArgXmm2M128),
	/*1476*/ uint16(xArgXmm1),
	/*1477*/ uint16(xMatch),
	/*1478*/ uint16(xSetOp), uint16(MOVUPD),
	/*1480*/ uint16(xReadSlashR),
	/*1481*/ uint16(xArgXmm2M128),
	/*1482*/ uint16(xArgXmm),
	/*1483*/ uint16(xMatch),
	/*1484*/ uint16(xSetOp), uint16(MOVSD_XMM),
	/*1486*/ uint16(xReadSlashR),
	/*1487*/ uint16(xArgXmm2M64),
	/*1488*/ uint16(xArgXmm1),
	/*1489*/ uint16(xMatch),
	/*1490*/ uint16(xSetOp), uint16(MOVSS),
	/*1492*/ uint16(xReadSlashR),
	/*1493*/ uint16(xArgXmm2M32),
	/*1494*/ uint16(xArgXmm),
	/*1495*/ uint16(xMatch),
	/*1496*/ uint16(xCondPrefix), 4,
	0xF3, 1533,
	0xF2, 1527,
	0x66, 1521,
	0x0, 1506,
	/*1506*/ uint16(xCondIsMem), 1509, 1515,
	/*1509*/ uint16(xSetOp), uint16(MOVHLPS),
	/*1511*/ uint16(xReadSlashR),
	/*1512*/ uint16(xArgXmm1),
	/*1513*/ uint16(xArgXmm2),
	/*1514*/ uint16(xMatch),
	/*1515*/ uint16(xSetOp), uint16(MOVLPS),
	/*1517*/ uint16(xReadSlashR),
	/*1518*/ uint16(xArgXmm),
	/*1519*/ uint16(xArgM64),
	/*1520*/ uint16(xMatch),
	/*1521*/ uint16(xSetOp), uint16(MOVLPD),
	/*1523*/ uint16(xReadSlashR),
	/*1524*/ uint16(xArgXmm),
	/*1525*/ uint16(xArgXmm2M64),
	/*1526*/ uint16(xMatch),
	/*1527*/ uint16(xSetOp), uint16(MOVDDUP),
	/*1529*/ uint16(xReadSlashR),
	/*1530*/ uint16(xArgXmm1),
	/*1531*/ uint16(xArgXmm2M64),
	/*1532*/ uint16(xMatch),
	/*1533*/ uint16(xSetOp), uint16(MOVSLDUP),
	/*1535*/ uint16(xReadSlashR),
	/*1536*/ uint16(xArgXmm1),
	/*1537*/ uint16(xArgXmm2M128),
	/*1538*/ uint16(xMatch),
	/*1539*/ uint16(xCondPrefix), 2,
	0x66, 1551,
	0x0, 1545,
	/*1545*/ uint16(xSetOp), uint16(MOVLPS),
	/*1547*/ uint16(xReadSlashR),
	/*1548*/ uint16(xArgM64),
	/*1549*/ uint16(xArgXmm),
	/*1550*/ uint16(xMatch),
	/*1551*/ uint16(xSetOp), uint16(MOVLPD),
	/*1553*/ uint16(xReadSlashR),
	/*1554*/ uint16(xArgXmm2M64),
	/*1555*/ uint16(xArgXmm),
	/*1556*/ uint16(xMatch),
	/*1557*/ uint16(xCondPrefix), 2,
	0x66, 1569,
	0x0, 1563,
	/*1563*/ uint16(xSetOp), uint16(UNPCKLPS),
	/*1565*/ uint16(xReadSlashR),
	/*1566*/ uint16(xArgXmm1),
	/*1567*/ uint16(xArgXmm2M128),
	/*1568*/ uint16(xMatch),
	/*1569*/ uint16(xSetOp), uint16(UNPCKLPD),
	/*1571*/ uint16(xReadSlashR),
	/*1572*/ uint16(xArgXmm1),
	/*1573*/ uint16(xArgXmm2M128),
	/*1574*/ uint16(xMatch),
	/*1575*/ uint16(xCondPrefix), 2,
	0x66, 1587,
	0x0, 1581,
	/*1581*/ uint16(xSetOp), uint16(UNPCKHPS),
	/*1583*/ uint16(xReadSlashR),
	/*1584*/ uint16(xArgXmm1),
	/*1585*/ uint16(xArgXmm2M128),
	/*1586*/ uint16(xMatch),
	/*1587*/ uint16(xSetOp), uint16(UNPCKHPD),
	/*1589*/ uint16(xReadSlashR),
	/*1590*/ uint16(xArgXmm1),
	/*1591*/ uint16(xArgXmm2M128),
	/*1592*/ uint16(xMatch),
	/*1593*/ uint16(xCondPrefix), 3,
	0xF3, 1622,
	0x66, 1616,
	0x0, 1601,
	/*1601*/ uint16(xCondIsMem), 1604, 1610,
	/*1604*/ uint16(xSetOp), uint16(MOVLHPS),
	/*1606*/ uint16(xReadSlashR),
	/*1607*/ uint16(xArgXmm1),
	/*1608*/ uint16(xArgXmm2),
	/*1609*/ uint16(xMatch),
	/*1610*/ uint16(xSetOp), uint16(MOVHPS),
	/*1612*/ uint16(xReadSlashR),
	/*1613*/ uint16(xArgXmm),
	/*1614*/ uint16(xArgM64),
	/*1615*/ uint16(xMatch),
	/*1616*/ uint16(xSetOp), uint16(MOVHPD),
	/*1618*/ uint16(xReadSlashR),
	/*1619*/ uint16(xArgXmm),
	/*1620*/ uint16(xArgXmm2M64),
	/*1621*/ uint16(xMatch),
	/*1622*/ uint16(xSetOp), uint16(MOVSHDUP),
	/*1624*/ uint16(xReadSlashR),
	/*1625*/ uint16(xArgXmm1),
	/*1626*/ uint16(xArgXmm2M128),
	/*1627*/ uint16(xMatch),
	/*1628*/ uint16(xCondPrefix), 2,
	0x66, 1640,
	0x0, 1634,
	/*1634*/ uint16(xSetOp), uint16(MOVHPS),
	/*1636*/ uint16(xReadSlashR),
	/*1637*/ uint16(xArgM64),
	/*1638*/ uint16(xArgXmm),
	/*1639*/ uint16(xMatch),
	/*1640*/ uint16(xSetOp), uint16(MOVHPD),
	/*1642*/ uint16(xReadSlashR),
	/*1643*/ uint16(xArgXmm2M64),
	/*1644*/ uint16(xArgXmm),
	/*1645*/ uint16(xMatch),
	/*1646*/ uint16(xCondSlashR),
	1655, // 0
	1659, // 1
	1663, // 2
	1667, // 3
	0,    // 4
	0,    // 5
	0,    // 6
	0,    // 7
	/*1655*/ uint16(xSetOp), uint16(PREFETCHNTA),
	/*1657*/ uint16(xArgM8),
	/*1658*/ uint16(xMatch),
	/*1659*/ uint16(xSetOp), uint16(PREFETCHT0),
	/*1661*/ uint16(xArgM8),
	/*1662*/ uint16(xMatch),
	/*1663*/ uint16(xSetOp), uint16(PREFETCHT1),
	/*1665*/ uint16(xArgM8),
	/*1666*/ uint16(xMatch),
	/*1667*/ uint16(xSetOp), uint16(PREFETCHT2),
	/*1669*/ uint16(xArgM8),
	/*1670*/ uint16(xMatch),
	/*1671*/ uint16(xCondSlashR),
	1680, // 0
	0,    // 1
	0,    // 2
	0,    // 3
	0,    // 4
	0,    // 5
	0,    // 6
	0,    // 7
	/*1680*/ uint16(xCondDataSize), 1684, 1688, 0,
	/*1684*/ uint16(xSetOp), uint16(NOP),
	/*1686*/ uint16(xArgRM16),
	/*1687*/ uint16(xMatch),
	/*1688*/ uint16(xSetOp), uint16(NOP),
	/*1690*/ uint16(xArgRM32),
	/*1691*/ uint16(xMatch),
	/*1692*/ uint16(xCondIs64), 1695, 1701,
	/*1695*/ uint16(xSetOp), uint16(MOV),
	/*1697*/ uint16(xReadSlashR),
	/*1698*/ uint16(xArgRmf32),
	/*1699*/ uint16(xArgCR0dashCR7),
	/*1700*/ uint16(xMatch),
	/*1701*/ uint16(xSetOp), uint16(MOV),
	/*1703*/ uint16(xReadSlashR),
	/*1704*/ uint16(xArgRmf64),
	/*1705*/ uint16(xArgCR0dashCR7),
	/*1706*/ uint16(xMatch),
	/*1707*/ uint16(xCondIs64), 1710, 1716,
	/*1710*/ uint16(xSetOp), uint16(MOV),
	/*1712*/ uint16(xReadSlashR),
	/*1713*/ uint16(xArgRmf32),
	/*1714*/ uint16(xArgDR0dashDR7),
	/*1715*/ uint16(xMatch),
	/*1716*/ uint16(xSetOp), uint16(MOV),
	/*1718*/ uint16(xReadSlashR),
	/*1719*/ uint16(xArgRmf64),
	/*1720*/ uint16(xArgDR0dashDR7),
	/*1721*/ uint16(xMatch),
	/*1722*/ uint16(xCondIs64), 1725, 1731,
	/*1725*/ uint16(xSetOp), uint16(MOV),
	/*1727*/ uint16(xReadSlashR),
	/*1728*/ uint16(xArgCR0dashCR7),
	/*1729*/ uint16(xArgRmf32),
	/*1730*/ uint16(xMatch),
	/*1731*/ uint16(xSetOp), uint16(MOV),
	/*1733*/ uint16(xReadSlashR),
	/*1734*/ uint16(xArgCR0dashCR7),
	/*1735*/ uint16(xArgRmf64),
	/*1736*/ uint16(xMatch),
	/*1737*/ uint16(xCondIs64), 1740, 1746,
	/*1740*/ uint16(xSetOp), uint16(MOV),
	/*1742*/ uint16(xReadSlashR),
	/*1743*/ uint16(xArgDR0dashDR7),
	/*1744*/ uint16(xArgRmf32),
	/*1745*/ uint16(xMatch),
	/*1746*/ uint16(xSetOp), uint16(MOV),
	/*1748*/ uint16(xReadSlashR),
	/*1749*/ uint16(xArgDR0dashDR7),
	/*1750*/ uint16(xArgRmf64),
	/*1751*/ uint16(xMatch),
	/*1752*/ uint16(xCondIs64), 1755, 1761,
	/*1755*/ uint16(xSetOp), uint16(MOV),
	/*1757*/ uint16(xReadSlashR),
	/*1758*/ uint16(xArgRmf32),
	/*1759*/ uint16(xArgTR0dashTR7),
	/*1760*/ uint16(xMatch),
	/*1761*/ uint16(xSetOp), uint16(MOV),
	/*1763*/ uint16(xReadSlashR),
	/*1764*/ uint16(xArgRmf64),
	/*1765*/ uint16(xArgTR0dashTR7),
	/*1766*/ uint16(xMatch),
	/*1767*/ uint16(xCondIs64), 1770, 1776,
	/*1770*/ uint16(xSetOp), uint16(MOV),
	/*1772*/ uint16(xReadSlashR),
	/*1773*/ uint16(xArgTR0dashTR7),
	/*1774*/ uint16(xArgRmf32),
	/*1775*/ uint16(xMatch),
	/*1776*/ uint16(xSetOp), uint16(MOV),
	/*1778*/ uint16(xReadSlashR),
	/*1779*/ uint16(xArgTR0dashTR7),
	/*1780*/ uint16(xArgRmf64),
	/*1781*/ uint16(xMatch),
	/*1782*/ uint16(xCondPrefix), 2,
	0x66, 1794,
	0x0, 1788,
	/*1788*/ uint16(xSetOp), uint16(MOVAPS),
	/*1790*/ uint16(xReadSlashR),
	/*1791*/ uint16(xArgXmm1),
	/*1792*/ uint16(xArgXmm2M128),
	/*1793*/ uint16(xMatch),
	/*1794*/ uint16(xSetOp), uint16(MOVAPD),
	/*1796*/ uint16(xReadSlashR),
	/*1797*/ uint16(xArgXmm1),
	/*1798*/ uint16(xArgXmm2M128),
	/*1799*/ uint16(xMatch),
	/*1800*/ uint16(xCondPrefix), 2,
	0x66, 1812,
	0x0, 1806,
	/*1806*/ uint16(xSetOp), uint16(MOVAPS),
	/*1808*/ uint16(xReadSlashR),
	/*1809*/ uint16(xArgXmm2M128),
	/*1810*/ uint16(xArgXmm1),
	/*1811*/ uint16(xMatch),
	/*1812*/ uint16(xSetOp), uint16(MOVAPD),
	/*1814*/ uint16(xReadSlashR),
	/*1815*/ uint16(xArgXmm2M128),
	/*1816*/ uint16(xArgXmm1),
	/*1817*/ uint16(xMatch),
	/*1818*/ uint16(xCondIs64), 1821, 1875,
	/*1821*/ uint16(xCondPrefix), 4,
	0xF3, 1859,
	0xF2, 1843,
	0x66, 1837,
	0x0, 1831,
	/*1831*/ uint16(xSetOp), uint16(CVTPI2PS),
	/*1833*/ uint16(xReadSlashR),
	/*1834*/ uint16(xArgXmm),
	/*1835*/ uint16(xArgMmM64),
	/*1836*/ uint16(xMatch),
	/*1837*/ uint16(xSetOp), uint16(CVTPI2PD),
	/*1839*/ uint16(xReadSlashR),
	/*1840*/ uint16(xArgXmm),
	/*1841*/ uint16(xArgMmM64),
	/*1842*/ uint16(xMatch),
	/*1843*/ uint16(xCondDataSize), 1847, 1853, 0,
	/*1847*/ uint16(xSetOp), uint16(CVTSI2SD),
	/*1849*/ uint16(xReadSlashR),
	/*1850*/ uint16(xArgXmm),
	/*1851*/ uint16(xArgRM32),
	/*1852*/ uint16(xMatch),
	/*1853*/ uint16(xSetOp), uint16(CVTSI2SD),
	/*1855*/ uint16(xReadSlashR),
	/*1856*/ uint16(xArgXmm),
	/*1857*/ uint16(xArgRM32),
	/*1858*/ uint16(xMatch),
	/*1859*/ uint16(xCondDataSize), 1863, 1869, 0,
	/*1863*/ uint16(xSetOp), uint16(CVTSI2SS),
	/*1865*/ uint16(xReadSlashR),
	/*1866*/ uint16(xArgXmm),
	/*1867*/ uint16(xArgRM32),
	/*1868*/ uint16(xMatch),
	/*1869*/ uint16(xSetOp), uint16(CVTSI2SS),
	/*1871*/ uint16(xReadSlashR),
	/*1872*/ uint16(xArgXmm),
	/*1873*/ uint16(xArgRM32),
	/*1874*/ uint16(xMatch),
	/*1875*/ uint16(xCondPrefix), 4,
	0xF3, 1895,
	0xF2, 1885,
	0x66, 1837,
	0x0, 1831,
	/*1885*/ uint16(xCondDataSize), 1847, 1853, 1889,
	/*1889*/ uint16(xSetOp), uint16(CVTSI2SD),
	/*1891*/ uint16(xReadSlashR),
	/*1892*/ uint16(xArgXmm),
	/*1893*/ uint16(xArgRM64),
	/*1894*/ uint16(xMatch),
	/*1895*/ uint16(xCondDataSize), 1863, 1869, 1899,
	/*1899*/ uint16(xSetOp), uint16(CVTSI2SS),
	/*1901*/ uint16(xReadSlashR),
	/*1902*/ uint16(xArgXmm),
	/*1903*/ uint16(xArgRM64),
	/*1904*/ uint16(xMatch),
	/*1905*/ uint16(xCondPrefix), 4,
	0xF3, 1933,
	0xF2, 1927,
	0x66, 1921,
	0x0, 1915,
	/*1915*/ uint16(xSetOp), uint16(MOVNTPS),
	/*1917*/ uint16(xReadSlashR),
	/*1918*/ uint16(xArgM128),
	/*1919*/ uint16(xArgXmm),
	/*1920*/ uint16(xMatch),
	/*1921*/ uint16(xSetOp), uint16(MOVNTPD),
	/*1923*/ uint16(xReadSlashR),
	/*1924*/ uint16(xArgM128),
	/*1925*/ uint16(xArgXmm),
	/*1926*/ uint16(xMatch),
	/*1927*/ uint16(xSetOp), uint16(MOVNTSD),
	/*1929*/ uint16(xReadSlashR),
	/*1930*/ uint16(xArgM64),
	/*1931*/ uint16(xArgXmm),
	/*1932*/ uint16(xMatch),
	/*1933*/ uint16(xSetOp), uint16(MOVNTSS),
	/*1935*/ uint16(xReadSlashR),
	/*1936*/ uint16(xArgM32),
	/*1937*/ uint16(xArgXmm),
	/*1938*/ uint16(xMatch),
	/*1939*/ uint16(xCondIs64), 1942, 1996,
	/*1942*/ uint16(xCondPrefix), 4,
	0xF3, 1980,
	0xF2, 1964,
	0x66, 1958,
	0x0, 1952,
	/*1952*/ uint16(xSetOp), uint16(CVTTPS2PI),
	/*1954*/ uint16(xReadSlashR),
	/*1955*/ uint16(xArgMm),
	/*1956*/ uint16(xArgXmmM64),
	/*1957*/ uint16(xMatch),
	/*1958*/ uint16(xSetOp), uint16(CVTTPD2PI),
	/*1960*/ uint16(xReadSlashR),
	/*1961*/ uint16(xArgMm),
	/*1962*/ uint16(xArgXmmM128),
	/*1963*/ uint16(xMatch),
	/*1964*/ uint16(xCondDataSize), 1968, 1974, 0,
	/*1968*/ uint16(xSetOp), uint16(CVTTSD2SI),
	/*1970*/ uint16(xReadSlashR),
	/*1971*/ uint16(xArgR32),
	/*1972*/ uint16(xArgXmmM64),
	/*1973*/ uint16(xMatch),
	/*1974*/ uint16(xSetOp), uint16(CVTTSD2SI),
	/*1976*/ uint16(xReadSlashR),
	/*1977*/ uint16(xArgR32),
	/*1978*/ uint16(xArgXmmM64),
	/*1979*/ uint16(xMatch),
	/*1980*/ uint16(xCondDataSize), 1984, 1990, 0,
	/*1984*/ uint16(xSetOp), uint16(CVTTSS2SI),
	/*1986*/ uint16(xReadSlashR),
	/*1987*/ uint16(xArgR32),
	/*1988*/ uint16(xArgXmmM32),
	/*1989*/ uint16(xMatch),
	/*1990*/ uint16(xSetOp), uint16(CVTTSS2SI),
	/*1992*/ uint16(xReadSlashR),
	/*1993*/ uint16(xArgR32),
	/*1994*/ uint16(xArgXmmM32),
	/*1995*/ uint16(xMatch),
	/*1996*/ uint16(xCondPrefix), 4,
	0xF3, 2016,
	0xF2, 2006,
	0x66, 1958,
	0x0, 1952,
	/*2006*/ uint16(xCondDataSize), 1968, 1974, 2010,
	/*2010*/ uint16(xSetOp), uint16(CVTTSD2SI),
	/*2012*/ uint16(xReadSlashR),
	/*2013*/ uint16(xArgR64),
	/*2014*/ uint16(xArgXmmM64),
	/*2015*/ uint16(xMatch),
	/*2016*/ uint16(xCondDataSize), 1984, 1990, 2020,
	/*2020*/ uint16(xSetOp), uint16(CVTTSS2SI),
	/*2022*/ uint16(xReadSlashR),
	/*2023*/ uint16(xArgR64),
	/*2024*/ uint16(xArgXmmM32),
	/*2025*/ uint16(xMatch),
	/*2026*/ uint16(xCondIs64), 2029, 2083,
	/*2029*/ uint16(xCondPrefix), 4,
	0xF3, 2067,
	0xF2, 2051,
	0x66, 2045,
	0x0, 2039,
	/*2039*/ uint16(xSetOp), uint16(CVTPS2PI),
	/*2041*/ uint16(xReadSlashR),
	/*2042*/ uint16(xArgMm),
	/*2043*/ uint16(xArgXmmM64),
	/*2044*/ uint16(xMatch),
	/*2045*/ uint16(xSetOp), uint16(CVTPD2PI),
	/*2047*/ uint16(xReadSlashR),
	/*2048*/ uint16(xArgMm),
	/*2049*/ uint16(xArgXmmM128),
	/*2050*/ uint16(xMatch),
	/*2051*/ uint16(xCondDataSize), 2055, 2061, 0,
	/*2055*/ uint16(xSetOp), uint16(CVTSD2SI),
	/*2057*/ uint16(xReadSlashR),
	/*2058*/ uint16(xArgR32),
	/*2059*/ uint16(xArgXmmM64),
	/*2060*/ uint16(xMatch),
	/*2061*/ uint16(xSetOp), uint16(CVTSD2SI),
	/*2063*/ uint16(xReadSlashR),
	/*2064*/ uint16(xArgR32),
	/*2065*/ uint16(xArgXmmM64),
	/*2066*/ uint16(xMatch),
	/*2067*/ uint16(xCondDataSize), 2071, 2077, 0,
	/*2071*/ uint16(xSetOp), uint16(CVTSS2SI),
	/*2073*/ uint16(xReadSlashR),
	/*2074*/ uint16(xArgR32),
	/*2075*/ uint16(xArgXmmM32),
	/*2076*/ uint16(xMatch),
	/*2077*/ uint16(xSetOp), uint16(CVTSS2SI),
	/*2079*/ uint16(xReadSlashR),
	/*2080*/ uint16(xArgR32),
	/*2081*/ uint16(xArgXmmM32),
	/*2082*/ uint16(xMatch),
	/*2083*/ uint16(xCondPrefix), 4,
	0xF3, 2103,
	0xF2, 2093,
	0x66, 2045,
	0x0, 2039,
	/*2093*/ uint16(xCondDataSize), 2055, 2061, 2097,
	/*2097*/ uint16(xSetOp), uint16(CVTSD2SI),
	/*2099*/ uint16(xReadSlashR),
	/*2100*/ uint16(xArgR64),
	/*2101*/ uint16(xArgXmmM64),
	/*2102*/ uint16(xMatch),
	/*2103*/ uint16(xCondDataSize), 2071, 2077, 2107,
	/*2107*/ uint16(xSetOp), uint16(CVTSS2SI),
	/*2109*/ uint16(xReadSlashR),
	/*2110*/ uint16(xArgR64),
	/*2111*/ uint16(xArgXmmM32),
	/*2112*/ uint16(xMatch),
	/*2113*/ uint16(xCondPrefix), 2,
	0x66, 2125,
	0x0, 2119,
	/*2119*/ uint16(xSetOp), uint16(UCOMISS),
	/*2121*/ uint16(xReadSlashR),
	/*2122*/ uint16(xArgXmm1),
	/*2123*/ uint16(xArgXmm2M32),
	/*2124*/ uint16(xMatch),
	/*2125*/ uint16(xSetOp), uint16(UCOMISD),
	/*2127*/ uint16(xReadSlashR),
	/*2128*/ uint16(xArgXmm1),
	/*2129*/ uint16(xArgXmm2M64),
	/*2130*/ uint16(xMatch),
	/*2131*/ uint16(xCondPrefix), 2,
	0x66, 2143,
	0x0, 2137,
	/*2137*/ uint16(xSetOp), uint16(COMISS),
	/*2139*/ uint16(xReadSlashR),
	/*2140*/ uint16(xArgXmm1),
	/*2141*/ uint16(xArgXmm2M32),
	/*2142*/ uint16(xMatch),
	/*2143*/ uint16(xSetOp), uint16(COMISD),
	/*2145*/ uint16(xReadSlashR),
	/*2146*/ uint16(xArgXmm1),
	/*2147*/ uint16(xArgXmm2M64),
	/*2148*/ uint16(xMatch),
	/*2149*/ uint16(xSetOp), uint16(WRMSR),
	/*2151*/ uint16(xMatch),
	/*2152*/ uint16(xSetOp), uint16(RDTSC),
	/*2154*/ uint16(xMatch),
	/*2155*/ uint16(xSetOp), uint16(RDMSR),
	/*2157*/ uint16(xMatch),
	/*2158*/ uint16(xSetOp), uint16(RDPMC),
	/*2160*/ uint16(xMatch),
	/*2161*/ uint16(xSetOp), uint16(SYSENTER),
	/*2163*/ uint16(xMatch),
	/*2164*/ uint16(xCondDataSize), 2168, 2168, 2171,
	/*2168*/ uint16(xSetOp), uint16(SYSEXIT),
	/*2170*/ uint16(xMatch),
	/*2171*/ uint16(xSetOp), uint16(SYSEXIT),
	/*2173*/ uint16(xMatch),
	/*2174*/ uint16(xCondByte), 54,
	0x00, 2285,
	0x01, 2303,
	0x02, 2321,
	0x03, 2339,
	0x04, 2357,
	0x05, 2375,
	0x06, 2393,
	0x07, 2411,
	0x08, 2429,
	0x09, 2447,
	0x0A, 2465,
	0x0B, 2483,
	0x10, 2501,
	0x14, 2512,
	0x15, 2523,
	0x17, 2534,
	0x1C, 2544,
	0x1D, 2562,
	0x1E, 2580,
	0x20, 2598,
	0x21, 2608,
	0x22, 2618,
	0x23, 2628,
	0x24, 2638,
	0x25, 2648,
	0x28, 2658,
	0x29, 2668,
	0x2A, 2678,
	0x2B, 2688,
	0x30, 2698,
	0x31, 2708,
	0x32, 2718,
	0x33, 2728,
	0x34, 2738,
	0x35, 2748,
	0x37, 2758,
	0x38, 2768,
	0x39, 2778,
	0x3A, 2788,
	0x3B, 2798,
	0x3C, 2808,
	0x3D, 2818,
	0x3E, 2828,
	0x3F, 2838,
	0x40, 2848,
	0x41, 2858,
	0x82, 2868,
	0xDB, 2891,
	0xDC, 2901,
	0xDD, 2911,
	0xDE, 2921,
	0xDF, 2931,
	0xF0, 2941,
	0xF1, 3008,
	uint16(xFail),
	/*2285*/ uint16(xCondPrefix), 2,
	0x66, 2297,
	0x0, 2291,
	/*2291*/ uint16(xSetOp), uint16(PSHUFB),
	/*2293*/ uint16(xReadSlashR),
	/*2294*/ uint16(xArgMm1),
	/*2295*/ uint16(xArgMm2M64),
	/*2296*/ uint16(xMatch),
	/*2297*/ uint16(xSetOp), uint16(PSHUFB),
	/*2299*/ uint16(xReadSlashR),
	/*2300*/ uint16(xArgXmm1),
	/*2301*/ uint16(xArgXmm2M128),
	/*2302*/ uint16(xMatch),
	/*2303*/ uint16(xCondPrefix), 2,
	0x66, 2315,
	0x0, 2309,
	/*2309*/ uint16(xSetOp), uint16(PHADDW),
	/*2311*/ uint16(xReadSlashR),
	/*2312*/ uint16(xArgMm1),
	/*2313*/ uint16(xArgMm2M64),
	/*2314*/ uint16(xMatch),
	/*2315*/ uint16(xSetOp), uint16(PHADDW),
	/*2317*/ uint16(xReadSlashR),
	/*2318*/ uint16(xArgXmm1),
	/*2319*/ uint16(xArgXmm2M128),
	/*2320*/ uint16(xMatch),
	/*2321*/ uint16(xCondPrefix), 2,
	0x66, 2333,
	0x0, 2327,
	/*2327*/ uint16(xSetOp), uint16(PHADDD),
	/*2329*/ uint16(xReadSlashR),
	/*2330*/ uint16(xArgMm1),
	/*2331*/ uint16(xArgMm2M64),
	/*2332*/ uint16(xMatch),
	/*2333*/ uint16(xSetOp), uint16(PHADDD),
	/*2335*/ uint16(xReadSlashR),
	/*2336*/ uint16(xArgXmm1),
	/*2337*/ uint16(xArgXmm2M128),
	/*2338*/ uint16(xMatch),
	/*2339*/ uint16(xCondPrefix), 2,
	0x66, 2351,
	0x0, 2345,
	/*2345*/ uint16(xSetOp), uint16(PHADDSW),
	/*2347*/ uint16(xReadSlashR),
	/*2348*/ uint16(xArgMm1),
	/*2349*/ uint16(xArgMm2M64),
	/*2350*/ uint16(xMatch),
	/*2351*/ uint16(xSetOp), uint16(PHADDSW),
	/*2353*/ uint16(xReadSlashR),
	/*2354*/ uint16(xArgXmm1),
	/*2355*/ uint16(xArgXmm2M128),
	/*2356*/ uint16(xMatch),
	/*2357*/ uint16(xCondPrefix), 2,
	0x66, 2369,
	0x0, 2363,
	/*2363*/ uint16(xSetOp), uint16(PMADDUBSW),
	/*2365*/ uint16(xReadSlashR),
	/*2366*/ uint16(xArgMm1),
	/*2367*/ uint16(xArgMm2M64),
	/*2368*/ uint16(xMatch),
	/*2369*/ uint16(xSetOp), uint16(PMADDUBSW),
	/*2371*/ uint16(xReadSlashR),
	/*2372*/ uint16(xArgXmm1),
	/*2373*/ uint16(xArgXmm2M128),
	/*2374*/ uint16(xMatch),
	/*2375*/ uint16(xCondPrefix), 2,
	0x66, 2387,
	0x0, 2381,
	/*2381*/ uint16(xSetOp), uint16(PHSUBW),
	/*2383*/ uint16(xReadSlashR),
	/*2384*/ uint16(xArgMm1),
	/*2385*/ uint16(xArgMm2M64),
	/*2386*/ uint16(xMatch),
	/*2387*/ uint16(xSetOp), uint16(PHSUBW),
	/*2389*/ uint16(xReadSlashR),
	/*2390*/ uint16(xArgXmm1),
	/*2391*/ uint16(xArgXmm2M128),
	/*2392*/ uint16(xMatch),
	/*2393*/ uint16(xCondPrefix), 2,
	0x66, 2405,
	0x0, 2399,
	/*2399*/ uint16(xSetOp), uint16(PHSUBD),
	/*2401*/ uint16(xReadSlashR),
	/*2402*/ uint16(xArgMm1),
	/*2403*/ uint16(xArgMm2M64),
	/*2404*/ uint16(xMatch),
	/*2405*/ uint16(xSetOp), uint16(PHSUBD),
	/*2407*/ uint16(xReadSlashR),
	/*2408*/ uint16(xArgXmm1),
	/*2409*/ uint16(xArgXmm2M128),
	/*2410*/ uint16(xMatch),
	/*2411*/ uint16(xCondPrefix), 2,
	0x66, 2423,
	0x0, 2417,
	/*2417*/ uint16(xSetOp), uint16(PHSUBSW),
	/*2419*/ uint16(xReadSlashR),
	/*2420*/ uint16(xArgMm1),
	/*2421*/ uint16(xArgMm2M64),
	/*2422*/ uint16(xMatch),
	/*2423*/ uint16(xSetOp), uint16(PHSUBSW),
	/*2425*/ uint16(xReadSlashR),
	/*2426*/ uint16(xArgXmm1),
	/*2427*/ uint16(xArgXmm2M128),
	/*2428*/ uint16(xMatch),
	/*2429*/ uint16(xCondPrefix), 2,
	0x66, 2441,
	0x0, 2435,
	/*2435*/ uint16(xSetOp), uint16(PSIGNB),
	/*2437*/ uint16(xReadSlashR),
	/*2438*/ uint16(xArgMm1),
	/*2439*/ uint16(xArgMm2M64),
	/*2440*/ uint16(xMatch),
	/*2441*/ uint16(xSetOp), uint16(PSIGNB),
	/*2443*/ uint16(xReadSlashR),
	/*2444*/ uint16(xArgXmm1),
	/*2445*/ uint16(xArgXmm2M128),
	/*2446*/ uint16(xMatch),
	/*2447*/ uint16(xCondPrefix), 2,
	0x66, 2459,
	0x0, 2453,
	/*2453*/ uint16(xSetOp), uint16(PSIGNW),
	/*2455*/ uint16(xReadSlashR),
	/*2456*/ uint16(xArgMm1),
	/*2457*/ uint16(xArgMm2M64),
	/*2458*/ uint16(xMatch),
	/*2459*/ uint16(xSetOp), uint16(PSIGNW),
	/*2461*/ uint16(xReadSlashR),
	/*2462*/ uint16(xArgXmm1),
	/*2463*/ uint16(xArgXmm2M128),
	/*2464*/ uint16(xMatch),
	/*2465*/ uint16(xCondPrefix), 2,
	0x66, 2477,
	0x0, 2471,
	/*2471*/ uint16(xSetOp), uint16(PSIGND),
	/*2473*/ uint16(xReadSlashR),
	/*2474*/ uint16(xArgMm1),
	/*2475*/ uint16(xArgMm2M64),
	/*2476*/ uint16(xMatch),
	/*2477*/ uint16(xSetOp), uint16(PSIGND),
	/*2479*/ uint16(xReadSlashR),
	/*2480*/ uint16(xArgXmm1),
	/*2481*/ uint16(xArgXmm2M128),
	/*2482*/ uint16(xMatch),
	/*2483*/ uint16(xCondPrefix), 2,
	0x66, 2495,
	0x0, 2489,
	/*2489*/ uint16(xSetOp), uint16(PMULHRSW),
	/*2491*/ uint16(xReadSlashR),
	/*2492*/ uint16(xArgMm1),
	/*2493*/ uint16(xArgMm2M64),
	/*2494*/ uint16(xMatch),
	/*2495*/ uint16(xSetOp), uint16(PMULHRSW),
	/*2497*/ uint16(xReadSlashR),
	/*2498*/ uint16(xArgXmm1),
	/*2499*/ uint16(xArgXmm2M128),
	/*2500*/ uint16(xMatch),
	/*2501*/ uint16(xCondPrefix), 1,
	0x66, 2505,
	/*2505*/ uint16(xSetOp), uint16(PBLENDVB),
	/*2507*/ uint16(xReadSlashR),
	/*2508*/ uint16(xArgXmm1),
	/*2509*/ uint16(xArgXmm2M128),
	/*2510*/ uint16(xArgXMM0),
	/*2511*/ uint16(xMatch),
	/*2512*/ uint16(xCondPrefix), 1,
	0x66, 2516,
	/*2516*/ uint16(xSetOp), uint16(BLENDVPS),
	/*2518*/ uint16(xReadSlashR),
	/*2519*/ uint16(xArgXmm1),
	/*2520*/ uint16(xArgXmm2M128),
	/*2521*/ uint16(xArgXMM0),
	/*2522*/ uint16(xMatch),
	/*2523*/ uint16(xCondPrefix), 1,
	0x66, 2527,
	/*2527*/ uint16(xSetOp), uint16(BLENDVPD),
	/*2529*/ uint16(xReadSlashR),
	/*2530*/ uint16(xArgXmm1),
	/*2531*/ uint16(xArgXmm2M128),
	/*2532*/ uint16(xArgXMM0),
	/*2533*/ uint16(xMatch),
	/*2534*/ uint16(xCondPrefix), 1,
	0x66, 2538,
	/*2538*/ uint16(xSetOp), uint16(PTEST),
	/*2540*/ uint16(xReadSlashR),
	/*2541*/ uint16(xArgXmm1),
	/*2542*/ uint16(xArgXmm2M128),
	/*2543*/ uint16(xMatch),
	/*2544*/ uint16(xCondPrefix), 2,
	0x66, 2556,
	0x0, 2550,
	/*2550*/ uint16(xSetOp), uint16(PABSB),
	/*2552*/ uint16(xReadSlashR),
	/*2553*/ uint16(xArgMm1),
	/*2554*/ uint16(xArgMm2M64),
	/*2555*/ uint16(xMatch),
	/*2556*/ uint16(xSetOp), uint16(PABSB),
	/*2558*/ uint16(xReadSlashR),
	/*2559*/ uint16(xArgXmm1),
	/*2560*/ uint16(xArgXmm2M128),
	/*2561*/ uint16(xMatch),
	/*2562*/ uint16(xCondPrefix), 2,
	0x66, 2574,
	0x0, 2568,
	/*2568*/ uint16(xSetOp), uint16(PABSW),
	/*2570*/ uint16(xReadSlashR),
	/*2571*/ uint16(xArgMm1),
	/*2572*/ uint16(xArgMm2M64),
	/*2573*/ uint16(xMatch),
	/*2574*/ uint16(xSetOp), uint16(PABSW),
	/*2576*/ uint16(xReadSlashR),
	/*2577*/ uint16(xArgXmm1),
	/*2578*/ uint16(xArgXmm2M128),
	/*2579*/ uint16(xMatch),
	/*2580*/ uint16(xCondPrefix), 2,
	0x66, 2592,
	0x0, 2586,
	/*2586*/ uint16(xSetOp), uint16(PABSD),
	/*2588*/ uint16(xReadSlashR),
	/*2589*/ uint16(xArgMm1),
	/*2590*/ uint16(xArgMm2M64),
	/*2591*/ uint16(xMatch),
	/*2592*/ uint16(xSetOp), uint16(PABSD),
	/*2594*/ uint16(xReadSlashR),
	/*2595*/ uint16(xArgXmm1),
	/*2596*/ uint16(xArgXmm2M128),
	/*2597*/ uint16(xMatch),
	/*2598*/ uint16(xCondPrefix), 1,
	0x66, 2602,
	/*2602*/ uint16(xSetOp), uint16(PMOVSXBW),
	/*2604*/ uint16(xReadSlashR),
	/*2605*/ uint16(xArgXmm1),
	/*2606*/ uint16(xArgXmm2M64),
	/*2607*/ uint16(xMatch),
	/*2608*/ uint16(xCondPrefix), 1,
	0x66, 2612,
	/*2612*/ uint16(xSetOp), uint16(PMOVSXBD),
	/*2614*/ uint16(xReadSlashR),
	/*2615*/ uint16(xArgXmm1),
	/*2616*/ uint16(xArgXmm2M32),
	/*2617*/ uint16(xMatch),
	/*2618*/ uint16(xCondPrefix), 1,
	0x66, 2622,
	/*2622*/ uint16(xSetOp), uint16(PMOVSXBQ),
	/*2624*/ uint16(xReadSlashR),
	/*2625*/ uint16(xArgXmm1),
	/*2626*/ uint16(xArgXmm2M16),
	/*2627*/ uint16(xMatch),
	/*2628*/ uint16(xCondPrefix), 1,
	0x66, 2632,
	/*2632*/ uint16(xSetOp), uint16(PMOVSXWD),
	/*2634*/ uint16(xReadSlashR),
	/*2635*/ uint16(xArgXmm1),
	/*2636*/ uint16(xArgXmm2M64),
	/*2637*/ uint16(xMatch),
	/*2638*/ uint16(xCondPrefix), 1,
	0x66, 2642,
	/*2642*/ uint16(xSetOp), uint16(PMOVSXWQ),
	/*2644*/ uint16(xReadSlashR),
	/*2645*/ uint16(xArgXmm1),
	/*2646*/ uint16(xArgXmm2M32),
	/*2647*/ uint16(xMatch),
	/*2648*/ uint16(xCondPrefix), 1,
	0x66, 2652,
	/*2652*/ uint16(xSetOp), uint16(PMOVSXDQ),
	/*2654*/ uint16(xReadSlashR),
	/*2655*/ uint16(xArgXmm1),
	/*2656*/ uint16(xArgXmm2M64),
	/*2657*/ uint16(xMatch),
	/*2658*/ uint16(xCondPrefix), 1,
	0x66, 2662,
	/*2662*/ uint16(xSetOp), uint16(PMULDQ),
	/*2664*/ uint16(xReadSlashR),
	/*2665*/ uint16(xArgXmm1),
	/*2666*/ uint16(xArgXmm2M128),
	/*2667*/ uint16(xMatch),
	/*2668*/ uint16(xCondPrefix), 1,
	0x66, 2672,
	/*2672*/ uint16(xSetOp), uint16(PCMPEQQ),
	/*2674*/ uint16(xReadSlashR),
	/*2675*/ uint16(xArgXmm1),
	/*2676*/ uint16(xArgXmm2M128),
	/*2677*/ uint16(xMatch),
	/*2678*/ uint16(xCondPrefix), 1,
	0x66, 2682,
	/*2682*/ uint16(xSetOp), uint16(MOVNTDQA),
	/*2684*/ uint16(xReadSlashR),
	/*2685*/ uint16(xArgXmm1),
	/*2686*/ uint16(xArgM128),
	/*2687*/ uint16(xMatch),
	/*2688*/ uint16(xCondPrefix), 1,
	0x66, 2692,
	/*2692*/ uint16(xSetOp), uint16(PACKUSDW),
	/*2694*/ uint16(xReadSlashR),
	/*2695*/ uint16(xArgXmm1),
	/*2696*/ uint16(xArgXmm2M128),
	/*2697*/ uint16(xMatch),
	/*2698*/ uint16(xCondPrefix), 1,
	0x66, 2702,
	/*2702*/ uint16(xSetOp), uint16(PMOVZXBW),
	/*2704*/ uint16(xReadSlashR),
	/*2705*/ uint16(xArgXmm1),
	/*2706*/ uint16(xArgXmm2M64),
	/*2707*/ uint16(xMatch),
	/*2708*/ uint16(xCondPrefix), 1,
	0x66, 2712,
	/*2712*/ uint16(xSetOp), uint16(PMOVZXBD),
	/*2714*/ uint16(xReadSlashR),
	/*2715*/ uint16(xArgXmm1),
	/*2716*/ uint16(xArgXmm2M32),
	/*2717*/ uint16(xMatch),
	/*2718*/ uint16(xCondPrefix), 1,
	0x66, 2722,
	/*2722*/ uint16(xSetOp), uint16(PMOVZXBQ),
	/*2724*/ uint16(xReadSlashR),
	/*2725*/ uint16(xArgXmm1),
	/*2726*/ uint16(xArgXmm2M16),
	/*2727*/ uint16(xMatch),
	/*2728*/ uint16(xCondPrefix), 1,
	0x66, 2732,
	/*2732*/ uint16(xSetOp), uint16(PMOVZXWD),
	/*2734*/ uint16(xReadSlashR),
	/*2735*/ uint16(xArgXmm1),
	/*2736*/ uint16(xArgXmm2M64),
	/*2737*/ uint16(xMatch),
	/*2738*/ uint16(xCondPrefix), 1,
	0x66, 2742,
	/*2742*/ uint16(xSetOp), uint16(PMOVZXWQ),
	/*2744*/ uint16(xReadSlashR),
	/*2745*/ uint16(xArgXmm1),
	/*2746*/ uint16(xArgXmm2M32),
	/*2747*/ uint16(xMatch),
	/*2748*/ uint16(xCondPrefix), 1,
	0x66, 2752,
	/*2752*/ uint16(xSetOp), uint16(PMOVZXDQ),
	/*2754*/ uint16(xReadSlashR),
	/*2755*/ uint16(xArgXmm1),
	/*2756*/ uint16(xArgXmm2M64),
	/*2757*/ uint16(xMatch),
	/*2758*/ uint16(xCondPrefix), 1,
	0x66, 2762,
	/*2762*/ uint16(xSetOp), uint16(PCMPGTQ),
	/*2764*/ uint16(xReadSlashR),
	/*2765*/ uint16(xArgXmm1),
	/*2766*/ uint16(xArgXmm2M128),
	/*2767*/ uint16(xMatch),
	/*2768*/ uint16(xCondPrefix), 1,
	0x66, 2772,
	/*2772*/ uint16(xSetOp), uint16(PMINSB),
	/*2774*/ uint16(xReadSlashR),
	/*2775*/ uint16(xArgXmm1),
	/*2776*/ uint16(xArgXmm2M128),
	/*2777*/ uint16(xMatch),
	/*2778*/ uint16(xCondPrefix), 1,
	0x66, 2782,
	/*2782*/ uint16(xSetOp), uint16(PMINSD),
	/*2784*/ uint16(xReadSlashR),
	/*2785*/ uint16(xArgXmm1),
	/*2786*/ uint16(xArgXmm2M128),
	/*2787*/ uint16(xMatch),
	/*2788*/ uint16(xCondPrefix), 1,
	0x66, 2792,
	/*2792*/ uint16(xSetOp), uint16(PMINUW),
	/*2794*/ uint16(xReadSlashR),
	/*2795*/ uint16(xArgXmm1),
	/*2796*/ uint16(xArgXmm2M128),
	/*2797*/ uint16(xMatch),
	/*2798*/ uint16(xCondPrefix), 1,
	0x66, 2802,
	/*2802*/ uint16(xSetOp), uint16(PMINUD),
	/*2804*/ uint16(xReadSlashR),
	/*2805*/ uint16(xArgXmm1),
	/*2806*/ uint16(xArgXmm2M128),
	/*2807*/ uint16(xMatch),
	/*2808*/ uint16(xCondPrefix), 1,
	0x66, 2812,
	/*2812*/ uint16(xSetOp), uint16(PMAXSB),
	/*2814*/ uint16(xReadSlashR),
	/*2815*/ uint16(xArgXmm1),
	/*2816*/ uint16(xArgXmm2M128),
	/*2817*/ uint16(xMatch),
	/*2818*/ uint16(xCondPrefix), 1,
	0x66, 2822,
	/*2822*/ uint16(xSetOp), uint16(PMAXSD),
	/*2824*/ uint16(xReadSlashR),
	/*2825*/ uint16(xArgXmm1),
	/*2826*/ uint16(xArgXmm2M128),
	/*2827*/ uint16(xMatch),
	/*2828*/ uint16(xCondPrefix), 1,
	0x66, 2832,
	/*2832*/ uint16(xSetOp), uint16(PMAXUW),
	/*2834*/ uint16(xReadSlashR),
	/*2835*/ uint16(xArgXmm1),
	/*2836*/ uint16(xArgXmm2M128),
	/*2837*/ uint16(xMatch),
	/*2838*/ uint16(xCondPrefix), 1,
	0x66, 2842,
	/*2842*/ uint16(xSetOp), uint16(PMAXUD),
	/*2844*/ uint16(xReadSlashR),
	/*2845*/ uint16(xArgXmm1),
	/*2846*/ uint16(xArgXmm2M128),
	/*2847*/ uint16(xMatch),
	/*2848*/ uint16(xCondPrefix), 1,
	0x66, 2852,
	/*2852*/ uint16(xSetOp), uint16(PMULLD),
	/*2854*/ uint16(xReadSlashR),
	/*2855*/ uint16(xArgXmm1),
	/*2856*/ uint16(xArgXmm2M128),
	/*2857*/ uint16(xMatch),
	/*2858*/ uint16(xCondPrefix), 1,
	0x66, 2862,
	/*2862*/ uint16(xSetOp), uint16(PHMINPOSUW),
	/*2864*/ uint16(xReadSlashR),
	/*2865*/ uint16(xArgXmm1),
	/*2866*/ uint16(xArgXmm2M128),
	/*2867*/ uint16(xMatch),
	/*2868*/ uint16(xCondIs64), 2871, 2881,
	/*2871*/ uint16(xCondPrefix), 1,
	0x66, 2875,
	/*2875*/ uint16(xSetOp), uint16(INVPCID),
	/*2877*/ uint16(xReadSlashR),
	/*2878*/ uint16(xArgR32),
	/*2879*/ uint16(xArgM128),
	/*2880*/ uint16(xMatch),
	/*2881*/ uint16(xCondPrefix), 1,
	0x66, 2885,
	/*2885*/ uint16(xSetOp), uint16(INVPCID),
	/*2887*/ uint16(xReadSlashR),
	/*2888*/ uint16(xArgR64),
	/*2889*/ uint16(xArgM128),
	/*2890*/ uint16(xMatch),
	/*2891*/ uint16(xCondPrefix), 1,
	0x66, 2895,
	/*2895*/ uint16(xSetOp), uint16(AESIMC),
	/*2897*/ uint16(xReadSlashR),
	/*2898*/ uint16(xArgXmm1),
	/*2899*/ uint16(xArgXmm2M128),
	/*2900*/ uint16(xMatch),
	/*2901*/ uint16(xCondPrefix), 1,
	0x66, 2905,
	/*2905*/ uint16(xSetOp), uint16(AESENC),
	/*2907*/ uint16(xReadSlashR),
	/*2908*/ uint16(xArgXmm1),
	/*2909*/ uint16(xArgXmm2M128),
	/*2910*/ uint16(xMatch),
	/*2911*/ uint16(xCondPrefix), 1,
	0x66, 2915,
	/*2915*/ uint16(xSetOp), uint16(AESENCLAST),
	/*2917*/ uint16(xReadSlashR),
	/*2918*/ uint16(xArgXmm1),
	/*2919*/ uint16(xArgXmm2M128),
	/*2920*/ uint16(xMatch),
	/*2921*/ uint16(xCondPrefix), 1,
	0x66, 2925,
	/*2925*/ uint16(xSetOp), uint16(AESDEC),
	/*2927*/ uint16(xReadSlashR),
	/*2928*/ uint16(xArgXmm1),
	/*2929*/ uint16(xArgXmm2M128),
	/*2930*/ uint16(xMatch),
	/*2931*/ uint16(xCondPrefix), 1,
	0x66, 2935,
	/*2935*/ uint16(xSetOp), uint16(AESDECLAST),
	/*2937*/ uint16(xReadSlashR),
	/*2938*/ uint16(xArgXmm1),
	/*2939*/ uint16(xArgXmm2M128),
	/*2940*/ uint16(xMatch),
	/*2941*/ uint16(xCondIs64), 2944, 2982,
	/*2944*/ uint16(xCondPrefix), 2,
	0xF2, 2966,
	0x0, 2950,
	/*2950*/ uint16(xCondDataSize), 2954, 2960, 0,
	/*2954*/ uint16(xSetOp), uint16(MOVBE),
	/*2956*/ uint16(xReadSlashR),
	/*2957*/ uint16(xArgR16),
	/*2958*/ uint16(xArgM16),
	/*2959*/ uint16(xMatch),
	/*2960*/ uint16(xSetOp), uint16(MOVBE),
	/*2962*/ uint16(xReadSlashR),
	/*2963*/ uint16(xArgR32),
	/*2964*/ uint16(xArgM32),
	/*2965*/ uint16(xMatch),
	/*2966*/ uint16(xCondDataSize), 2970, 2976, 0,
	/*2970*/ uint16(xSetOp), uint16(CRC32),
	/*2972*/ uint16(xReadSlashR),
	/*2973*/ uint16(xArgR32),
	/*2974*/ uint16(xArgRM8),
	/*2975*/ uint16(xMatch),
	/*2976*/ uint16(xSetOp), uint16(CRC32),
	/*2978*/ uint16(xReadSlashR),
	/*2979*/ uint16(xArgR32),
	/*2980*/ uint16(xArgRM8),
	/*2981*/ uint16(xMatch),
	/*2982*/ uint16(xCondPrefix), 2,
	0xF2, 2998,
	0x0, 2988,
	/*2988*/ uint16(xCondDataSize), 2954, 2960, 2992,
	/*2992*/ uint16(xSetOp), uint16(MOVBE),
	/*2994*/ uint16(xReadSlashR),
	/*2995*/ uint16(xArgR64),
	/*2996*/ uint16(xArgM64),
	/*2997*/ uint16(xMatch),
	/*2998*/ uint16(xCondDataSize), 2970, 2976, 3002,
	/*3002*/ uint16(xSetOp), uint16(CRC32),
	/*3004*/ uint16(xReadSlashR),
	/*3005*/ uint16(xArgR64),
	/*3006*/ uint16(xArgRM8),
	/*3007*/ uint16(xMatch),
	/*3008*/ uint16(xCondIs64), 3011, 3049,
	/*3011*/ uint16(xCondPrefix), 2,
	0xF2, 3033,
	0x0, 3017,
	/*3017*/ uint16(xCondDataSize), 3021, 3027, 0,
	/*3021*/ uint16(xSetOp), uint16(MOVBE),
	/*3023*/ uint16(xReadSlashR),
	/*3024*/ uint16(xArgM16),
	/*3025*/ uint16(xArgR16),
	/*3026*/ uint16(xMatch),
	/*3027*/ uint16(xSetOp), uint16(MOVBE),
	/*3029*/ uint16(xReadSlashR),
	/*3030*/ uint16(xArgM32),
	/*3031*/ uint16(xArgR32),
	/*3032*/ uint16(xMatch),
	/*3033*/ uint16(xCondDataSize), 3037, 3043, 0,
	/*3037*/ uint16(xSetOp), uint16(CRC32),
	/*3039*/ uint16(xReadSlashR),
	/*3040*/ uint16(xArgR32),
	/*3041*/ uint16(xArgRM16),
	/*3042*/ uint16(xMatch),
	/*3043*/ uint16(xSetOp), uint16(CRC32),
	/*3045*/ uint16(xReadSlashR),
	/*3046*/ uint16(xArgR32),
	/*3047*/ uint16(xArgRM32),
	/*3048*/ uint16(xMatch),
	/*3049*/ uint16(xCondPrefix), 2,
	0xF2, 3065,
	0x0, 3055,
	/*3055*/ uint16(xCondDataSize), 3021, 3027, 3059,
	/*3059*/ uint16(xSetOp), uint16(MOVBE),
	/*3061*/ uint16(xReadSlashR),
	/*3062*/ uint16(xArgM64),
	/*3063*/ uint16(xArgR64),
	/*3064*/ uint16(xMatch),
	/*3065*/ uint16(xCondDataSize), 3037, 3043, 3069,
	/*3069*/ uint16(xSetOp), uint16(CRC32),
	/*3071*/ uint16(xReadSlashR),
	/*3072*/ uint16(xArgR64),
	/*3073*/ uint16(xArgRM64),
	/*3074*/ uint16(xMatch),
	/*3075*/ uint16(xCondByte), 24,
	0x08, 3126,
	0x09, 3138,
	0x0A, 3150,
	0x0B, 3162,
	0x0C, 3174,
	0x0D, 3186,
	0x0E, 3198,
	0x0F, 3210,
	0x14, 3232,
	0x15, 3244,
	0x16, 3256,
	0x17, 3299,
	0x20, 3311,
	0x21, 3323,
	0x22, 3335,
	0x40, 3378,
	0x41, 3390,
	0x42, 3402,
	0x44, 3414,
	0x60, 3426,
	0x61, 3438,
	0x62, 3450,
	0x63, 3462,
	0xDF, 3474,
	uint16(xFail),
	/*3126*/ uint16(xCondPrefix), 1,
	0x66, 3130,
	/*3130*/ uint16(xSetOp), uint16(ROUNDPS),
	/*3132*/ uint16(xReadSlashR),
	/*3133*/ uint16(xReadIb),
	/*3134*/ uint16(xArgXmm1),
	/*3135*/ uint16(xArgXmm2M128),
	/*3136*/ uint16(xArgImm8u),
	/*3137*/ uint16(xMatch),
	/*3138*/ uint16(xCondPrefix), 1,
	0x66, 3142,
	/*3142*/ uint16(xSetOp), uint16(ROUNDPD),
	/*3144*/ uint16(xReadSlashR),
	/*3145*/ uint16(xReadIb),
	/*3146*/ uint16(xArgXmm1),
	/*3147*/ uint16(xArgXmm2M128),
	/*3148*/ uint16(xArgImm8u),
	/*3149*/ uint16(xMatch),
	/*3150*/ uint16(xCondPrefix), 1,
	0x66, 3154,
	/*3154*/ uint16(xSetOp), uint16(ROUNDSS),
	/*3156*/ uint16(xReadSlashR),
	/*3157*/ uint16(xReadIb),
	/*3158*/ uint16(xArgXmm1),
	/*3159*/ uint16(xArgXmm2M32),
	/*3160*/ uint16(xArgImm8u),
	/*3161*/ uint16(xMatch),
	/*3162*/ uint16(xCondPrefix), 1,
	0x66, 3166,
	/*3166*/ uint16(xSetOp), uint16(ROUNDSD),
	/*3168*/ uint16(xReadSlashR),
	/*3169*/ uint16(xReadIb),
	/*3170*/ uint16(xArgXmm1),
	/*3171*/ uint16(xArgXmm2M64),
	/*3172*/ uint16(xArgImm8u),
	/*3173*/ uint16(xMatch),
	/*3174*/ uint16(xCondPrefix), 1,
	0x66, 3178,
	/*3178*/ uint16(xSetOp), uint16(BLENDPS),
	/*3180*/ uint16(xReadSlashR),
	/*3181*/ uint16(xReadIb),
	/*3182*/ uint16(xArgXmm1),
	/*3183*/ uint16(xArgXmm2M128),
	/*3184*/ uint16(xArgImm8u),
	/*3185*/ uint16(xMatch),
	/*3186*/ uint16(xCondPrefix), 1,
	0x66, 3190,
	/*3190*/ uint16(xSetOp), uint16(BLENDPD),
	/*3192*/ uint16(xReadSlashR),
	/*3193*/ uint16(xReadIb),
	/*3194*/ uint16(xArgXmm1),
	/*3195*/ uint16(xArgXmm2M128),
	/*3196*/ uint16(xArgImm8u),
	/*3197*/ uint16(xMatch),
	/*3198*/ uint16(xCondPrefix), 1,
	0x66, 3202,
	/*3202*/ uint16(xSetOp), uint16(PBLENDW),
	/*3204*/ uint16(xReadSlashR),
	/*3205*/ uint16(xReadIb),
	/*3206*/ uint16(xArgXmm1),
	/*3207*/ uint16(xArgXmm2M128),
	/*3208*/ uint16(xArgImm8u),
	/*3209*/ uint16(xMatch),
	/*3210*/ uint16(xCondPrefix), 2,
	0x66, 3224,
	0x0, 3216,
	/*3216*/ uint16(xSetOp), uint16(PALIGNR),
	/*3218*/ uint16(xReadSlashR),
	/*3219*/ uint16(xReadIb),
	/*3220*/ uint16(xArgMm1),
	/*3221*/ uint16(xArgMm2M64),
	/*3222*/ uint16(xArgImm8u),
	/*3223*/ uint16(xMatch),
	/*3224*/ uint16(xSetOp), uint16(PALIGNR),
	/*3226*/ uint16(xReadSlashR),
	/*3227*/ uint16(xReadIb),
	/*3228*/ uint16(xArgXmm1),
	/*3229*/ uint16(xArgXmm2M128),
	/*3230*/ uint16(xArgImm8u),
	/*3231*/ uint16(xMatch),
	/*3232*/ uint16(xCondPrefix), 1,
	0x66, 3236,
	/*3236*/ uint16(xSetOp), uint16(PEXTRB),
	/*3238*/ uint16(xReadSlashR),
	/*3239*/ uint16(xReadIb),
	/*3240*/ uint16(xArgR32M8),
	/*3241*/ uint16(xArgXmm1),
	/*3242*/ uint16(xArgImm8u),
	/*3243*/ uint16(xMatch),
	/*3244*/ uint16(xCondPrefix), 1,
	0x66, 3248,
	/*3248*/ uint16(xSetOp), uint16(PEXTRW),
	/*3250*/ uint16(xReadSlashR),
	/*3251*/ uint16(xReadIb),
	/*3252*/ uint16(xArgR32M16),
	/*3253*/ uint16(xArgXmm1),
	/*3254*/ uint16(xArgImm8u),
	/*3255*/ uint16(xMatch),
	/*3256*/ uint16(xCondIs64), 3259, 3283,
	/*3259*/ uint16(xCondPrefix), 1,
	0x66, 3263,
	/*3263*/ uint16(xCondDataSize), 3267, 3275, 0,
	/*3267*/ uint16(xSetOp), uint16(PEXTRD),
	/*3269*/ uint16(xReadSlashR),
	/*3270*/ uint16(xReadIb),
	/*3271*/ uint16(xArgRM32),
	/*3272*/ uint16(xArgXmm1),
	/*3273*/ uint16(xArgImm8u),
	/*3274*/ uint16(xMatch),
	/*3275*/ uint16(xSetOp), uint16(PEXTRD),
	/*3277*/ uint16(xReadSlashR),
	/*3278*/ uint16(xReadIb),
	/*3279*/ uint16(xArgRM32),
	/*3280*/ uint16(xArgXmm1),
	/*3281*/ uint16(xArgImm8u),
	/*3282*/ uint16(xMatch),
	/*3283*/ uint16(xCondPrefix), 1,
	0x66, 3287,
	/*3287*/ uint16(xCondDataSize), 3267, 3275, 3291,
	/*3291*/ uint16(xSetOp), uint16(PEXTRQ),
	/*3293*/ uint16(xReadSlashR),
	/*3294*/ uint16(xReadIb),
	/*3295*/ uint16(xArgRM64),
	/*3296*/ uint16(xArgXmm1),
	/*3297*/ uint16(xArgImm8u),
	/*3298*/ uint16(xMatch),
	/*3299*/ uint16(xCondPrefix), 1,
	0x66, 3303,
	/*3303*/ uint16(xSetOp), uint16(EXTRACTPS),
	/*3305*/ uint16(xReadSlashR),
	/*3306*/ uint16(xReadIb),
	/*3307*/ uint16(xArgRM32),
	/*3308*/ uint16(xArgXmm1),
	/*3309*/ uint16(xArgImm8u),
	/*3310*/ uint16(xMatch),
	/*3311*/ uint16(xCondPrefix), 1,
	0x66, 3315,
	/*3315*/ uint16(xSetOp), uint16(PINSRB),
	/*3317*/ uint16(xReadSlashR),
	/*3318*/ uint16(xReadIb),
	/*3319*/ uint16(xArgXmm1),
	/*3320*/ uint16(xArgR32M8),
	/*3321*/ uint16(xArgImm8u),
	/*3322*/ uint16(xMatch),
	/*3323*/ uint16(xCondPrefix), 1,
	0x66, 3327,
	/*3327*/ uint16(xSetOp), uint16(INSERTPS),
	/*3329*/ uint16(xReadSlashR),
	/*3330*/ uint16(xReadIb),
	/*3331*/ uint16(xArgXmm1),
	/*3332*/ uint16(xArgXmm2M32),
	/*3333*/ uint16(xArgImm8u),
	/*3334*/ uint16(xMatch),
	/*3335*/ uint16(xCondIs64), 3338, 3362,
	/*3338*/ uint16(xCondPrefix), 1,
	0x66, 3342,
	/*3342*/ uint16(xCondDataSize), 3346, 3354, 0,
	/*3346*/ uint16(xSetOp), uint16(PINSRD),
	/*3348*/ uint16(xReadSlashR),
	/*3349*/ uint16(xReadIb),
	/*3350*/ uint16(xArgXmm1),
	/*3351*/ uint16(xArgRM32),
	/*3352*/ uint16(xArgImm8u),
	/*3353*/ uint16(xMatch),
	/*3354*/ uint16(xSetOp), uint16(PINSRD),
	/*3356*/ uint16(xReadSlashR),
	/*3357*/ uint16(xReadIb),
	/*3358*/ uint16(xArgXmm1),
	/*3359*/ uint16(xArgRM32),
	/*3360*/ uint16(xArgImm8u),
	/*3361*/ uint16(xMatch),
	/*3362*/ uint16(xCondPrefix), 1,
	0x66, 3366,
	/*3366*/ uint16(xCondDataSize), 3346, 3354, 3370,
	/*3370*/ uint16(xSetOp), uint16(PINSRQ),
	/*3372*/ uint16(xReadSlashR),
	/*3373*/ uint16(xReadIb),
	/*3374*/ uint16(xArgXmm1),
	/*3375*/ uint16(xArgRM64),
	/*3376*/ uint16(xArgImm8u),
	/*3377*/ uint16(xMatch),
	/*3378*/ uint16(xCondPrefix), 1,
	0x66, 3382,
	/*3382*/ uint16(xSetOp), uint16(DPPS),
	/*3384*/ uint16(xReadSlashR),
	/*3385*/ uint16(xReadIb),
	/*3386*/ uint16(xArgXmm1),
	/*3387*/ uint16(xArgXmm2M128),
	/*3388*/ uint16(xArgImm8u),
	/*3389*/ uint16(xMatch),
	/*3390*/ uint16(xCondPrefix), 1,
	0x66, 3394,
	/*3394*/ uint16(xSetOp), uint16(DPPD),
	/*3396*/ uint16(xReadSlashR),
	/*3397*/ uint16(xReadIb),
	/*3398*/ uint16(xArgXmm1),
	/*3399*/ uint16(xArgXmm2M128),
	/*3400*/ uint16(xArgImm8u),
	/*3401*/ uint16(xMatch),
	/*3402*/ uint16(xCondPrefix), 1,
	0x66, 3406,
	/*3406*/ uint16(xSetOp), uint16(MPSADBW),
	/*3408*/ uint16(xReadSlashR),
	/*3409*/ uint16(xReadIb),
	/*3410*/ uint16(xArgXmm1),
	/*3411*/ uint16(xArgXmm2M128),
	/*3412*/ uint16(xArgImm8u),
	/*3413*/ uint16(xMatch),
	/*3414*/ uint16(xCondPrefix), 1,
	0x66, 3418,
	/*3418*/ uint16(xSetOp), uint16(PCLMULQDQ),
	/*3420*/ uint16(xReadSlashR),
	/*3421*/ uint16(xReadIb),
	/*3422*/ uint16(xArgXmm1),
	/*3423*/ uint16(xArgXmm2M128),
	/*3424*/ uint16(xArgImm8u),
	/*3425*/ uint16(xMatch),
	/*3426*/ uint16(xCondPrefix), 1,
	0x66, 3430,
	/*3430*/ uint16(xSetOp), uint16(PCMPESTRM),
	/*3432*/ uint16(xReadSlashR),
	/*3433*/ uint16(xReadIb),
	/*3434*/ uint16(xArgXmm1),
	/*3435*/ uint16(xArgXmm2M128),
	/*3436*/ uint16(xArgImm8u),
	/*3437*/ uint16(xMatch),
	/*3438*/ uint16(xCondPrefix), 1,
	0x66, 3442,
	/*3442*/ uint16(xSetOp), uint16(PCMPESTRI),
	/*3444*/ uint16(xReadSlashR),
	/*3445*/ uint16(xReadIb),
	/*3446*/ uint16(xArgXmm1),
	/*3447*/ uint16(xArgXmm2M128),
	/*3448*/ uint16(xArgImm8u),
	/*3449*/ uint16(xMatch),
	/*3450*/ uint16(xCondPrefix), 1,
	0x66, 3454,
	/*3454*/ uint16(xSetOp), uint16(PCMPISTRM),
	/*3456*/ uint16(xReadSlashR),
	/*3457*/ uint16(xReadIb),
	/*3458*/ uint16(xArgXmm1),
	/*3459*/ uint16(xArgXmm2M128),
	/*3460*/ uint16(xArgImm8u),
	/*3461*/ uint16(xMatch),
	/*3462*/ uint16(xCondPrefix), 1,
	0x66, 3466,
	/*3466*/ uint16(xSetOp), uint16(PCMPISTRI),
	/*3468*/ uint16(xReadSlashR),
	/*3469*/ uint16(xReadIb),
	/*3470*/ uint16(xArgXmm1),
	/*3471*/ uint16(xArgXmm2M128),
	/*3472*/ uint16(xArgImm8u),
	/*3473*/ uint16(xMatch),
	/*3474*/ uint16(xCondPrefix), 1,
	0x66, 3478,
	/*3478*/ uint16(xSetOp), uint16(AESKEYGENASSIST),
	/*3480*/ uint16(xReadSlashR),
	/*3481*/ uint16(xReadIb),
	/*3482*/ uint16(xArgXmm1),
	/*3483*/ uint16(xArgXmm2M128),
	/*3484*/ uint16(xArgImm8u),
	/*3485*/ uint16(xMatch),
	/*3486*/ uint16(xCondIs64), 3489, 3505,
	/*3489*/ uint16(xCondDataSize), 3493, 3499, 0,
	/*3493*/ uint16(xSetOp), uint16(CMOVO),
	/*3495*/ uint16(xReadSlashR),
	/*3496*/ uint16(xArgR16),
	/*3497*/ uint16(xArgRM16),
	/*3498*/ uint16(xMatch),
	/*3499*/ uint16(xSetOp), uint16(CMOVO),
	/*3501*/ uint16(xReadSlashR),
	/*3502*/ uint16(xArgR32),
	/*3503*/ uint16(xArgRM32),
	/*3504*/ uint16(xMatch),
	/*3505*/ uint16(xCondDataSize), 3493, 3499, 3509,
	/*3509*/ uint16(xSetOp), uint16(CMOVO),
	/*3511*/ uint16(xReadSlashR),
	/*3512*/ uint16(xArgR64),
	/*3513*/ uint16(xArgRM64),
	/*3514*/ uint16(xMatch),
	/*3515*/ uint16(xCondIs64), 3518, 3534,
	/*3518*/ uint16(xCondDataSize), 3522, 3528, 0,
	/*3522*/ uint16(xSetOp), uint16(CMOVNO),
	/*3524*/ uint16(xReadSlashR),
	/*3525*/ uint16(xArgR16),
	/*3526*/ uint16(xArgRM16),
	/*3527*/ uint16(xMatch),
	/*3528*/ uint16(xSetOp), uint16(CMOVNO),
	/*3530*/ uint16(xReadSlashR),
	/*3531*/ uint16(xArgR32),
	/*3532*/ uint16(xArgRM32),
	/*3533*/ uint16(xMatch),
	/*3534*/ uint16(xCondDataSize), 3522, 3528, 3538,
	/*3538*/ uint16(xSetOp), uint16(CMOVNO),
	/*3540*/ uint16(xReadSlashR),
	/*3541*/ uint16(xArgR64),
	/*3542*/ uint16(xArgRM64),
	/*3543*/ uint16(xMatch),
	/*3544*/ uint16(xCondIs64), 3547, 3563,
	/*3547*/ uint16(xCondDataSize), 3551, 3557, 0,
	/*3551*/ uint16(xSetOp), uint16(CMOVB),
	/*3553*/ uint16(xReadSlashR),
	/*3554*/ uint16(xArgR16),
	/*3555*/ uint16(xArgRM16),
	/*3556*/ uint16(xMatch),
	/*3557*/ uint16(xSetOp), uint16(CMOVB),
	/*3559*/ uint16(xReadSlashR),
	/*3560*/ uint16(xArgR32),
	/*3561*/ uint16(xArgRM32),
	/*3562*/ uint16(xMatch),
	/*3563*/ uint16(xCondDataSize), 3551, 3557, 3567,
	/*3567*/ uint16(xSetOp), uint16(CMOVB),
	/*3569*/ uint16(xReadSlashR),
	/*3570*/ uint16(xArgR64),
	/*3571*/ uint16(xArgRM64),
	/*3572*/ uint16(xMatch),
	/*3573*/ uint16(xCondIs64), 3576, 3592,
	/*3576*/ uint16(xCondDataSize), 3580, 3586, 0,
	/*3580*/ uint16(xSetOp), uint16(CMOVAE),
	/*3582*/ uint16(xReadSlashR),
	/*3583*/ uint16(xArgR16),
	/*3584*/ uint16(xArgRM16),
	/*3585*/ uint16(xMatch),
	/*3586*/ uint16(xSetOp), uint16(CMOVAE),
	/*3588*/ uint16(xReadSlashR),
	/*3589*/ uint16(xArgR32),
	/*3590*/ uint16(xArgRM32),
	/*3591*/ uint16(xMatch),
	/*3592*/ uint16(xCondDataSize), 3580, 3586, 3596,
	/*3596*/ uint16(xSetOp), uint16(CMOVAE),
	/*3598*/ uint16(xReadSlashR),
	/*3599*/ uint16(xArgR64),
	/*3600*/ uint16(xArgRM64),
	/*3601*/ uint16(xMatch),
	/*3602*/ uint16(xCondIs64), 3605, 3621,
	/*3605*/ uint16(xCondDataSize), 3609, 3615, 0,
	/*3609*/ uint16(xSetOp), uint16(CMOVE),
	/*3611*/ uint16(xReadSlashR),
	/*3612*/ uint16(xArgR16),
	/*3613*/ uint16(xArgRM16),
	/*3614*/ uint16(xMatch),
	/*3615*/ uint16(xSetOp), uint16(CMOVE),
	/*3617*/ uint16(xReadSlashR),
	/*3618*/ uint16(xArgR32),
	/*3619*/ uint16(xArgRM32),
	/*3620*/ uint16(xMatch),
	/*3621*/ uint16(xCondDataSize), 3609, 3615, 3625,
	/*3625*/ uint16(xSetOp), uint16(CMOVE),
	/*3627*/ uint16(xReadSlashR),
	/*3628*/ uint16(xArgR64),
	/*3629*/ uint16(xArgRM64),
	/*3630*/ uint16(xMatch),
	/*3631*/ uint16(xCondIs64), 3634, 3650,
	/*3634*/ uint16(xCondDataSize), 3638, 3644, 0,
	/*3638*/ uint16(xSetOp), uint16(CMOVNE),
	/*3640*/ uint16(xReadSlashR),
	/*3641*/ uint16(xArgR16),
	/*3642*/ uint16(xArgRM16),
	/*3643*/ uint16(xMatch),
	/*3644*/ uint16(xSetOp), uint16(CMOVNE),
	/*3646*/ uint16(xReadSlashR),
	/*3647*/ uint16(xArgR32),
	/*3648*/ uint16(xArgRM32),
	/*3649*/ uint16(xMatch),
	/*3650*/ uint16(xCondDataSize), 3638, 3644, 3654,
	/*3654*/ uint16(xSetOp), uint16(CMOVNE),
	/*3656*/ uint16(xReadSlashR),
	/*3657*/ uint16(xArgR64),
	/*3658*/ uint16(xArgRM64),
	/*3659*/ uint16(xMatch),
	/*3660*/ uint16(xCondIs64), 3663, 3679,
	/*3663*/ uint16(xCondDataSize), 3667, 3673, 0,
	/*3667*/ uint16(xSetOp), uint16(CMOVBE),
	/*3669*/ uint16(xReadSlashR),
	/*3670*/ uint16(xArgR16),
	/*3671*/ uint16(xArgRM16),
	/*3672*/ uint16(xMatch),
	/*3673*/ uint16(xSetOp), uint16(CMOVBE),
	/*3675*/ uint16(xReadSlashR),
	/*3676*/ uint16(xArgR32),
	/*3677*/ uint16(xArgRM32),
	/*3678*/ uint16(xMatch),
	/*3679*/ uint16(xCondDataSize), 3667, 3673, 3683,
	/*3683*/ uint16(xSetOp), uint16(CMOVBE),
	/*3685*/ uint16(xReadSlashR),
	/*3686*/ uint16(xArgR64),
	/*3687*/ uint16(xArgRM64),
	/*3688*/ uint16(xMatch),
	/*3689*/ uint16(xCondIs64), 3692, 3708,
	/*3692*/ uint16(xCondDataSize), 3696, 3702, 0,
	/*3696*/ uint16(xSetOp), uint16(CMOVA),
	/*3698*/ uint16(xReadSlashR),
	/*3699*/ uint16(xArgR16),
	/*3700*/ uint16(xArgRM16),
	/*3701*/ uint16(xMatch),
	/*3702*/ uint16(xSetOp), uint16(CMOVA),
	/*3704*/ uint16(xReadSlashR),
	/*3705*/ uint16(xArgR32),
	/*3706*/ uint16(xArgRM32),
	/*3707*/ uint16(xMatch),
	/*3708*/ uint16(xCondDataSize), 3696, 3702, 3712,
	/*3712*/ uint16(xSetOp), uint16(CMOVA),
	/*3714*/ uint16(xReadSlashR),
	/*3715*/ uint16(xArgR64),
	/*3716*/ uint16(xArgRM64),
	/*3717*/ uint16(xMatch),
	/*3718*/ uint16(xCondIs64), 3721, 3737,
	/*3721*/ uint16(xCondDataSize), 3725, 3731, 0,
	/*3725*/ uint16(xSetOp), uint16(CMOVS),
	/*3727*/ uint16(xReadSlashR),
	/*3728*/ uint16(xArgR16),
	/*3729*/ uint16(xArgRM16),
	/*3730*/ uint16(xMatch),
	/*3731*/ uint16(xSetOp), uint16(CMOVS),
	/*3733*/ uint16(xReadSlashR),
	/*3734*/ uint16(xArgR32),
	/*3735*/ uint16(xArgRM32),
	/*3736*/ uint16(xMatch),
	/*3737*/ uint16(xCondDataSize), 3725, 3731, 3741,
	/*3741*/ uint16(xSetOp), uint16(CMOVS),
	/*3743*/ uint16(xReadSlashR),
	/*3744*/ uint16(xArgR64),
	/*3745*/ uint16(xArgRM64),
	/*3746*/ uint16(xMatch),
	/*3747*/ uint16(xCondIs64), 3750, 3766,
	/*3750*/ uint16(xCondDataSize), 3754, 3760, 0,
	/*3754*/ uint16(xSetOp), uint16(CMOVNS),
	/*3756*/ uint16(xReadSlashR),
	/*3757*/ uint16(xArgR16),
	/*3758*/ uint16(xArgRM16),
	/*3759*/ uint16(xMatch),
	/*3760*/ uint16(xSetOp), uint16(CMOVNS),
	/*3762*/ uint16(xReadSlashR),
	/*3763*/ uint16(xArgR32),
	/*3764*/ uint16(xArgRM32),
	/*3765*/ uint16(xMatch),
	/*3766*/ uint16(xCondDataSize), 3754, 3760, 3770,
	/*3770*/ uint16(xSetOp), uint16(CMOVNS),
	/*3772*/ uint16(xReadSlashR),
	/*3773*/ uint16(xArgR64),
	/*3774*/ uint16(xArgRM64),
	/*3775*/ uint16(xMatch),
	/*3776*/ uint16(xCondIs64), 3779, 3795,
	/*3779*/ uint16(xCondDataSize), 3783, 3789, 0,
	/*3783*/ uint16(xSetOp), uint16(CMOVP),
	/*3785*/ uint16(xReadSlashR),
	/*3786*/ uint16(xArgR16),
	/*3787*/ uint16(xArgRM16),
	/*3788*/ uint16(xMatch),
	/*3789*/ uint16(xSetOp), uint16(CMOVP),
	/*3791*/ uint16(xReadSlashR),
	/*3792*/ uint16(xArgR32),
	/*3793*/ uint16(xArgRM32),
	/*3794*/ uint16(xMatch),
	/*3795*/ uint16(xCondDataSize), 3783, 3789, 3799,
	/*3799*/ uint16(xSetOp), uint16(CMOVP),
	/*3801*/ uint16(xReadSlashR),
	/*3802*/ uint16(xArgR64),
	/*3803*/ uint16(xArgRM64),
	/*3804*/ uint16(xMatch),
	/*3805*/ uint16(xCondIs64), 3808, 3824,
	/*3808*/ uint16(xCondDataSize), 3812, 3818, 0,
	/*3812*/ uint16(xSetOp), uint16(CMOVNP),
	/*3814*/ uint16(xReadSlashR),
	/*3815*/ uint16(xArgR16),
	/*3816*/ uint16(xArgRM16),
	/*3817*/ uint16(xMatch),
	/*3818*/ uint16(xSetOp), uint16(CMOVNP),
	/*3820*/ uint16(xReadSlashR),
	/*3821*/ uint16(xArgR32),
	/*3822*/ uint16(xArgRM32),
	/*3823*/ uint16(xMatch),
	/*3824*/ uint16(xCondDataSize), 3812, 3818, 3828,
	/*3828*/ uint16(xSetOp), uint16(CMOVNP),
	/*3830*/ uint16(xReadSlashR),
	/*3831*/ uint16(xArgR64),
	/*3832*/ uint16(xArgRM64),
	/*3833*/ uint16(xMatch),
	/*3834*/ uint16(xCondIs64), 3837, 3853,
	/*3837*/ uint16(xCondDataSize), 3841, 3847, 0,
	/*3841*/ uint16(xSetOp), uint16(CMOVL),
	/*3843*/ uint16(xReadSlashR),
	/*3844*/ uint16(xArgR16),
	/*3845*/ uint16(xArgRM16),
	/*3846*/ uint16(xMatch),
	/*3847*/ uint16(xSetOp), uint16(CMOVL),
	/*3849*/ uint16(xReadSlashR),
	/*3850*/ uint16(xArgR32),
	/*3851*/ uint16(xArgRM32),
	/*3852*/ uint16(xMatch),
	/*3853*/ uint16(xCondDataSize), 3841, 3847, 3857,
	/*3857*/ uint16(xSetOp), uint16(CMOVL),
	/*3859*/ uint16(xReadSlashR),
	/*3860*/ uint16(xArgR64),
	/*3861*/ uint16(xArgRM64),
	/*3862*/ uint16(xMatch),
	/*3863*/ uint16(xCondIs64), 3866, 3882,
	/*3866*/ uint16(xCondDataSize), 3870, 3876, 0,
	/*3870*/ uint16(xSetOp), uint16(CMOVGE),
	/*3872*/ uint16(xReadSlashR),
	/*3873*/ uint16(xArgR16),
	/*3874*/ uint16(xArgRM16),
	/*3875*/ uint16(xMatch),
	/*3876*/ uint16(xSetOp), uint16(CMOVGE),
	/*3878*/ uint16(xReadSlashR),
	/*3879*/ uint16(xArgR32),
	/*3880*/ uint16(xArgRM32),
	/*3881*/ uint16(xMatch),
	/*3882*/ uint16(xCondDataSize), 3870, 3876, 3886,
	/*3886*/ uint16(xSetOp), uint16(CMOVGE),
	/*3888*/ uint16(xReadSlashR),
	/*3889*/ uint16(xArgR64),
	/*3890*/ uint16(xArgRM64),
	/*3891*/ uint16(xMatch),
	/*3892*/ uint16(xCondIs64), 3895, 3911,
	/*3895*/ uint16(xCondDataSize), 3899, 3905, 0,
	/*3899*/ uint16(xSetOp), uint16(CMOVLE),
	/*3901*/ uint16(xReadSlashR),
	/*3902*/ uint16(xArgR16),
	/*3903*/ uint16(xArgRM16),
	/*3904*/ uint16(xMatch),
	/*3905*/ uint16(xSetOp), uint16(CMOVLE),
	/*3907*/ uint16(xReadSlashR),
	/*3908*/ uint16(xArgR32),
	/*3909*/ uint16(xArgRM32),
	/*3910*/ uint16(xMatch),
	/*3911*/ uint16(xCondDataSize), 3899, 3905, 3915,
	/*3915*/ uint16(xSetOp), uint16(CMOVLE),
	/*3917*/ uint16(xReadSlashR),
	/*3918*/ uint16(xArgR64),
	/*3919*/ uint16(xArgRM64),
	/*3920*/ uint16(xMatch),
	/*3921*/ uint16(xCondIs64), 3924, 3940,
	/*3924*/ uint16(xCondDataSize), 3928, 3934, 0,
	/*3928*/ uint16(xSetOp), uint16(CMOVG),
	/*3930*/ uint16(xReadSlashR),
	/*3931*/ uint16(xArgR16),
	/*3932*/ uint16(xArgRM16),
	/*3933*/ uint16(xMatch),
	/*3934*/ uint16(xSetOp), uint16(CMOVG),
	/*3936*/ uint16(xReadSlashR),
	/*3937*/ uint16(xArgR32),
	/*3938*/ uint16(xArgRM32),
	/*3939*/ uint16(xMatch),
	/*3940*/ uint16(xCondDataSize), 3928, 3934, 3944,
	/*3944*/ uint16(xSetOp), uint16(CMOVG),
	/*3946*/ uint16(xReadSlashR),
	/*3947*/ uint16(xArgR64),
	/*3948*/ uint16(xArgRM64),
	/*3949*/ uint16(xMatch),
	/*3950*/ uint16(xCondPrefix), 2,
	0x66, 3962,
	0x0, 3956,
	/*3956*/ uint16(xSetOp), uint16(MOVMSKPS),
	/*3958*/ uint16(xReadSlashR),
	/*3959*/ uint16(xArgR32),
	/*3960*/ uint16(xArgXmm2),
	/*3961*/ uint16(xMatch),
	/*3962*/ uint16(xSetOp), uint16(MOVMSKPD),
	/*3964*/ uint16(xReadSlashR),
	/*3965*/ uint16(xArgR32),
	/*3966*/ uint16(xArgXmm2),
	/*3967*/ uint16(xMatch),
	/*3968*/ uint16(xCondPrefix), 4,
	0xF3, 3996,
	0xF2, 3990,
	0x66, 3984,
	0x0, 3978,
	/*3978*/ uint16(xSetOp), uint16(SQRTPS),
	/*3980*/ uint16(xReadSlashR),
	/*3981*/ uint16(xArgXmm1),
	/*3982*/ uint16(xArgXmm2M128),
	/*3983*/ uint16(xMatch),
	/*3984*/ uint16(xSetOp), uint16(SQRTPD),
	/*3986*/ uint16(xReadSlashR),
	/*3987*/ uint16(xArgXmm1),
	/*3988*/ uint16(xArgXmm2M128),
	/*3989*/ uint16(xMatch),
	/*3990*/ uint16(xSetOp), uint16(SQRTSD),
	/*3992*/ uint16(xReadSlashR),
	/*3993*/ uint16(xArgXmm1),
	/*3994*/ uint16(xArgXmm2M64),
	/*3995*/ uint16(xMatch),
	/*3996*/ uint16(xSetOp), uint16(SQRTSS),
	/*3998*/ uint16(xReadSlashR),
	/*3999*/ uint16(xArgXmm1),
	/*4000*/ uint16(xArgXmm2M32),
	/*4001*/ uint16(xMatch),
	/*4002*/ uint16(xCondPrefix), 2,
	0xF3, 4014,
	0x0, 4008,
	/*4008*/ uint16(xSetOp), uint16(RSQRTPS),
	/*4010*/ uint16(xReadSlashR),
	/*4011*/ uint16(xArgXmm1),
	/*4012*/ uint16(xArgXmm2M128),
	/*4013*/ uint16(xMatch),
	/*4014*/ uint16(xSetOp), uint16(RSQRTSS),
	/*4016*/ uint16(xReadSlashR),
	/*4017*/ uint16(xArgXmm1),
	/*4018*/ uint16(xArgXmm2M32),
	/*4019*/ uint16(xMatch),
	/*4020*/ uint16(xCondPrefix), 2,
	0xF3, 4032,
	0x0, 4026,
	/*4026*/ uint16(xSetOp), uint16(RCPPS),
	/*4028*/ uint16(xReadSlashR),
	/*4029*/ uint16(xArgXmm1),
	/*4030*/ uint16(xArgXmm2M128),
	/*4031*/ uint16(xMatch),
	/*4032*/ uint16(xSetOp), uint16(RCPSS),
	/*4034*/ uint16(xReadSlashR),
	/*4035*/ uint16(xArgXmm1),
	/*4036*/ uint16(xArgXmm2M32),
	/*4037*/ uint16(xMatch),
	/*4038*/ uint16(xCondPrefix), 2,
	0x66, 4050,
	0x0, 4044,
	/*4044*/ uint16(xSetOp), uint16(ANDPS),
	/*4046*/ uint16(xReadSlashR),
	/*4047*/ uint16(xArgXmm1),
	/*4048*/ uint16(xArgXmm2M128),
	/*4049*/ uint16(xMatch),
	/*4050*/ uint16(xSetOp), uint16(ANDPD),
	/*4052*/ uint16(xReadSlashR),
	/*4053*/ uint16(xArgXmm1),
	/*4054*/ uint16(xArgXmm2M128),
	/*4055*/ uint16(xMatch),
	/*4056*/ uint16(xCondPrefix), 2,
	0x66, 4068,
	0x0, 4062,
	/*4062*/ uint16(xSetOp), uint16(ANDNPS),
	/*4064*/ uint16(xReadSlashR),
	/*4065*/ uint16(xArgXmm1),
	/*4066*/ uint16(xArgXmm2M128),
	/*4067*/ uint16(xMatch),
	/*4068*/ uint16(xSetOp), uint16(ANDNPD),
	/*4070*/ uint16(xReadSlashR),
	/*4071*/ uint16(xArgXmm1),
	/*4072*/ uint16(xArgXmm2M128),
	/*4073*/ uint16(xMatch),
	/*4074*/ uint16(xCondPrefix), 2,
	0x66, 4086,
	0x0, 4080,
	/*4080*/ uint16(xSetOp), uint16(ORPS),
	/*4082*/ uint16(xReadSlashR),
	/*4083*/ uint16(xArgXmm1),
	/*4084*/ uint16(xArgXmm2M128),
	/*4085*/ uint16(xMatch),
	/*4086*/ uint16(xSetOp), uint16(ORPD),
	/*4088*/ uint16(xReadSlashR),
	/*4089*/ uint16(xArgXmm1),
	/*4090*/ uint16(xArgXmm2M128),
	/*4091*/ uint16(xMatch),
	/*4092*/ uint16(xCondPrefix), 2,
	0x66, 4104,
	0x0, 4098,
	/*4098*/ uint16(xSetOp), uint16(XORPS),
	/*4100*/ uint16(xReadSlashR),
	/*4101*/ uint16(xArgXmm1),
	/*4102*/ uint16(xArgXmm2M128),
	/*4103*/ uint16(xMatch),
	/*4104*/ uint16(xSetOp), uint16(XORPD),
	/*4106*/ uint16(xReadSlashR),
	/*4107*/ uint16(xArgXmm1),
	/*4108*/ uint16(xArgXmm2M128),
	/*4109*/ uint16(xMatch),
	/*4110*/ uint16(xCondPrefix), 4,
	0xF3, 4138,
	0xF2, 4132,
	0x66, 4126,
	0x0, 4120,
	/*4120*/ uint16(xSetOp), uint16(ADDPS),
	/*4122*/ uint16(xReadSlashR),
	/*4123*/ uint16(xArgXmm1),
	/*4124*/ uint16(xArgXmm2M128),
	/*4125*/ uint16(xMatch),
	/*4126*/ uint16(xSetOp), uint16(ADDPD),
	/*4128*/ uint16(xReadSlashR),
	/*4129*/ uint16(xArgXmm1),
	/*4130*/ uint16(xArgXmm2M128),
	/*4131*/ uint16(xMatch),
	/*4132*/ uint16(xSetOp), uint16(ADDSD),
	/*4134*/ uint16(xReadSlashR),
	/*4135*/ uint16(xArgXmm1),
	/*4136*/ uint16(xArgXmm2M64),
	/*4137*/ uint16(xMatch),
	/*4138*/ uint16(xSetOp), uint16(ADDSS),
	/*4140*/ uint16(xReadSlashR),
	/*4141*/ uint16(xArgXmm1),
	/*4142*/ uint16(xArgXmm2M32),
	/*4143*/ uint16(xMatch),
	/*4144*/ uint16(xCondPrefix), 4,
	0xF3, 4172,
	0xF2, 4166,
	0x66, 4160,
	0x0, 4154,
	/*4154*/ uint16(xSetOp), uint16(MULPS),
	/*4156*/ uint16(xReadSlashR),
	/*4157*/ uint16(xArgXmm1),
	/*4158*/ uint16(xArgXmm2M128),
	/*4159*/ uint16(xMatch),
	/*4160*/ uint16(xSetOp), uint16(MULPD),
	/*4162*/ uint16(xReadSlashR),
	/*4163*/ uint16(xArgXmm1),
	/*4164*/ uint16(xArgXmm2M128),
	/*4165*/ uint16(xMatch),
	/*4166*/ uint16(xSetOp), uint16(MULSD),
	/*4168*/ uint16(xReadSlashR),
	/*4169*/ uint16(xArgXmm1),
	/*4170*/ uint16(xArgXmm2M64),
	/*4171*/ uint16(xMatch),
	/*4172*/ uint16(xSetOp), uint16(MULSS),
	/*4174*/ uint16(xReadSlashR),
	/*4175*/ uint16(xArgXmm1),
	/*4176*/ uint16(xArgXmm2M32),
	/*4177*/ uint16(xMatch),
	/*4178*/ uint16(xCondPrefix), 4,
	0xF3, 4206,
	0xF2, 4200,
	0x66, 4194,
	0x0, 4188,
	/*4188*/ uint16(xSetOp), uint16(CVTPS2PD),
	/*4190*/ uint16(xReadSlashR),
	/*4191*/ uint16(xArgXmm1),
	/*4192*/ uint16(xArgXmm2M64),
	/*4193*/ uint16(xMatch),
	/*4194*/ uint16(xSetOp), uint16(CVTPD2PS),
	/*4196*/ uint16(xReadSlashR),
	/*4197*/ uint16(xArgXmm1),
	/*4198*/ uint16(xArgXmm2M128),
	/*4199*/ uint16(xMatch),
	/*4200*/ uint16(xSetOp), uint16(CVTSD2SS),
	/*4202*/ uint16(xReadSlashR),
	/*4203*/ uint16(xArgXmm1),
	/*4204*/ uint16(xArgXmm2M64),
	/*4205*/ uint16(xMatch),
	/*4206*/ uint16(xSetOp), uint16(CVTSS2SD),
	/*4208*/ uint16(xReadSlashR),
	/*4209*/ uint16(xArgXmm1),
	/*4210*/ uint16(xArgXmm2M32),
	/*4211*/ uint16(xMatch),
	/*4212*/ uint16(xCondPrefix), 3,
	0xF3, 4232,
	0x66, 4226,
	0x0, 4220,
	/*4220*/ uint16(xSetOp), uint16(CVTDQ2PS),
	/*4222*/ uint16(xReadSlashR),
	/*4223*/ uint16(xArgXmm1),
	/*4224*/ uint16(xArgXmm2M128),
	/*4225*/ uint16(xMatch),
	/*4226*/ uint16(xSetOp), uint16(CVTPS2DQ),
	/*4228*/ uint16(xReadSlashR),
	/*4229*/ uint16(xArgXmm1),
	/*4230*/ uint16(xArgXmm2M128),
	/*4231*/ uint16(xMatch),
	/*4232*/ uint16(xSetOp), uint16(CVTTPS2DQ),
	/*4234*/ uint16(xReadSlashR),
	/*4235*/ uint16(xArgXmm1),
	/*4236*/ uint16(xArgXmm2M128),
	/*4237*/ uint16(xMatch),
	/*4238*/ uint16(xCondPrefix), 4,
	0xF3, 4266,
	0xF2, 4260,
	0x66, 4254,
	0x0, 4248,
	/*4248*/ uint16(xSetOp), uint16(SUBPS),
	/*4250*/ uint16(xReadSlashR),
	/*4251*/ uint16(xArgXmm1),
	/*4252*/ uint16(xArgXmm2M128),
	/*4253*/ uint16(xMatch),
	/*4254*/ uint16(xSetOp), uint16(SUBPD),
	/*4256*/ uint16(xReadSlashR),
	/*4257*/ uint16(xArgXmm1),
	/*4258*/ uint16(xArgXmm2M128),
	/*4259*/ uint16(xMatch),
	/*4260*/ uint16(xSetOp), uint16(SUBSD),
	/*4262*/ uint16(xReadSlashR),
	/*4263*/ uint16(xArgXmm1),
	/*4264*/ uint16(xArgXmm2M64),
	/*4265*/ uint16(xMatch),
	/*4266*/ uint16(xSetOp), uint16(SUBSS),
	/*4268*/ uint16(xReadSlashR),
	/*4269*/ uint16(xArgXmm1),
	/*4270*/ uint16(xArgXmm2M32),
	/*4271*/ uint16(xMatch),
	/*4272*/ uint16(xCondPrefix), 4,
	0xF3, 4300,
	0xF2, 4294,
	0x66, 4288,
	0x0, 4282,
	/*4282*/ uint16(xSetOp), uint16(MINPS),
	/*4284*/ uint16(xReadSlashR),
	/*4285*/ uint16(xArgXmm1),
	/*4286*/ uint16(xArgXmm2M128),
	/*4287*/ uint16(xMatch),
	/*4288*/ uint16(xSetOp), uint16(MINPD),
	/*4290*/ uint16(xReadSlashR),
	/*4291*/ uint16(xArgXmm1),
	/*4292*/ uint16(xArgXmm2M128),
	/*4293*/ uint16(xMatch),
	/*4294*/ uint16(xSetOp), uint16(MINSD),
	/*4296*/ uint16(xReadSlashR),
	/*4297*/ uint16(xArgXmm1),
	/*4298*/ uint16(xArgXmm2M64),
	/*4299*/ uint16(xMatch),
	/*4300*/ uint16(xSetOp), uint16(MINSS),
	/*4302*/ uint16(xReadSlashR),
	/*4303*/ uint16(xArgXmm1),
	/*4304*/ uint16(xArgXmm2M32),
	/*4305*/ uint16(xMatch),
	/*4306*/ uint16(xCondPrefix), 4,
	0xF3, 4334,
	0xF2, 4328,
	0x66, 4322,
	0x0, 4316,
	/*4316*/ uint16(xSetOp), uint16(DIVPS),
	/*4318*/ uint16(xReadSlashR),
	/*4319*/ uint16(xArgXmm1),
	/*4320*/ uint16(xArgXmm2M128),
	/*4321*/ uint16(xMatch),
	/*4322*/ uint16(xSetOp), uint16(DIVPD),
	/*4324*/ uint16(xReadSlashR),
	/*4325*/ uint16(xArgXmm1),
	/*4326*/ uint16(xArgXmm2M128),
	/*4327*/ uint16(xMatch),
	/*4328*/ uint16(xSetOp), uint16(DIVSD),
	/*4330*/ uint16(xReadSlashR),
	/*4331*/ uint16(xArgXmm1),
	/*4332*/ uint16(xArgXmm2M64),
	/*4333*/ uint16(xMatch),
	/*4334*/ uint16(xSetOp), uint16(DIVSS),
	/*4336*/ uint16(xReadSlashR),
	/*4337*/ uint16(xArgXmm1),
	/*4338*/ uint16(xArgXmm2M32),
	/*4339*/ uint16(xMatch),
	/*4340*/ uint16(xCondPrefix), 4,
	0xF3, 4368,
	0xF2, 4362,
	0x66, 4356,
	0x0, 4350,
	/*4350*/ uint16(xSetOp), uint16(MAXPS),
	/*4352*/ uint16(xReadSlashR),
	/*4353*/ uint16(xArgXmm1),
	/*4354*/ uint16(xArgXmm2M128),
	/*4355*/ uint16(xMatch),
	/*4356*/ uint16(xSetOp), uint16(MAXPD),
	/*4358*/ uint16(xReadSlashR),
	/*4359*/ uint16(xArgXmm1),
	/*4360*/ uint16(xArgXmm2M128),
	/*4361*/ uint16(xMatch),
	/*4362*/ uint16(xSetOp), uint16(MAXSD),
	/*4364*/ uint16(xReadSlashR),
	/*4365*/ uint16(xArgXmm1),
	/*4366*/ uint16(xArgXmm2M64),
	/*4367*/ uint16(xMatch),
	/*4368*/ uint16(xSetOp), uint16(MAXSS),
	/*4370*/ uint16(xReadSlashR),
	/*4371*/ uint16(xArgXmm1),
	/*4372*/ uint16(xArgXmm2M32),
	/*4373*/ uint16(xMatch),
	/*4374*/ uint16(xCondPrefix), 2,
	0x66, 4386,
	0x0, 4380,
	/*4380*/ uint16(xSetOp), uint16(PUNPCKLBW),
	/*4382*/ uint16(xReadSlashR),
	/*4383*/ uint16(xArgMm),
	/*4384*/ uint16(xArgMmM32),
	/*4385*/ uint16(xMatch),
	/*4386*/ uint16(xSetOp), uint16(PUNPCKLBW),
	/*4388*/ uint16(xReadSlashR),
	/*4389*/ uint16(xArgXmm1),
	/*4390*/ uint16(xArgXmm2M128),
	/*4391*/ uint16(xMatch),
	/*4392*/ uint16(xCondPrefix), 2,
	0x66, 4404,
	0x0, 4398,
	/*4398*/ uint16(xSetOp), uint16(PUNPCKLWD),
	/*4400*/ uint16(xReadSlashR),
	/*4401*/ uint16(xArgMm),
	/*4402*/ uint16(xArgMmM32),
	/*4403*/ uint16(xMatch),
	/*4404*/ uint16(xSetOp), uint16(PUNPCKLWD),
	/*4406*/ uint16(xReadSlashR),
	/*4407*/ uint16(xArgXmm1),
	/*4408*/ uint16(xArgXmm2M128),
	/*4409*/ uint16(xMatch),
	/*4410*/ uint16(xCondPrefix), 2,
	0x66, 4422,
	0x0, 4416,
	/*4416*/ uint16(xSetOp), uint16(PUNPCKLDQ),
	/*4418*/ uint16(xReadSlashR),
	/*4419*/ uint16(xArgMm),
	/*4420*/ uint16(xArgMmM32),
	/*4421*/ uint16(xMatch),
	/*4422*/ uint16(xSetOp), uint16(PUNPCKLDQ),
	/*4424*/ uint16(xReadSlashR),
	/*4425*/ uint16(xArgXmm1),
	/*4426*/ uint16(xArgXmm2M128),
	/*4427*/ uint16(xMatch),
	/*4428*/ uint16(xCondPrefix), 2,
	0x66, 4440,
	0x0, 4434,
	/*4434*/ uint16(xSetOp), uint16(PACKSSWB),
	/*4436*/ uint16(xReadSlashR),
	/*4437*/ uint16(xArgMm1),
	/*4438*/ uint16(xArgMm2M64),
	/*4439*/ uint16(xMatch),
	/*4440*/ uint16(xSetOp), uint16(PACKSSWB),
	/*4442*/ uint16(xReadSlashR),
	/*4443*/ uint16(xArgXmm1),
	/*4444*/ uint16(xArgXmm2M128),
	/*4445*/ uint16(xMatch),
	/*4446*/ uint16(xCondPrefix), 2,
	0x66, 4458,
	0x0, 4452,
	/*4452*/ uint16(xSetOp), uint16(PCMPGTB),
	/*4454*/ uint16(xReadSlashR),
	/*4455*/ uint16(xArgMm),
	/*4456*/ uint16(xArgMmM64),
	/*4457*/ uint16(xMatch),
	/*4458*/ uint16(xSetOp), uint16(PCMPGTB),
	/*4460*/ uint16(xReadSlashR),
	/*4461*/ uint16(xArgXmm1),
	/*4462*/ uint16(xArgXmm2M128),
	/*4463*/ uint16(xMatch),
	/*4464*/ uint16(xCondPrefix), 2,
	0x66, 4476,
	0x0, 4470,
	/*4470*/ uint16(xSetOp), uint16(PCMPGTW),
	/*4472*/ uint16(xReadSlashR),
	/*4473*/ uint16(xArgMm),
	/*4474*/ uint16(xArgMmM64),
	/*4475*/ uint16(xMatch),
	/*4476*/ uint16(xSetOp), uint16(PCMPGTW),
	/*4478*/ uint16(xReadSlashR),
	/*4479*/ uint16(xArgXmm1),
	/*4480*/ uint16(xArgXmm2M128),
	/*4481*/ uint16(xMatch),
	/*4482*/ uint16(xCondPrefix), 2,
	0x66, 4494,
	0x0, 4488,
	/*4488*/ uint16(xSetOp), uint16(PCMPGTD),
	/*4490*/ uint16(xReadSlashR),
	/*4491*/ uint16(xArgMm),
	/*4492*/ uint16(xArgMmM64),
	/*4493*/ uint16(xMatch),
	/*4494*/ uint16(xSetOp), uint16(PCMPGTD),
	/*4496*/ uint16(xReadSlashR),
	/*4497*/ uint16(xArgXmm1),
	/*4498*/ uint16(xArgXmm2M128),
	/*4499*/ uint16(xMatch),
	/*4500*/ uint16(xCondPrefix), 2,
	0x66, 4512,
	0x0, 4506,
	/*4506*/ uint16(xSetOp), uint16(PACKUSWB),
	/*4508*/ uint16(xReadSlashR),
	/*4509*/ uint16(xArgMm),
	/*4510*/ uint16(xArgMmM64),
	/*4511*/ uint16(xMatch),
	/*4512*/ uint16(xSetOp), uint16(PACKUSWB),
	/*4514*/ uint16(xReadSlashR),
	/*4515*/ uint16(xArgXmm1),
	/*4516*/ uint16(xArgXmm2M128),
	/*4517*/ uint16(xMatch),
	/*4518*/ uint16(xCondPrefix), 2,
	0x66, 4530,
	0x0, 4524,
	/*4524*/ uint16(xSetOp), uint16(PUNPCKHBW),
	/*4526*/ uint16(xReadSlashR),
	/*4527*/ uint16(xArgMm),
	/*4528*/ uint16(xArgMmM64),
	/*4529*/ uint16(xMatch),
	/*4530*/ uint16(xSetOp), uint16(PUNPCKHBW),
	/*4532*/ uint16(xReadSlashR),
	/*4533*/ uint16(xArgXmm1),
	/*4534*/ uint16(xArgXmm2M128),
	/*4535*/ uint16(xMatch),
	/*4536*/ uint16(xCondPrefix), 2,
	0x66, 4548,
	0x0, 4542,
	/*4542*/ uint16(xSetOp), uint16(PUNPCKHWD),
	/*4544*/ uint16(xReadSlashR),
	/*4545*/ uint16(xArgMm),
	/*4546*/ uint16(xArgMmM64),
	/*4547*/ uint16(xMatch),
	/*4548*/ uint16(xSetOp), uint16(PUNPCKHWD),
	/*4550*/ uint16(xReadSlashR),
	/*4551*/ uint16(xArgXmm1),
	/*4552*/ uint16(xArgXmm2M128),
	/*4553*/ uint16(xMatch),
	/*4554*/ uint16(xCondPrefix), 2,
	0x66, 4566,
	0x0, 4560,
	/*4560*/ uint16(xSetOp), uint16(PUNPCKHDQ),
	/*4562*/ uint16(xReadSlashR),
	/*4563*/ uint16(xArgMm),
	/*4564*/ uint16(xArgMmM64),
	/*4565*/ uint16(xMatch),
	/*4566*/ uint16(xSetOp), uint16(PUNPCKHDQ),
	/*4568*/ uint16(xReadSlashR),
	/*4569*/ uint16(xArgXmm1),
	/*4570*/ uint16(xArgXmm2M128),
	/*4571*/ uint16(xMatch),
	/*4572*/ uint16(xCondPrefix), 2,
	0x66, 4584,
	0x0, 4578,
	/*4578*/ uint16(xSetOp), uint16(PACKSSDW),
	/*4580*/ uint16(xReadSlashR),
	/*4581*/ uint16(xArgMm1),
	/*4582*/ uint16(xArgMm2M64),
	/*4583*/ uint16(xMatch),
	/*4584*/ uint16(xSetOp), uint16(PACKSSDW),
	/*4586*/ uint16(xReadSlashR),
	/*4587*/ uint16(xArgXmm1),
	/*4588*/ uint16(xArgXmm2M128),
	/*4589*/ uint16(xMatch),
	/*4590*/ uint16(xCondPrefix), 1,
	0x66, 4594,
	/*4594*/ uint16(xSetOp), uint16(PUNPCKLQDQ),
	/*4596*/ uint16(xReadSlashR),
	/*4597*/ uint16(xArgXmm1),
	/*4598*/ uint16(xArgXmm2M128),
	/*4599*/ uint16(xMatch),
	/*4600*/ uint16(xCondPrefix), 1,
	0x66, 4604,
	/*4604*/ uint16(xSetOp), uint16(PUNPCKHQDQ),
	/*4606*/ uint16(xReadSlashR),
	/*4607*/ uint16(xArgXmm1),
	/*4608*/ uint16(xArgXmm2M128),
	/*4609*/ uint16(xMatch),
	/*4610*/ uint16(xCondIs64), 4613, 4651,
	/*4613*/ uint16(xCondPrefix), 2,
	0x66, 4635,
	0x0, 4619,
	/*4619*/ uint16(xCondDataSize), 4623, 4629, 0,
	/*4623*/ uint16(xSetOp), uint16(MOVD),
	/*4625*/ uint16(xReadSlashR),
	/*4626*/ uint16(xArgMm),
	/*4627*/ uint16(xArgRM32),
	/*4628*/ uint16(xMatch),
	/*4629*/ uint16(xSetOp), uint16(MOVD),
	/*4631*/ uint16(xReadSlashR),
	/*4632*/ uint16(xArgMm),
	/*4633*/ uint16(xArgRM32),
	/*4634*/ uint16(xMatch),
	/*4635*/ uint16(xCondDataSize), 4639, 4645, 0,
	/*4639*/ uint16(xSetOp), uint16(MOVD),
	/*4641*/ uint16(xReadSlashR),
	/*4642*/ uint16(xArgXmm),
	/*4643*/ uint16(xArgRM32),
	/*4644*/ uint16(xMatch),
	/*4645*/ uint16(xSetOp), uint16(MOVD),
	/*4647*/ uint16(xReadSlashR),
	/*4648*/ uint16(xArgXmm),
	/*4649*/ uint16(xArgRM32),
	/*4650*/ uint16(xMatch),
	/*4651*/ uint16(xCondPrefix), 2,
	0x66, 4667,
	0x0, 4657,
	/*4657*/ uint16(xCondDataSize), 4623, 4629, 4661,
	/*4661*/ uint16(xSetOp), uint16(MOVQ),
	/*4663*/ uint16(xReadSlashR),
	/*4664*/ uint16(xArgMm),
	/*4665*/ uint16(xArgRM64),
	/*4666*/ uint16(xMatch),
	/*4667*/ uint16(xCondDataSize), 4639, 4645, 4671,
	/*4671*/ uint16(xSetOp), uint16(MOVQ),
	/*4673*/ uint16(xReadSlashR),
	/*4674*/ uint16(xArgXmm),
	/*4675*/ uint16(xArgRM64),
	/*4676*/ uint16(xMatch),
	/*4677*/ uint16(xCondPrefix), 3,
	0xF3, 4697,
	0x66, 4691,
	0x0, 4685,
	/*4685*/ uint16(xSetOp), uint16(MOVQ),
	/*4687*/ uint16(xReadSlashR),
	/*4688*/ uint16(xArgMm),
	/*4689*/ uint16(xArgMmM64),
	/*4690*/ uint16(xMatch),
	/*4691*/ uint16(xSetOp), uint16(MOVDQA),
	/*4693*/ uint16(xReadSlashR),
	/*4694*/ uint16(xArgXmm1),
	/*4695*/ uint16(xArgXmm2M128),
	/*4696*/ uint16(xMatch),
	/*4697*/ uint16(xSetOp), uint16(MOVDQU),
	/*4699*/ uint16(xReadSlashR),
	/*4700*/ uint16(xArgXmm1),
	/*4701*/ uint16(xArgXmm2M128),
	/*4702*/ uint16(xMatch),
	/*4703*/ uint16(xCondPrefix), 4,
	0xF3, 4737,
	0xF2, 4729,
	0x66, 4721,
	0x0, 4713,
	/*4713*/ uint16(xSetOp), uint16(PSHUFW),
	/*4715*/ uint16(xReadSlashR),
	/*4716*/ uint16(xReadIb),
	/*4717*/ uint16(xArgMm1),
	/*4718*/ uint16(xArgMm2M64),
	/*4719*/ uint16(xArgImm8u),
	/*4720*/ uint16(xMatch),
	/*4721*/ uint16(xSetOp), uint16(PSHUFD),
	/*4723*/ uint16(xReadSlashR),
	/*4724*/ uint16(xReadIb),
	/*4725*/ uint16(xArgXmm1),
	/*4726*/ uint16(xArgXmm2M128),
	/*4727*/ uint16(xArgImm8u),
	/*4728*/ uint16(xMatch),
	/*4729*/ uint16(xSetOp), uint16(PSHUFLW),
	/*4731*/ uint16(xReadSlashR),
	/*4732*/ uint16(xReadIb),
	/*4733*/ uint16(xArgXmm1),
	/*4734*/ uint16(xArgXmm2M128),
	/*4735*/ uint16(xArgImm8u),
	/*4736*/ uint16(xMatch),
	/*4737*/ uint16(xSetOp), uint16(PSHUFHW),
	/*4739*/ uint16(xReadSlashR),
	/*4740*/ uint16(xReadIb),
	/*4741*/ uint16(xArgXmm1),
	/*4742*/ uint16(xArgXmm2M128),
	/*4743*/ uint16(xArgImm8u),
	/*4744*/ uint16(xMatch),
	/*4745*/ uint16(xCondSlashR),
	0,    // 0
	0,    // 1
	4754, // 2
	0,    // 3
	4772, // 4
	0,    // 5
	4790, // 6
	0,    // 7
	/*4754*/ uint16(xCondPrefix), 2,
	0x66, 4766,
	0x0, 4760,
	/*4760*/ uint16(xSetOp), uint16(PSRLW),
	/*4762*/ uint16(xReadIb),
	/*4763*/ uint16(xArgMm2),
	/*4764*/ uint16(xArgImm8u),
	/*4765*/ uint16(xMatch),
	/*4766*/ uint16(xSetOp), uint16(PSRLW),
	/*4768*/ uint16(xReadIb),
	/*4769*/ uint16(xArgXmm2),
	/*4770*/ uint16(xArgImm8u),
	/*4771*/ uint16(xMatch),
	/*4772*/ uint16(xCondPrefix), 2,
	0x66, 4784,
	0x0, 4778,
	/*4778*/ uint16(xSetOp), uint16(PSRAW),
	/*4780*/ uint16(xReadIb),
	/*4781*/ uint16(xArgMm2),
	/*4782*/ uint16(xArgImm8u),
	/*4783*/ uint16(xMatch),
	/*4784*/ uint16(xSetOp), uint16(PSRAW),
	/*4786*/ uint16(xReadIb),
	/*4787*/ uint16(xArgXmm2),
	/*4788*/ uint16(xArgImm8u),
	/*4789*/ uint16(xMatch),
	/*4790*/ uint16(xCondPrefix), 2,
	0x66, 4802,
	0x0, 4796,
	/*4796*/ uint16(xSetOp), uint16(PSLLW),
	/*4798*/ uint16(xReadIb),
	/*4799*/ uint16(xArgMm2),
	/*4800*/ uint16(xArgImm8u),
	/*4801*/ uint16(xMatch),
	/*4802*/ uint16(xSetOp), uint16(PSLLW),
	/*4804*/ uint16(xReadIb),
	/*4805*/ uint16(xArgXmm2),
	/*4806*/ uint16(xArgImm8u),
	/*4807*/ uint16(xMatch),
	/*4808*/ uint16(xCondSlashR),
	0,    // 0
	0,    // 1
	4817, // 2
	0,    // 3
	4835, // 4
	0,    // 5
	4853, // 6
	0,    // 7
	/*4817*/ uint16(xCondPrefix), 2,
	0x66, 4829,
	0x0, 4823,
	/*4823*/ uint16(xSetOp), uint16(PSRLD),
	/*4825*/ uint16(xReadIb),
	/*4826*/ uint16(xArgMm2),
	/*4827*/ uint16(xArgImm8u),
	/*4828*/ uint16(xMatch),
	/*4829*/ uint16(xSetOp), uint16(PSRLD),
	/*4831*/ uint16(xReadIb),
	/*4832*/ uint16(xArgXmm2),
	/*4833*/ uint16(xArgImm8u),
	/*4834*/ uint16(xMatch),
	/*4835*/ uint16(xCondPrefix), 2,
	0x66, 4847,
	0x0, 4841,
	/*4841*/ uint16(xSetOp), uint16(PSRAD),
	/*4843*/ uint16(xReadIb),
	/*4844*/ uint16(xArgMm2),
	/*4845*/ uint16(xArgImm8u),
	/*4846*/ uint16(xMatch),
	/*4847*/ uint16(xSetOp), uint16(PSRAD),
	/*4849*/ uint16(xReadIb),
	/*4850*/ uint16(xArgXmm2),
	/*4851*/ uint16(xArgImm8u),
	/*4852*/ uint16(xMatch),
	/*4853*/ uint16(xCondPrefix), 2,
	0x66, 4865,
	0x0, 4859,
	/*4859*/ uint16(xSetOp), uint16(PSLLD),
	/*4861*/ uint16(xReadIb),
	/*4862*/ uint16(xArgMm2),
	/*4863*/ uint16(xArgImm8u),
	/*4864*/ uint16(xMatch),
	/*4865*/ uint16(xSetOp), uint16(PSLLD),
	/*4867*/ uint16(xReadIb),
	/*4868*/ uint16(xArgXmm2),
	/*4869*/ uint16(xArgImm8u),
	/*4870*/ uint16(xMatch),
	/*4871*/ uint16(xCondSlashR),
	0,    // 0
	0,    // 1
	4880, // 2
	4898, // 3
	0,    // 4
	0,    // 5
	4908, // 6
	4926, // 7
	/*4880*/ uint16(xCondPrefix), 2,
	0x66, 4892,
	0x0, 4886,
	/*4886*/ uint16(xSetOp), uint16(PSRLQ),
	/*4888*/ uint16(xReadIb),
	/*4889*/ uint16(xArgMm2),
	/*4890*/ uint16(xArgImm8u),
	/*4891*/ uint16(xMatch),
	/*4892*/ uint16(xSetOp), uint16(PSRLQ),
	/*4894*/ uint16(xReadIb),
	/*4895*/ uint16(xArgXmm2),
	/*4896*/ uint16(xArgImm8u),
	/*4897*/ uint16(xMatch),
	/*4898*/ uint16(xCondPrefix), 1,
	0x66, 4902,
	/*4902*/ uint16(xSetOp), uint16(PSRLDQ),
	/*4904*/ uint16(xReadIb),
	/*4905*/ uint16(xArgXmm2),
	/*4906*/ uint16(xArgImm8u),
	/*4907*/ uint16(xMatch),
	/*4908*/ uint16(xCondPrefix), 2,
	0x66, 4920,
	0x0, 4914,
	/*4914*/ uint16(xSetOp), uint16(PSLLQ),
	/*4916*/ uint16(xReadIb),
	/*4917*/ uint16(xArgMm2),
	/*4918*/ uint16(xArgImm8u),
	/*4919*/ uint16(xMatch),
	/*4920*/ uint16(xSetOp), uint16(PSLLQ),
	/*4922*/ uint16(xReadIb),
	/*4923*/ uint16(xArgXmm2),
	/*4924*/ uint16(xArgImm8u),
	/*4925*/ uint16(xMatch),
	/*4926*/ uint16(xCondPrefix), 1,
	0x66, 4930,
	/*4930*/ uint16(xSetOp), uint16(PSLLDQ),
	/*4932*/ uint16(xReadIb),
	/*4933*/ uint16(xArgXmm2),
	/*4934*/ uint16(xArgImm8u),
	/*4935*/ uint16(xMatch),
	/*4936*/ uint16(xCondPrefix), 2,
	0x66, 4948,
	0x0, 4942,
	/*4942*/ uint16(xSetOp), uint16(PCMPEQB),
	/*4944*/ uint16(xReadSlashR),
	/*4945*/ uint16(xArgMm),
	/*4946*/ uint16(xArgMmM64),
	/*4947*/ uint16(xMatch),
	/*4948*/ uint16(xSetOp), uint16(PCMPEQB),
	/*4950*/ uint16(xReadSlashR),
	/*4951*/ uint16(xArgXmm1),
	/*4952*/ uint16(xArgXmm2M128),
	/*4953*/ uint16(xMatch),
	/*4954*/ uint16(xCondPrefix), 2,
	0x66, 4966,
	0x0, 4960,
	/*4960*/ uint16(xSetOp), uint16(PCMPEQW),
	/*4962*/ uint16(xReadSlashR),
	/*4963*/ uint16(xArgMm),
	/*4964*/ uint16(xArgMmM64),
	/*4965*/ uint16(xMatch),
	/*4966*/ uint16(xSetOp), uint16(PCMPEQW),
	/*4968*/ uint16(xReadSlashR),
	/*4969*/ uint16(xArgXmm1),
	/*4970*/ uint16(xArgXmm2M128),
	/*4971*/ uint16(xMatch),
	/*4972*/ uint16(xCondPrefix), 2,
	0x66, 4984,
	0x0, 4978,
	/*4978*/ uint16(xSetOp), uint16(PCMPEQD),
	/*4980*/ uint16(xReadSlashR),
	/*4981*/ uint16(xArgMm),
	/*4982*/ uint16(xArgMmM64),
	/*4983*/ uint16(xMatch),
	/*4984*/ uint16(xSetOp), uint16(PCMPEQD),
	/*4986*/ uint16(xReadSlashR),
	/*4987*/ uint16(xArgXmm1),
	/*4988*/ uint16(xArgXmm2M128),
	/*4989*/ uint16(xMatch),
	/*4990*/ uint16(xSetOp), uint16(EMMS),
	/*4992*/ uint16(xMatch),
	/*4993*/ uint16(xCondPrefix), 2,
	0xF2, 5005,
	0x66, 4999,
	/*4999*/ uint16(xSetOp), uint16(HADDPD),
	/*5001*/ uint16(xReadSlashR),
	/*5002*/ uint16(xArgXmm1),
	/*5003*/ uint16(xArgXmm2M128),
	/*5004*/ uint16(xMatch),
	/*5005*/ uint16(xSetOp), uint16(HADDPS),
	/*5007*/ uint16(xReadSlashR),
	/*5008*/ uint16(xArgXmm1),
	/*5009*/ uint16(xArgXmm2M128),
	/*5010*/ uint16(xMatch),
	/*5011*/ uint16(xCondPrefix), 2,
	0xF2, 5023,
	0x66, 5017,
	/*5017*/ uint16(xSetOp), uint16(HSUBPD),
	/*5019*/ uint16(xReadSlashR),
	/*5020*/ uint16(xArgXmm1),
	/*5021*/ uint16(xArgXmm2M128),
	/*5022*/ uint16(xMatch),
	/*5023*/ uint16(xSetOp), uint16(HSUBPS),
	/*5025*/ uint16(xReadSlashR),
	/*5026*/ uint16(xArgXmm1),
	/*5027*/ uint16(xArgXmm2M128),
	/*5028*/ uint16(xMatch),
	/*5029*/ uint16(xCondIs64), 5032, 5078,
	/*5032*/ uint16(xCondPrefix), 3,
	0xF3, 5072,
	0x66, 5056,
	0x0, 5040,
	/*5040*/ uint16(xCondDataSize), 5044, 5050, 0,
	/*5044*/ uint16(xSetOp), uint16(MOVD),
	/*5046*/ uint16(xReadSlashR),
	/*5047*/ uint16(xArgRM32),
	/*5048*/ uint16(xArgMm),
	/*5049*/ uint16(xMatch),
	/*5050*/ uint16(xSetOp), uint16(MOVD),
	/*5052*/ uint16(xReadSlashR),
	/*5053*/ uint16(xArgRM32),
	/*5054*/ uint16(xArgMm),
	/*5055*/ uint16(xMatch),
	/*5056*/ uint16(xCondDataSize), 5060, 5066, 0,
	/*5060*/ uint16(xSetOp), uint16(MOVD),
	/*5062*/ uint16(xReadSlashR),
	/*5063*/ uint16(xArgRM32),
	/*5064*/ uint16(xArgXmm),
	/*5065*/ uint16(xMatch),
	/*5066*/ uint16(xSetOp), uint16(MOVD),
	/*5068*/ uint16(xReadSlashR),
	/*5069*/ uint16(xArgRM32),
	/*5070*/ uint16(xArgXmm),
	/*5071*/ uint16(xMatch),
	/*5072*/ uint16(xSetOp), uint16(MOVQ),
	/*5074*/ uint16(xReadSlashR),
	/*5075*/ uint16(xArgXmm1),
	/*5076*/ uint16(xArgXmm2M64),
	/*5077*/ uint16(xMatch),
	/*5078*/ uint16(xCondPrefix), 3,
	0xF3, 5072,
	0x66, 5096,
	0x0, 5086,
	/*5086*/ uint16(xCondDataSize), 5044, 5050, 5090,
	/*5090*/ uint16(xSetOp), uint16(MOVQ),
	/*5092*/ uint16(xReadSlashR),
	/*5093*/ uint16(xArgRM64),
	/*5094*/ uint16(xArgMm),
	/*5095*/ uint16(xMatch),
	/*5096*/ uint16(xCondDataSize), 5060, 5066, 5100,
	/*5100*/ uint16(xSetOp), uint16(MOVQ),
	/*5102*/ uint16(xReadSlashR),
	/*5103*/ uint16(xArgRM64),
	/*5104*/ uint16(xArgXmm),
	/*5105*/ uint16(xMatch),
	/*5106*/ uint16(xCondPrefix), 3,
	0xF3, 5126,
	0x66, 5120,
	0x0, 5114,
	/*5114*/ uint16(xSetOp), uint16(MOVQ),
	/*5116*/ uint16(xReadSlashR),
	/*5117*/ uint16(xArgMmM64),
	/*5118*/ uint16(xArgMm),
	/*5119*/ uint16(xMatch),
	/*5120*/ uint16(xSetOp), uint16(MOVDQA),
	/*5122*/ uint16(xReadSlashR),
	/*5123*/ uint16(xArgXmm2M128),
	/*5124*/ uint16(xArgXmm1),
	/*5125*/ uint16(xMatch),
	/*5126*/ uint16(xSetOp), uint16(MOVDQU),
	/*5128*/ uint16(xReadSlashR),
	/*5129*/ uint16(xArgXmm2M128),
	/*5130*/ uint16(xArgXmm1),
	/*5131*/ uint16(xMatch),
	/*5132*/ uint16(xCondIs64), 5135, 5149,
	/*5135*/ uint16(xCondDataSize), 5139, 5144, 0,
	/*5139*/ uint16(xSetOp), uint16(JO),
	/*5141*/ uint16(xReadCw),
	/*5142*/ uint16(xArgRel16),
	/*5143*/ uint16(xMatch),
	/*5144*/ uint16(xSetOp), uint16(JO),
	/*5146*/ uint16(xReadCd),
	/*5147*/ uint16(xArgRel32),
	/*5148*/ uint16(xMatch),
	/*5149*/ uint16(xCondDataSize), 5153, 5144, 5158,
	/*5153*/ uint16(xSetOp), uint16(JO),
	/*5155*/ uint16(xReadCd),
	/*5156*/ uint16(xArgRel32),
	/*5157*/ uint16(xMatch),
	/*5158*/ uint16(xSetOp), uint16(JO),
	/*5160*/ uint16(xReadCd),
	/*5161*/ uint16(xArgRel32),
	/*5162*/ uint16(xMatch),
	/*5163*/ uint16(xCondIs64), 5166, 5180,
	/*5166*/ uint16(xCondDataSize), 5170, 5175, 0,
	/*5170*/ uint16(xSetOp), uint16(JNO),
	/*5172*/ uint16(xReadCw),
	/*5173*/ uint16(xArgRel16),
	/*5174*/ uint16(xMatch),
	/*5175*/ uint16(xSetOp), uint16(JNO),
	/*5177*/ uint16(xReadCd),
	/*5178*/ uint16(xArgRel32),
	/*5179*/ uint16(xMatch),
	/*5180*/ uint16(xCondDataSize), 5184, 5175, 5189,
	/*5184*/ uint16(xSetOp), uint16(JNO),
	/*5186*/ uint16(xReadCd),
	/*5187*/ uint16(xArgRel32),
	/*5188*/ uint16(xMatch),
	/*5189*/ uint16(xSetOp), uint16(JNO),
	/*5191*/ uint16(xReadCd),
	/*5192*/ uint16(xArgRel32),
	/*5193*/ uint16(xMatch),
	/*5194*/ uint16(xCondIs64), 5197, 5211,
	/*5197*/ uint16(xCondDataSize), 5201, 5206, 0,
	/*5201*/ uint16(xSetOp), uint16(JB),
	/*5203*/ uint16(xReadCw),
	/*5204*/ uint16(xArgRel16),
	/*5205*/ uint16(xMatch),
	/*5206*/ uint16(xSetOp), uint16(JB),
	/*5208*/ uint16(xReadCd),
	/*5209*/ uint16(xArgRel32),
	/*5210*/ uint16(xMatch),
	/*5211*/ uint16(xCondDataSize), 5215, 5206, 5220,
	/*5215*/ uint16(xSetOp), uint16(JB),
	/*5217*/ uint16(xReadCd),
	/*5218*/ uint16(xArgRel32),
	/*5219*/ uint16(xMatch),
	/*5220*/ uint16(xSetOp), uint16(JB),
	/*5222*/ uint16(xReadCd),
	/*5223*/ uint16(xArgRel32),
	/*5224*/ uint16(xMatch),
	/*5225*/ uint16(xCondIs64), 5228, 5242,
	/*5228*/ uint16(xCondDataSize), 5232, 5237, 0,
	/*5232*/ uint16(xSetOp), uint16(JAE),
	/*5234*/ uint16(xReadCw),
	/*5235*/ uint16(xArgRel16),
	/*5236*/ uint16(xMatch),
	/*5237*/ uint16(xSetOp), uint16(JAE),
	/*5239*/ uint16(xReadCd),
	/*5240*/ uint16(xArgRel32),
	/*5241*/ uint16(xMatch),
	/*5242*/ uint16(xCondDataSize), 5246, 5237, 5251,
	/*5246*/ uint16(xSetOp), uint16(JAE),
	/*5248*/ uint16(xReadCd),
	/*5249*/ uint16(xArgRel32),
	/*5250*/ uint16(xMatch),
	/*5251*/ uint16(xSetOp), uint16(JAE),
	/*5253*/ uint16(xReadCd),
	/*5254*/ uint16(xArgRel32),
	/*5255*/ uint16(xMatch),
	/*5256*/ uint16(xCondIs64), 5259, 5273,
	/*5259*/ uint16(xCondDataSize), 5263, 5268, 0,
	/*5263*/ uint16(xSetOp), uint16(JE),
	/*5265*/ uint16(xReadCw),
	/*5266*/ uint16(xArgRel16),
	/*5267*/ uint16(xMatch),
	/*5268*/ uint16(xSetOp), uint16(JE),
	/*5270*/ uint16(xReadCd),
	/*5271*/ uint16(xArgRel32),
	/*5272*/ uint16(xMatch),
	/*5273*/ uint16(xCondDataSize), 5277, 5268, 5282,
	/*5277*/ uint16(xSetOp), uint16(JE),
	/*5279*/ uint16(xReadCd),
	/*5280*/ uint16(xArgRel32),
	/*5281*/ uint16(xMatch),
	/*5282*/ uint16(xSetOp), uint16(JE),
	/*5284*/ uint16(xReadCd),
	/*5285*/ uint16(xArgRel32),
	/*5286*/ uint16(xMatch),
	/*5287*/ uint16(xCondIs64), 5290, 5304,
	/*5290*/ uint16(xCondDataSize), 5294, 5299, 0,
	/*5294*/ uint16(xSetOp), uint16(JNE),
	/*5296*/ uint16(xReadCw),
	/*5297*/ uint16(xArgRel16),
	/*5298*/ uint16(xMatch),
	/*5299*/ uint16(xSetOp), uint16(JNE),
	/*5301*/ uint16(xReadCd),
	/*5302*/ uint16(xArgRel32),
	/*5303*/ uint16(xMatch),
	/*5304*/ uint16(xCondDataSize), 5308, 5299, 5313,
	/*5308*/ uint16(xSetOp), uint16(JNE),
	/*5310*/ uint16(xReadCd),
	/*5311*/ uint16(xArgRel32),
	/*5312*/ uint16(xMatch),
	/*5313*/ uint16(xSetOp), uint16(JNE),
	/*5315*/ uint16(xReadCd),
	/*5316*/ uint16(xArgRel32),
	/*5317*/ uint16(xMatch),
	/*5318*/ uint16(xCondIs64), 5321, 5335,
	/*5321*/ uint16(xCondDataSize), 5325, 5330, 0,
	/*5325*/ uint16(xSetOp), uint16(JBE),
	/*5327*/ uint16(xReadCw),
	/*5328*/ uint16(xArgRel16),
	/*5329*/ uint16(xMatch),
	/*5330*/ uint16(xSetOp), uint16(JBE),
	/*5332*/ uint16(xReadCd),
	/*5333*/ uint16(xArgRel32),
	/*5334*/ uint16(xMatch),
	/*5335*/ uint16(xCondDataSize), 5339, 5330, 5344,
	/*5339*/ uint16(xSetOp), uint16(JBE),
	/*5341*/ uint16(xReadCd),
	/*5342*/ uint16(xArgRel32),
	/*5343*/ uint16(xMatch),
	/*5344*/ uint16(xSetOp), uint16(JBE),
	/*5346*/ uint16(xReadCd),
	/*5347*/ uint16(xArgRel32),
	/*5348*/ uint16(xMatch),
	/*5349*/ uint16(xCondIs64), 5352, 5366,
	/*5352*/ uint16(xCondDataSize), 5356, 5361, 0,
	/*5356*/ uint16(xSetOp), uint16(JA),
	/*5358*/ uint16(xReadCw),
	/*5359*/ uint16(xArgRel16),
	/*5360*/ uint16(xMatch),
	/*5361*/ uint16(xSetOp), uint16(JA),
	/*5363*/ uint16(xReadCd),
	/*5364*/ uint16(xArgRel32),
	/*5365*/ uint16(xMatch),
	/*5366*/ uint16(xCondDataSize), 5370, 5361, 5375,
	/*5370*/ uint16(xSetOp), uint16(JA),
	/*5372*/ uint16(xReadCd),
	/*5373*/ uint16(xArgRel32),
	/*5374*/ uint16(xMatch),
	/*5375*/ uint16(xSetOp), uint16(JA),
	/*5377*/ uint16(xReadCd),
	/*5378*/ uint16(xArgRel32),
	/*5379*/ uint16(xMatch),
	/*5380*/ uint16(xCondIs64), 5383, 5397,
	/*5383*/ uint16(xCondDataSize), 5387, 5392, 0,
	/*5387*/ uint16(xSetOp), uint16(JS),
	/*5389*/ uint16(xReadCw),
	/*5390*/ uint16(xArgRel16),
	/*5391*/ uint16(xMatch),
	/*5392*/ uint16(xSetOp), uint16(JS),
	/*5394*/ uint16(xReadCd),
	/*5395*/ uint16(xArgRel32),
	/*5396*/ uint16(xMatch),
	/*5397*/ uint16(xCondDataSize), 5401, 5392, 5406,
	/*5401*/ uint16(xSetOp), uint16(JS),
	/*5403*/ uint16(xReadCd),
	/*5404*/ uint16(xArgRel32),
	/*5405*/ uint16(xMatch),
	/*5406*/ uint16(xSetOp), uint16(JS),
	/*5408*/ uint16(xReadCd),
	/*5409*/ uint16(xArgRel32),
	/*5410*/ uint16(xMatch),
	/*5411*/ uint16(xCondIs64), 5414, 5428,
	/*5414*/ uint16(xCondDataSize), 5418, 5423, 0,
	/*5418*/ uint16(xSetOp), uint16(JNS),
	/*5420*/ uint16(xReadCw),
	/*5421*/ uint16(xArgRel16),
	/*5422*/ uint16(xMatch),
	/*5423*/ uint16(xSetOp), uint16(JNS),
	/*5425*/ uint16(xReadCd),
	/*5426*/ uint16(xArgRel32),
	/*5427*/ uint16(xMatch),
	/*5428*/ uint16(xCondDataSize), 5432, 5423, 5437,
	/*5432*/ uint16(xSetOp), uint16(JNS),
	/*5434*/ uint16(xReadCd),
	/*5435*/ uint16(xArgRel32),
	/*5436*/ uint16(xMatch),
	/*5437*/ uint16(xSetOp), uint16(JNS),
	/*5439*/ uint16(xReadCd),
	/*5440*/ uint16(xArgRel32),
	/*5441*/ uint16(xMatch),
	/*5442*/ uint16(xCondIs64), 5445, 5459,
	/*5445*/ uint16(xCondDataSize), 5449, 5454, 0,
	/*5449*/ uint16(xSetOp), uint16(JP),
	/*5451*/ uint16(xReadCw),
	/*5452*/ uint16(xArgRel16),
	/*5453*/ uint16(xMatch),
	/*5454*/ uint16(xSetOp), uint16(JP),
	/*5456*/ uint16(xReadCd),
	/*5457*/ uint16(xArgRel32),
	/*5458*/ uint16(xMatch),
	/*5459*/ uint16(xCondDataSize), 5463, 5454, 5468,
	/*5463*/ uint16(xSetOp), uint16(JP),
	/*5465*/ uint16(xReadCd),
	/*5466*/ uint16(xArgRel32),
	/*5467*/ uint16(xMatch),
	/*5468*/ uint16(xSetOp), uint16(JP),
	/*5470*/ uint16(xReadCd),
	/*5471*/ uint16(xArgRel32),
	/*5472*/ uint16(xMatch),
	/*5473*/ uint16(xCondIs64), 5476, 5490,
	/*5476*/ uint16(xCondDataSize), 5480, 5485, 0,
	/*5480*/ uint16(xSetOp), uint16(JNP),
	/*5482*/ uint16(xReadCw),
	/*5483*/ uint16(xArgRel16),
	/*5484*/ uint16(xMatch),
	/*5485*/ uint16(xSetOp), uint16(JNP),
	/*5487*/ uint16(xReadCd),
	/*5488*/ uint16(xArgRel32),
	/*5489*/ uint16(xMatch),
	/*5490*/ uint16(xCondDataSize), 5494, 5485, 5499,
	/*5494*/ uint16(xSetOp), uint16(JNP),
	/*5496*/ uint16(xReadCd),
	/*5497*/ uint16(xArgRel32),
	/*5498*/ uint16(xMatch),
	/*5499*/ uint16(xSetOp), uint16(JNP),
	/*5501*/ uint16(xReadCd),
	/*5502*/ uint16(xArgRel32),
	/*5503*/ uint16(xMatch),
	/*5504*/ uint16(xCondIs64), 5507, 5521,
	/*5507*/ uint16(xCondDataSize), 5511, 5516, 0,
	/*5511*/ uint16(xSetOp), uint16(JL),
	/*5513*/ uint16(xReadCw),
	/*5514*/ uint16(xArgRel16),
	/*5515*/ uint16(xMatch),
	/*5516*/ uint16(xSetOp), uint16(JL),
	/*5518*/ uint16(xReadCd),
	/*5519*/ uint16(xArgRel32),
	/*5520*/ uint16(xMatch),
	/*5521*/ uint16(xCondDataSize), 5525, 5516, 5530,
	/*5525*/ uint16(xSetOp), uint16(JL),
	/*5527*/ uint16(xReadCd),
	/*5528*/ uint16(xArgRel32),
	/*5529*/ uint16(xMatch),
	/*5530*/ uint16(xSetOp), uint16(JL),
	/*5532*/ uint16(xReadCd),
	/*5533*/ uint16(xArgRel32),
	/*5534*/ uint16(xMatch),
	/*5535*/ uint16(xCondIs64), 5538, 5552,
	/*5538*/ uint16(xCondDataSize), 5542, 5547, 0,
	/*5542*/ uint16(xSetOp), uint16(JGE),
	/*5544*/ uint16(xReadCw),
	/*5545*/ uint16(xArgRel16),
	/*5546*/ uint16(xMatch),
	/*5547*/ uint16(xSetOp), uint16(JGE),
	/*5549*/ uint16(xReadCd),
	/*5550*/ uint16(xArgRel32),
	/*5551*/ uint16(xMatch),
	/*5552*/ uint16(xCondDataSize), 5556, 5547, 5561,
	/*5556*/ uint16(xSetOp), uint16(JGE),
	/*5558*/ uint16(xReadCd),
	/*5559*/ uint16(xArgRel32),
	/*5560*/ uint16(xMatch),
	/*5561*/ uint16(xSetOp), uint16(JGE),
	/*5563*/ uint16(xReadCd),
	/*5564*/ uint16(xArgRel32),
	/*5565*/ uint16(xMatch),
	/*5566*/ uint16(xCondIs64), 5569, 5583,
	/*5569*/ uint16(xCondDataSize), 5573, 5578, 0,
	/*5573*/ uint16(xSetOp), uint16(JLE),
	/*5575*/ uint16(xReadCw),
	/*5576*/ uint16(xArgRel16),
	/*5577*/ uint16(xMatch),
	/*5578*/ uint16(xSetOp), uint16(JLE),
	/*5580*/ uint16(xReadCd),
	/*5581*/ uint16(xArgRel32),
	/*5582*/ uint16(xMatch),
	/*5583*/ uint16(xCondDataSize), 5587, 5578, 5592,
	/*5587*/ uint16(xSetOp), uint16(JLE),
	/*5589*/ uint16(xReadCd),
	/*5590*/ uint16(xArgRel32),
	/*5591*/ uint16(xMatch),
	/*5592*/ uint16(xSetOp), uint16(JLE),
	/*5594*/ uint16(xReadCd),
	/*5595*/ uint16(xArgRel32),
	/*5596*/ uint16(xMatch),
	/*5597*/ uint16(xCondIs64), 5600, 5614,
	/*5600*/ uint16(xCondDataSize), 5604, 5609, 0,
	/*5604*/ uint16(xSetOp), uint16(JG),
	/*5606*/ uint16(xReadCw),
	/*5607*/ uint16(xArgRel16),
	/*5608*/ uint16(xMatch),
	/*5609*/ uint16(xSetOp), uint16(JG),
	/*5611*/ uint16(xReadCd),
	/*5612*/ uint16(xArgRel32),
	/*5613*/ uint16(xMatch),
	/*5614*/ uint16(xCondDataSize), 5618, 5609, 5623,
	/*5618*/ uint16(xSetOp), uint16(JG),
	/*5620*/ uint16(xReadCd),
	/*5621*/ uint16(xArgRel32),
	/*5622*/ uint16(xMatch),
	/*5623*/ uint16(xSetOp), uint16(JG),
	/*5625*/ uint16(xReadCd),
	/*5626*/ uint16(xArgRel32),
	/*5627*/ uint16(xMatch),
	/*5628*/ uint16(xSetOp), uint16(SETO),
	/*5630*/ uint16(xReadSlashR),
	/*5631*/ uint16(xArgRM8),
	/*5632*/ uint16(xMatch),
	/*5633*/ uint16(xSetOp), uint16(SETNO),
	/*5635*/ uint16(xReadSlashR),
	/*5636*/ uint16(xArgRM8),
	/*5637*/ uint16(xMatch),
	/*5638*/ uint16(xSetOp), uint16(SETB),
	/*5640*/ uint16(xReadSlashR),
	/*5641*/ uint16(xArgRM8),
	/*5642*/ uint16(xMatch),
	/*5643*/ uint16(xSetOp), uint16(SETAE),
	/*5645*/ uint16(xReadSlashR),
	/*5646*/ uint16(xArgRM8),
	/*5647*/ uint16(xMatch),
	/*5648*/ uint16(xSetOp), uint16(SETE),
	/*5650*/ uint16(xReadSlashR),
	/*5651*/ uint16(xArgRM8),
	/*5652*/ uint16(xMatch),
	/*5653*/ uint16(xSetOp), uint16(SETNE),
	/*5655*/ uint16(xReadSlashR),
	/*5656*/ uint16(xArgRM8),
	/*5657*/ uint16(xMatch),
	/*5658*/ uint16(xSetOp), uint16(SETBE),
	/*5660*/ uint16(xReadSlashR),
	/*5661*/ uint16(xArgRM8),
	/*5662*/ uint16(xMatch),
	/*5663*/ uint16(xSetOp), uint16(SETA),
	/*5665*/ uint16(xReadSlashR),
	/*5666*/ uint16(xArgRM8),
	/*5667*/ uint16(xMatch),
	/*5668*/ uint16(xSetOp), uint16(SETS),
	/*5670*/ uint16(xReadSlashR),
	/*5671*/ uint16(xArgRM8),
	/*5672*/ uint16(xMatch),
	/*5673*/ uint16(xSetOp), uint16(SETNS),
	/*5675*/ uint16(xReadSlashR),
	/*5676*/ uint16(xArgRM8),
	/*5677*/ uint16(xMatch),
	/*5678*/ uint16(xSetOp), uint16(SETP),
	/*5680*/ uint16(xReadSlashR),
	/*5681*/ uint16(xArgRM8),
	/*5682*/ uint16(xMatch),
	/*5683*/ uint16(xSetOp), uint16(SETNP),
	/*5685*/ uint16(xReadSlashR),
	/*5686*/ uint16(xArgRM8),
	/*5687*/ uint16(xMatch),
	/*5688*/ uint16(xSetOp), uint16(SETL),
	/*5690*/ uint16(xReadSlashR),
	/*5691*/ uint16(xArgRM8),
	/*5692*/ uint16(xMatch),
	/*5693*/ uint16(xSetOp), uint16(SETGE),
	/*5695*/ uint16(xReadSlashR),
	/*5696*/ uint16(xArgRM8),
	/*5697*/ uint16(xMatch),
	/*5698*/ uint16(xSetOp), uint16(SETLE),
	/*5700*/ uint16(xReadSlashR),
	/*5701*/ uint16(xArgRM8),
	/*5702*/ uint16(xMatch),
	/*5703*/ uint16(xSetOp), uint16(SETG),
	/*5705*/ uint16(xReadSlashR),
	/*5706*/ uint16(xArgRM8),
	/*5707*/ uint16(xMatch),
	/*5708*/ uint16(xSetOp), uint16(PUSH),
	/*5710*/ uint16(xArgFS),
	/*5711*/ uint16(xMatch),
	/*5712*/ uint16(xCondIs64), 5715, 5727,
	/*5715*/ uint16(xCondDataSize), 5719, 5723, 0,
	/*5719*/ uint16(xSetOp), uint16(POP),
	/*5721*/ uint16(xArgFS),
	/*5722*/ uint16(xMatch),
	/*5723*/ uint16(xSetOp), uint16(POP),
	/*5725*/ uint16(xArgFS),
	/*5726*/ uint16(xMatch),
	/*5727*/ uint16(xCondDataSize), 5719, 5731, 5735,
	/*5731*/ uint16(xSetOp), uint16(POP),
	/*5733*/ uint16(xArgFS),
	/*5734*/ uint16(xMatch),
	/*5735*/ uint16(xSetOp), uint16(POP),
	/*5737*/ uint16(xArgFS),
	/*5738*/ uint16(xMatch),
	/*5739*/ uint16(xSetOp), uint16(CPUID),
	/*5741*/ uint16(xMatch),
	/*5742*/ uint16(xCondIs64), 5745, 5761,
	/*5745*/ uint16(xCondDataSize), 5749, 5755, 0,
	/*5749*/ uint16(xSetOp), uint16(BT),
	/*5751*/ uint16(xReadSlashR),
	/*5752*/ uint16(xArgRM16),
	/*5753*/ uint16(xArgR16),
	/*5754*/ uint16(xMatch),
	/*5755*/ uint16(xSetOp), uint16(BT),
	/*5757*/ uint16(xReadSlashR),
	/*5758*/ uint16(xArgRM32),
	/*5759*/ uint16(xArgR32),
	/*5760*/ uint16(xMatch),
	/*5761*/ uint16(xCondDataSize), 5749, 5755, 5765,
	/*5765*/ uint16(xSetOp), uint16(BT),
	/*5767*/ uint16(xReadSlashR),
	/*5768*/ uint16(xArgRM64),
	/*5769*/ uint16(xArgR64),
	/*5770*/ uint16(xMatch),
	/*5771*/ uint16(xCondIs64), 5774, 5794,
	/*5774*/ uint16(xCondDataSize), 5778, 5786, 0,
	/*5778*/ uint16(xSetOp), uint16(SHLD),
	/*5780*/ uint16(xReadSlashR),
	/*5781*/ uint16(xReadIb),
	/*5782*/ uint16(xArgRM16),
	/*5783*/ uint16(xArgR16),
	/*5784*/ uint16(xArgImm8u),
	/*5785*/ uint16(xMatch),
	/*5786*/ uint16(xSetOp), uint16(SHLD),
	/*5788*/ uint16(xReadSlashR),
	/*5789*/ uint16(xReadIb),
	/*5790*/ uint16(xArgRM32),
	/*5791*/ uint16(xArgR32),
	/*5792*/ uint16(xArgImm8u),
	/*5793*/ uint16(xMatch),
	/*5794*/ uint16(xCondDataSize), 5778, 5786, 5798,
	/*5798*/ uint16(xSetOp), uint16(SHLD),
	/*5800*/ uint16(xReadSlashR),
	/*5801*/ uint16(xReadIb),
	/*5802*/ uint16(xArgRM64),
	/*5803*/ uint16(xArgR64),
	/*5804*/ uint16(xArgImm8u),
	/*5805*/ uint16(xMatch),
	/*5806*/ uint16(xCondIs64), 5809, 5827,
	/*5809*/ uint16(xCondDataSize), 5813, 5820, 0,
	/*5813*/ uint16(xSetOp), uint16(SHLD),
	/*5815*/ uint16(xReadSlashR),
	/*5816*/ uint16(xArgRM16),
	/*5817*/ uint16(xArgR16),
	/*5818*/ uint16(xArgCL),
	/*5819*/ uint16(xMatch),
	/*5820*/ uint16(xSetOp), uint16(SHLD),
	/*5822*/ uint16(xReadSlashR),
	/*5823*/ uint16(xArgRM32),
	/*5824*/ uint16(xArgR32),
	/*5825*/ uint16(xArgCL),
	/*5826*/ uint16(xMatch),
	/*5827*/ uint16(xCondDataSize), 5813, 5820, 5831,
	/*5831*/ uint16(xSetOp), uint16(SHLD),
	/*5833*/ uint16(xReadSlashR),
	/*5834*/ uint16(xArgRM64),
	/*5835*/ uint16(xArgR64),
	/*5836*/ uint16(xArgCL),
	/*5837*/ uint16(xMatch),
	/*5838*/ uint16(xSetOp), uint16(PUSH),
	/*5840*/ uint16(xArgGS),
	/*5841*/ uint16(xMatch),
	/*5842*/ uint16(xCondIs64), 5845, 5857,
	/*5845*/ uint16(xCondDataSize), 5849, 5853, 0,
	/*5849*/ uint16(xSetOp), uint16(POP),
	/*5851*/ uint16(xArgGS),
	/*5852*/ uint16(xMatch),
	/*5853*/ uint16(xSetOp), uint16(POP),
	/*5855*/ uint16(xArgGS),
	/*5856*/ uint16(xMatch),
	/*5857*/ uint16(xCondDataSize), 5849, 5861, 5865,
	/*5861*/ uint16(xSetOp), uint16(POP),
	/*5863*/ uint16(xArgGS),
	/*5864*/ uint16(xMatch),
	/*5865*/ uint16(xSetOp), uint16(POP),
	/*5867*/ uint16(xArgGS),
	/*5868*/ uint16(xMatch),
	/*5869*/ uint16(xSetOp), uint16(RSM),
	/*5871*/ uint16(xMatch),
	/*5872*/ uint16(xCondIs64), 5875, 5891,
	/*5875*/ uint16(xCondDataSize), 5879, 5885, 0,
	/*5879*/ uint16(xSetOp), uint16(BTS),
	/*5881*/ uint16(xReadSlashR),
	/*5882*/ uint16(xArgRM16),
	/*5883*/ uint16(xArgR16),
	/*5884*/ uint16(xMatch),
	/*5885*/ uint16(xSetOp), uint16(BTS),
	/*5887*/ uint16(xReadSlashR),
	/*5888*/ uint16(xArgRM32),
	/*5889*/ uint16(xArgR32),
	/*5890*/ uint16(xMatch),
	/*5891*/ uint16(xCondDataSize), 5879, 5885, 5895,
	/*5895*/ uint16(xSetOp), uint16(BTS),
	/*5897*/ uint16(xReadSlashR),
	/*5898*/ uint16(xArgRM64),
	/*5899*/ uint16(xArgR64),
	/*5900*/ uint16(xMatch),
	/*5901*/ uint16(xCondIs64), 5904, 5924,
	/*5904*/ uint16(xCondDataSize), 5908, 5916, 0,
	/*5908*/ uint16(xSetOp), uint16(SHRD),
	/*5910*/ uint16(xReadSlashR),
	/*5911*/ uint16(xReadIb),
	/*5912*/ uint16(xArgRM16),
	/*5913*/ uint16(xArgR16),
	/*5914*/ uint16(xArgImm8u),
	/*5915*/ uint16(xMatch),
	/*5916*/ uint16(xSetOp), uint16(SHRD),
	/*5918*/ uint16(xReadSlashR),
	/*5919*/ uint16(xReadIb),
	/*5920*/ uint16(xArgRM32),
	/*5921*/ uint16(xArgR32),
	/*5922*/ uint16(xArgImm8u),
	/*5923*/ uint16(xMatch),
	/*5924*/ uint16(xCondDataSize), 5908, 5916, 5928,
	/*5928*/ uint16(xSetOp), uint16(SHRD),
	/*5930*/ uint16(xReadSlashR),
	/*5931*/ uint16(xReadIb),
	/*5932*/ uint16(xArgRM64),
	/*5933*/ uint16(xArgR64),
	/*5934*/ uint16(xArgImm8u),
	/*5935*/ uint16(xMatch),
	/*5936*/ uint16(xCondIs64), 5939, 5957,
	/*5939*/ uint16(xCondDataSize), 5943, 5950, 0,
	/*5943*/ uint16(xSetOp), uint16(SHRD),
	/*5945*/ uint16(xReadSlashR),
	/*5946*/ uint16(xArgRM16),
	/*5947*/ uint16(xArgR16),
	/*5948*/ uint16(xArgCL),
	/*5949*/ uint16(xMatch),
	/*5950*/ uint16(xSetOp), uint16(SHRD),
	/*5952*/ uint16(xReadSlashR),
	/*5953*/ uint16(xArgRM32),
	/*5954*/ uint16(xArgR32),
	/*5955*/ uint16(xArgCL),
	/*5956*/ uint16(xMatch),
	/*5957*/ uint16(xCondDataSize), 5943, 5950, 5961,
	/*5961*/ uint16(xSetOp), uint16(SHRD),
	/*5963*/ uint16(xReadSlashR),
	/*5964*/ uint16(xArgRM64),
	/*5965*/ uint16(xArgR64),
	/*5966*/ uint16(xArgCL),
	/*5967*/ uint16(xMatch),
	/*5968*/ uint16(xCondByte), 3,
	0xE8, 6217,
	0xF0, 6220,
	0xF8, 6223,
	/*5976*/ uint16(xCondSlashR),
	5985, // 0
	6039, // 1
	6093, // 2
	6122, // 3
	6151, // 4
	6174, // 5
	6197, // 6
	6213, // 7
	/*5985*/ uint16(xCondIs64), 5988, 6000,
	/*5988*/ uint16(xCondDataSize), 5992, 5996, 0,
	/*5992*/ uint16(xSetOp), uint16(FXSAVE),
	/*5994*/ uint16(xArgM512byte),
	/*5995*/ uint16(xMatch),
	/*5996*/ uint16(xSetOp), uint16(FXSAVE),
	/*5998*/ uint16(xArgM512byte),
	/*5999*/ uint16(xMatch),
	/*6000*/ uint16(xCondPrefix), 2,
	0xF3, 6014,
	0x0, 6006,
	/*6006*/ uint16(xCondDataSize), 5992, 5996, 6010,
	/*6010*/ uint16(xSetOp), uint16(FXSAVE64),
	/*6012*/ uint16(xArgM512byte),
	/*6013*/ uint16(xMatch),
	/*6014*/ uint16(xCondDataSize), 6018, 6025, 6032,
	/*6018*/ uint16(xCondIsMem), 6021, 0,
	/*6021*/ uint16(xSetOp), uint16(RDFSBASE),
	/*6023*/ uint16(xArgRM32),
	/*6024*/ uint16(xMatch),
	/*6025*/ uint16(xCondIsMem), 6028, 0,
	/*6028*/ uint16(xSetOp), uint16(RDFSBASE),
	/*6030*/ uint16(xArgRM32),
	/*6031*/ uint16(xMatch),
	/*6032*/ uint16(xCondIsMem), 6035, 0,
	/*6035*/ uint16(xSetOp), uint16(RDFSBASE),
	/*6037*/ uint16(xArgRM64),
	/*6038*/ uint16(xMatch),
	/*6039*/ uint16(xCondIs64), 6042, 6054,
	/*6042*/ uint16(xCondDataSize), 6046, 6050, 0,
	/*6046*/ uint16(xSetOp), uint16(FXRSTOR),
	/*6048*/ uint16(xArgM512byte),
	/*6049*/ uint16(xMatch),
	/*6050*/ uint16(xSetOp), uint16(FXRSTOR),
	/*6052*/ uint16(xArgM512byte),
	/*6053*/ uint16(xMatch),
	/*6054*/ uint16(xCondPrefix), 2,
	0xF3, 6068,
	0x0, 6060,
	/*6060*/ uint16(xCondDataSize), 6046, 6050, 6064,
	/*6064*/ uint16(xSetOp), uint16(FXRSTOR64),
	/*6066*/ uint16(xArgM512byte),
	/*6067*/ uint16(xMatch),
	/*6068*/ uint16(xCondDataSize), 6072, 6079, 6086,
	/*6072*/ uint16(xCondIsMem), 6075, 0,
	/*6075*/ uint16(xSetOp), uint16(RDGSBASE),
	/*6077*/ uint16(xArgRM32),
	/*6078*/ uint16(xMatch),
	/*6079*/ uint16(xCondIsMem), 6082, 0,
	/*6082*/ uint16(xSetOp), uint16(RDGSBASE),
	/*6084*/ uint16(xArgRM32),
	/*6085*/ uint16(xMatch),
	/*6086*/ uint16(xCondIsMem), 6089, 0,
	/*6089*/ uint16(xSetOp), uint16(RDGSBASE),
	/*6091*/ uint16(xArgRM64),
	/*6092*/ uint16(xMatch),
	/*6093*/ uint16(xCondIs64), 6096, 6100,
	/*6096*/ uint16(xSetOp), uint16(LDMXCSR),
	/*6098*/ uint16(xArgM32),
	/*6099*/ uint16(xMatch),
	/*6100*/ uint16(xCondPrefix), 2,
	0xF3, 6106,
	0x0, 6096,
	/*6106*/ uint16(xCondDataSize), 6110, 6114, 6118,
	/*6110*/ uint16(xSetOp), uint16(WRFSBASE),
	/*6112*/ uint16(xArgRM32),
	/*6113*/ uint16(xMatch),
	/*6114*/ uint16(xSetOp), uint16(WRFSBASE),
	/*6116*/ uint16(xArgRM32),
	/*6117*/ uint16(xMatch),
	/*6118*/ uint16(xSetOp), uint16(WRFSBASE),
	/*6120*/ uint16(xArgRM64),
	/*6121*/ uint16(xMatch),
	/*6122*/ uint16(xCondIs64), 6125, 6129,
	/*6125*/ uint16(xSetOp), uint16(STMXCSR),
	/*6127*/ uint16(xArgM32),
	/*6128*/ uint16(xMatch),
	/*6129*/ uint16(xCondPrefix), 2,
	0xF3, 6135,
	0x0, 6125,
	/*6135*/ uint16(xCondDataSize), 6139, 6143, 6147,
	/*6139*/ uint16(xSetOp), uint16(WRGSBASE),
	/*6141*/ uint16(xArgRM32),
	/*6142*/ uint16(xMatch),
	/*6143*/ uint16(xSetOp), uint16(WRGSBASE),
	/*6145*/ uint16(xArgRM32),
	/*6146*/ uint16(xMatch),
	/*6147*/ uint16(xSetOp), uint16(WRGSBASE),
	/*6149*/ uint16(xArgRM64),
	/*6150*/ uint16(xMatch),
	/*6151*/ uint16(xCondIs64), 6154, 6166,
	/*6154*/ uint16(xCondDataSize), 6158, 6162, 0,
	/*6158*/ uint16(xSetOp), uint16(XSAVE),
	/*6160*/ uint16(xArgMem),
	/*6161*/ uint16(xMatch),
	/*6162*/ uint16(xSetOp), uint16(XSAVE),
	/*6164*/ uint16(xArgMem),
	/*6165*/ uint16(xMatch),
	/*6166*/ uint16(xCondDataSize), 6158, 6162, 6170,
	/*6170*/ uint16(xSetOp), uint16(XSAVE64),
	/*6172*/ uint16(xArgMem),
	/*6173*/ uint16(xMatch),
	/*6174*/ uint16(xCondIs64), 6177, 6189,
	/*6177*/ uint16(xCondDataSize), 6181, 6185, 0,
	/*6181*/ uint16(xSetOp), uint16(XRSTOR),
	/*6183*/ uint16(xArgMem),
	/*6184*/ uint16(xMatch),
	/*6185*/ uint16(xSetOp), uint16(XRSTOR),
	/*6187*/ uint16(xArgMem),
	/*6188*/ uint16(xMatch),
	/*6189*/ uint16(xCondDataSize), 6181, 6185, 6193,
	/*6193*/ uint16(xSetOp), uint16(XRSTOR64),
	/*6195*/ uint16(xArgMem),
	/*6196*/ uint16(xMatch),
	/*6197*/ uint16(xCondDataSize), 6201, 6205, 6209,
	/*6201*/ uint16(xSetOp), uint16(XSAVEOPT),
	/*6203*/ uint16(xArgMem),
	/*6204*/ uint16(xMatch),
	/*6205*/ uint16(xSetOp), uint16(XSAVEOPT),
	/*6207*/ uint16(xArgMem),
	/*6208*/ uint16(xMatch),
	/*6209*/ uint16(xSetOp), uint16(XSAVEOPT64),
	/*6211*/ uint16(xArgMem),
	/*6212*/ uint16(xMatch),
	/*6213*/ uint16(xSetOp), uint16(CLFLUSH),
	/*6215*/ uint16(xArgM8),
	/*6216*/ uint16(xMatch),
	/*6217*/ uint16(xSetOp), uint16(LFENCE),
	/*6219*/ uint16(xMatch),
	/*6220*/ uint16(xSetOp), uint16(MFENCE),
	/*6222*/ uint16(xMatch),
	/*6223*/ uint16(xSetOp), uint16(SFENCE),
	/*6225*/ uint16(xMatch),
	/*6226*/ uint16(xCondIs64), 6229, 6245,
	/*6229*/ uint16(xCondDataSize), 6233, 6239, 0,
	/*6233*/ uint16(xSetOp), uint16(IMUL),
	/*6235*/ uint16(xReadSlashR),
	/*6236*/ uint16(xArgR16),
	/*6237*/ uint16(xArgRM16),
	/*6238*/ uint16(xMatch),
	/*6239*/ uint16(xSetOp), uint16(IMUL),
	/*6241*/ uint16(xReadSlashR),
	/*6242*/ uint16(xArgR32),
	/*6243*/ uint16(xArgRM32),
	/*6244*/ uint16(xMatch),
	/*6245*/ uint16(xCondDataSize), 6233, 6239, 6249,
	/*6249*/ uint16(xSetOp), uint16(IMUL),
	/*6251*/ uint16(xReadSlashR),
	/*6252*/ uint16(xArgR64),
	/*6253*/ uint16(xArgRM64),
	/*6254*/ uint16(xMatch),
	/*6255*/ uint16(xSetOp), uint16(CMPXCHG),
	/*6257*/ uint16(xReadSlashR),
	/*6258*/ uint16(xArgRM8),
	/*6259*/ uint16(xArgR8),
	/*6260*/ uint16(xMatch),
	/*6261*/ uint16(xCondIs64), 6264, 6280,
	/*6264*/ uint16(xCondDataSize), 6268, 6274, 0,
	/*6268*/ uint16(xSetOp), uint16(CMPXCHG),
	/*6270*/ uint16(xReadSlashR),
	/*6271*/ uint16(xArgRM16),
	/*6272*/ uint16(xArgR16),
	/*6273*/ uint16(xMatch),
	/*6274*/ uint16(xSetOp), uint16(CMPXCHG),
	/*6276*/ uint16(xReadSlashR),
	/*6277*/ uint16(xArgRM32),
	/*6278*/ uint16(xArgR32),
	/*6279*/ uint16(xMatch),
	/*6280*/ uint16(xCondDataSize), 6268, 6274, 6284,
	/*6284*/ uint16(xSetOp), uint16(CMPXCHG),
	/*6286*/ uint16(xReadSlashR),
	/*6287*/ uint16(xArgRM64),
	/*6288*/ uint16(xArgR64),
	/*6289*/ uint16(xMatch),
	/*6290*/ uint16(xCondIs64), 6293, 6309,
	/*6293*/ uint16(xCondDataSize), 6297, 6303, 0,
	/*6297*/ uint16(xSetOp), uint16(LSS),
	/*6299*/ uint16(xReadSlashR),
	/*6300*/ uint16(xArgR16),
	/*6301*/ uint16(xArgM16colon16),
	/*6302*/ uint16(xMatch),
	/*6303*/ uint16(xSetOp), uint16(LSS),
	/*6305*/ uint16(xReadSlashR),
	/*6306*/ uint16(xArgR32),
	/*6307*/ uint16(xArgM16colon32),
	/*6308*/ uint16(xMatch),
	/*6309*/ uint16(xCondDataSize), 6297, 6303, 6313,
	/*6313*/ uint16(xSetOp), uint16(LSS),
	/*6315*/ uint16(xReadSlashR),
	/*6316*/ uint16(xArgR64),
	/*6317*/ uint16(xArgM16colon64),
	/*6318*/ uint16(xMatch),
	/*6319*/ uint16(xCondIs64), 6322, 6338,
	/*6322*/ uint16(xCondDataSize), 6326, 6332, 0,
	/*6326*/ uint16(xSetOp), uint16(BTR),
	/*6328*/ uint16(xReadSlashR),
	/*6329*/ uint16(xArgRM16),
	/*6330*/ uint16(xArgR16),
	/*6331*/ uint16(xMatch),
	/*6332*/ uint16(xSetOp), uint16(BTR),
	/*6334*/ uint16(xReadSlashR),
	/*6335*/ uint16(xArgRM32),
	/*6336*/ uint16(xArgR32),
	/*6337*/ uint16(xMatch),
	/*6338*/ uint16(xCondDataSize), 6326, 6332, 6342,
	/*6342*/ uint16(xSetOp), uint16(BTR),
	/*6344*/ uint16(xReadSlashR),
	/*6345*/ uint16(xArgRM64),
	/*6346*/ uint16(xArgR64),
	/*6347*/ uint16(xMatch),
	/*6348*/ uint16(xCondIs64), 6351, 6367,
	/*6351*/ uint16(xCondDataSize), 6355, 6361, 0,
	/*6355*/ uint16(xSetOp), uint16(LFS),
	/*6357*/ uint16(xReadSlashR),
	/*6358*/ uint16(xArgR16),
	/*6359*/ uint16(xArgM16colon16),
	/*6360*/ uint16(xMatch),
	/*6361*/ uint16(xSetOp), uint16(LFS),
	/*6363*/ uint16(xReadSlashR),
	/*6364*/ uint16(xArgR32),
	/*6365*/ uint16(xArgM16colon32),
	/*6366*/ uint16(xMatch),
	/*6367*/ uint16(xCondDataSize), 6355, 6361, 6371,
	/*6371*/ uint16(xSetOp), uint16(LFS),
	/*6373*/ uint16(xReadSlashR),
	/*6374*/ uint16(xArgR64),
	/*6375*/ uint16(xArgM16colon64),
	/*6376*/ uint16(xMatch),
	/*6377*/ uint16(xCondIs64), 6380, 6396,
	/*6380*/ uint16(xCondDataSize), 6384, 6390, 0,
	/*6384*/ uint16(xSetOp), uint16(LGS),
	/*6386*/ uint16(xReadSlashR),
	/*6387*/ uint16(xArgR16),
	/*6388*/ uint16(xArgM16colon16),
	/*6389*/ uint16(xMatch),
	/*6390*/ uint16(xSetOp), uint16(LGS),
	/*6392*/ uint16(xReadSlashR),
	/*6393*/ uint16(xArgR32),
	/*6394*/ uint16(xArgM16colon32),
	/*6395*/ uint16(xMatch),
	/*6396*/ uint16(xCondDataSize), 6384, 6390, 6400,
	/*6400*/ uint16(xSetOp), uint16(LGS),
	/*6402*/ uint16(xReadSlashR),
	/*6403*/ uint16(xArgR64),
	/*6404*/ uint16(xArgM16colon64),
	/*6405*/ uint16(xMatch),
	/*6406*/ uint16(xCondIs64), 6409, 6425,
	/*6409*/ uint16(xCondDataSize), 6413, 6419, 0,
	/*6413*/ uint16(xSetOp), uint16(MOVZX),
	/*6415*/ uint16(xReadSlashR),
	/*6416*/ uint16(xArgR16),
	/*6417*/ uint16(xArgRM8),
	/*6418*/ uint16(xMatch),
	/*6419*/ uint16(xSetOp), uint16(MOVZX),
	/*6421*/ uint16(xReadSlashR),
	/*6422*/ uint16(xArgR32),
	/*6423*/ uint16(xArgRM8),
	/*6424*/ uint16(xMatch),
	/*6425*/ uint16(xCondDataSize), 6413, 6419, 6429,
	/*6429*/ uint16(xSetOp), uint16(MOVZX),
	/*6431*/ uint16(xReadSlashR),
	/*6432*/ uint16(xArgR64),
	/*6433*/ uint16(xArgRM8),
	/*6434*/ uint16(xMatch),
	/*6435*/ uint16(xCondIs64), 6438, 6454,
	/*6438*/ uint16(xCondDataSize), 6442, 6448, 0,
	/*6442*/ uint16(xSetOp), uint16(MOVZX),
	/*6444*/ uint16(xReadSlashR),
	/*6445*/ uint16(xArgR16),
	/*6446*/ uint16(xArgRM16),
	/*6447*/ uint16(xMatch),
	/*6448*/ uint16(xSetOp), uint16(MOVZX),
	/*6450*/ uint16(xReadSlashR),
	/*6451*/ uint16(xArgR32),
	/*6452*/ uint16(xArgRM16),
	/*6453*/ uint16(xMatch),
	/*6454*/ uint16(xCondDataSize), 6442, 6448, 6458,
	/*6458*/ uint16(xSetOp), uint16(MOVZX),
	/*6460*/ uint16(xReadSlashR),
	/*6461*/ uint16(xArgR64),
	/*6462*/ uint16(xArgRM16),
	/*6463*/ uint16(xMatch),
	/*6464*/ uint16(xCondIs64), 6467, 6487,
	/*6467*/ uint16(xCondPrefix), 1,
	0xF3, 6471,
	/*6471*/ uint16(xCondDataSize), 6475, 6481, 0,
	/*6475*/ uint16(xSetOp), uint16(POPCNT),
	/*6477*/ uint16(xReadSlashR),
	/*6478*/ uint16(xArgR16),
	/*6479*/ uint16(xArgRM16),
	/*6480*/ uint16(xMatch),
	/*6481*/ uint16(xSetOp), uint16(POPCNT),
	/*6483*/ uint16(xReadSlashR),
	/*6484*/ uint16(xArgR32),
	/*6485*/ uint16(xArgRM32),
	/*6486*/ uint16(xMatch),
	/*6487*/ uint16(xCondPrefix), 1,
	0xF3, 6491,
	/*6491*/ uint16(xCondDataSize), 6475, 6481, 6495,
	/*6495*/ uint16(xSetOp), uint16(POPCNT),
	/*6497*/ uint16(xReadSlashR),
	/*6498*/ uint16(xArgR64),
	/*6499*/ uint16(xArgRM64),
	/*6500*/ uint16(xMatch),
	/*6501*/ uint16(xCondDataSize), 0, 6505, 0,
	/*6505*/ uint16(xSetOp), uint16(UD1),
	/*6507*/ uint16(xReadSlashR),
	/*6508*/ uint16(xArgR32),
	/*6509*/ uint16(xArgRM32),
	/*6510*/ uint16(xMatch),
	/*6511*/ uint16(xCondSlashR),
	0,    // 0
	0,    // 1
	0,    // 2
	0,    // 3
	6520, // 4
	6549, // 5
	6578, // 6
	6607, // 7
	/*6520*/ uint16(xCondIs64), 6523, 6539,
	/*6523*/ uint16(xCondDataSize), 6527, 6533, 0,
	/*6527*/ uint16(xSetOp), uint16(BT),
	/*6529*/ uint16(xReadIb),
	/*6530*/ uint16(xArgRM16),
	/*6531*/ uint16(xArgImm8u),
	/*6532*/ uint16(xMatch),
	/*6533*/ uint16(xSetOp), uint16(BT),
	/*6535*/ uint16(xReadIb),
	/*6536*/ uint16(xArgRM32),
	/*6537*/ uint16(xArgImm8u),
	/*6538*/ uint16(xMatch),
	/*6539*/ uint16(xCondDataSize), 6527, 6533, 6543,
	/*6543*/ uint16(xSetOp), uint16(BT),
	/*6545*/ uint16(xReadIb),
	/*6546*/ uint16(xArgRM64),
	/*6547*/ uint16(xArgImm8u),
	/*6548*/ uint16(xMatch),
	/*6549*/ uint16(xCondIs64), 6552, 6568,
	/*6552*/ uint16(xCondDataSize), 6556, 6562, 0,
	/*6556*/ uint16(xSetOp), uint16(BTS),
	/*6558*/ uint16(xReadIb),
	/*6559*/ uint16(xArgRM16),
	/*6560*/ uint16(xArgImm8u),
	/*6561*/ uint16(xMatch),
	/*6562*/ uint16(xSetOp), uint16(BTS),
	/*6564*/ uint16(xReadIb),
	/*6565*/ uint16(xArgRM32),
	/*6566*/ uint16(xArgImm8u),
	/*6567*/ uint16(xMatch),
	/*6568*/ uint16(xCondDataSize), 6556, 6562, 6572,
	/*6572*/ uint16(xSetOp), uint16(BTS),
	/*6574*/ uint16(xReadIb),
	/*6575*/ uint16(xArgRM64),
	/*6576*/ uint16(xArgImm8u),
	/*6577*/ uint16(xMatch),
	/*6578*/ uint16(xCondIs64), 6581, 6597,
	/*6581*/ uint16(xCondDataSize), 6585, 6591, 0,
	/*6585*/ uint16(xSetOp), uint16(BTR),
	/*6587*/ uint16(xReadIb),
	/*6588*/ uint16(xArgRM16),
	/*6589*/ uint16(xArgImm8u),
	/*6590*/ uint16(xMatch),
	/*6591*/ uint16(xSetOp), uint16(BTR),
	/*6593*/ uint16(xReadIb),
	/*6594*/ uint16(xArgRM32),
	/*6595*/ uint16(xArgImm8u),
	/*6596*/ uint16(xMatch),
	/*6597*/ uint16(xCondDataSize), 6585, 6591, 6601,
	/*6601*/ uint16(xSetOp), uint16(BTR),
	/*6603*/ uint16(xReadIb),
	/*6604*/ uint16(xArgRM64),
	/*6605*/ uint16(xArgImm8u),
	/*6606*/ uint16(xMatch),
	/*6607*/ uint16(xCondIs64), 6610, 6626,
	/*6610*/ uint16(xCondDataSize), 6614, 6620, 0,
	/*6614*/ uint16(xSetOp), uint16(BTC),
	/*6616*/ uint16(xReadIb),
	/*6617*/ uint16(xArgRM16),
	/*6618*/ uint16(xArgImm8u),
	/*6619*/ uint16(xMatch),
	/*6620*/ uint16(xSetOp), uint16(BTC),
	/*6622*/ uint16(xReadIb),
	/*6623*/ uint16(xArgRM32),
	/*6624*/ uint16(xArgImm8u),
	/*6625*/ uint16(xMatch),
	/*6626*/ uint16(xCondDataSize), 6614, 6620, 6630,
	/*6630*/ uint16(xSetOp), uint16(BTC),
	/*6632*/ uint16(xReadIb),
	/*6633*/ uint16(xArgRM64),
	/*6634*/ uint16(xArgImm8u),
	/*6635*/ uint16(xMatch),
	/*6636*/ uint16(xCondIs64), 6639, 6655,
	/*6639*/ uint16(xCondDataSize), 6643, 6649, 0,
	/*6643*/ uint16(xSetOp), uint16(BTC),
	/*6645*/ uint16(xReadSlashR),
	/*6646*/ uint16(xArgRM16),
	/*6647*/ uint16(xArgR16),
	/*6648*/ uint16(xMatch),
	/*6649*/ uint16(xSetOp), uint16(BTC),
	/*6651*/ uint16(xReadSlashR),
	/*6652*/ uint16(xArgRM32),
	/*6653*/ uint16(xArgR32),
	/*6654*/ uint16(xMatch),
	/*6655*/ uint16(xCondDataSize), 6643, 6649, 6659,
	/*6659*/ uint16(xSetOp), uint16(BTC),
	/*6661*/ uint16(xReadSlashR),
	/*6662*/ uint16(xArgRM64),
	/*6663*/ uint16(xArgR64),
	/*6664*/ uint16(xMatch),
	/*6665*/ uint16(xCondIs64), 6668, 6706,
	/*6668*/ uint16(xCondPrefix), 2,
	0xF3, 6690,
	0x0, 6674,
	/*6674*/ uint16(xCondDataSize), 6678, 6684, 0,
	/*6678*/ uint16(xSetOp), uint16(BSF),
	/*6680*/ uint16(xReadSlashR),
	/*6681*/ uint16(xArgR16),
	/*6682*/ uint16(xArgRM16),
	/*6683*/ uint16(xMatch),
	/*6684*/ uint16(xSetOp), uint16(BSF),
	/*6686*/ uint16(xReadSlashR),
	/*6687*/ uint16(xArgR32),
	/*6688*/ uint16(xArgRM32),
	/*6689*/ uint16(xMatch),
	/*6690*/ uint16(xCondDataSize), 6694, 6700, 0,
	/*6694*/ uint16(xSetOp), uint16(TZCNT),
	/*6696*/ uint16(xReadSlashR),
	/*6697*/ uint16(xArgR16),
	/*6698*/ uint16(xArgRM16),
	/*6699*/ uint16(xMatch),
	/*6700*/ uint16(xSetOp), uint16(TZCNT),
	/*6702*/ uint16(xReadSlashR),
	/*6703*/ uint16(xArgR32),
	/*6704*/ uint16(xArgRM32),
	/*6705*/ uint16(xMatch),
	/*6706*/ uint16(xCondPrefix), 2,
	0xF3, 6722,
	0x0, 6712,
	/*6712*/ uint16(xCondDataSize), 6678, 6684, 6716,
	/*6716*/ uint16(xSetOp), uint16(BSF),
	/*6718*/ uint16(xReadSlashR),
	/*6719*/ uint16(xArgR64),
	/*6720*/ uint16(xArgRM64),
	/*6721*/ uint16(xMatch),
	/*6722*/ uint16(xCondDataSize), 6694, 6700, 6726,
	/*6726*/ uint16(xSetOp), uint16(TZCNT),
	/*6728*/ uint16(xReadSlashR),
	/*6729*/ uint16(xArgR64),
	/*6730*/ uint16(xArgRM64),
	/*6731*/ uint16(xMatch),
	/*6732*/ uint16(xCondIs64), 6735, 6773,
	/*6735*/ uint16(xCondPrefix), 2,
	0xF3, 6757,
	0x0, 6741,
	/*6741*/ uint16(xCondDataSize), 6745, 6751, 0,
	/*6745*/ uint16(xSetOp), uint16(BSR),
	/*6747*/ uint16(xReadSlashR),
	/*6748*/ uint16(xArgR16),
	/*6749*/ uint16(xArgRM16),
	/*6750*/ uint16(xMatch),
	/*6751*/ uint16(xSetOp), uint16(BSR),
	/*6753*/ uint16(xReadSlashR),
	/*6754*/ uint16(xArgR32),
	/*6755*/ uint16(xArgRM32),
	/*6756*/ uint16(xMatch),
	/*6757*/ uint16(xCondDataSize), 6761, 6767, 0,
	/*6761*/ uint16(xSetOp), uint16(LZCNT),
	/*6763*/ uint16(xReadSlashR),
	/*6764*/ uint16(xArgR16),
	/*6765*/ uint16(xArgRM16),
	/*6766*/ uint16(xMatch),
	/*6767*/ uint16(xSetOp), uint16(LZCNT),
	/*6769*/ uint16(xReadSlashR),
	/*6770*/ uint16(xArgR32),
	/*6771*/ uint16(xArgRM32),
	/*6772*/ uint16(xMatch),
	/*6773*/ uint16(xCondPrefix), 2,
	0xF3, 6789,
	0x0, 6779,
	/*6779*/ uint16(xCondDataSize), 6745, 6751, 6783,
	/*6783*/ uint16(xSetOp), uint16(BSR),
	/*6785*/ uint16(xReadSlashR),
	/*6786*/ uint16(xArgR64),
	/*6787*/ uint16(xArgRM64),
	/*6788*/ uint16(xMatch),
	/*6789*/ uint16(xCondDataSize), 6761, 6767, 6793,
	/*6793*/ uint16(xSetOp), uint16(LZCNT),
	/*6795*/ uint16(xReadSlashR),
	/*6796*/ uint16(xArgR64),
	/*6797*/ uint16(xArgRM64),
	/*6798*/ uint16(xMatch),
	/*6799*/ uint16(xCondIs64), 6802, 6818,
	/*6802*/ uint16(xCondDataSize), 6806, 6812, 0,
	/*6806*/ uint16(xSetOp), uint16(MOVSX),
	/*6808*/ uint16(xReadSlashR),
	/*6809*/ uint16(xArgR16),
	/*6810*/ uint16(xArgRM8),
	/*6811*/ uint16(xMatch),
	/*6812*/ uint16(xSetOp), uint16(MOVSX),
	/*6814*/ uint16(xReadSlashR),
	/*6815*/ uint16(xArgR32),
	/*6816*/ uint16(xArgRM8),
	/*6817*/ uint16(xMatch),
	/*6818*/ uint16(xCondDataSize), 6806, 6812, 6822,
	/*6822*/ uint16(xSetOp), uint16(MOVSX),
	/*6824*/ uint16(xReadSlashR),
	/*6825*/ uint16(xArgR64),
	/*6826*/ uint16(xArgRM8),
	/*6827*/ uint16(xMatch),
	/*6828*/ uint16(xCondIs64), 6831, 6847,
	/*6831*/ uint16(xCondDataSize), 6835, 6841, 0,
	/*6835*/ uint16(xSetOp), uint16(MOVSX),
	/*6837*/ uint16(xReadSlashR),
	/*6838*/ uint16(xArgR16),
	/*6839*/ uint16(xArgRM16),
	/*6840*/ uint16(xMatch),
	/*6841*/ uint16(xSetOp), uint16(MOVSX),
	/*6843*/ uint16(xReadSlashR),
	/*6844*/ uint16(xArgR32),
	/*6845*/ uint16(xArgRM16),
	/*6846*/ uint16(xMatch),
	/*6847*/ uint16(xCondDataSize), 6835, 6841, 6851,
	/*6851*/ uint16(xSetOp), uint16(MOVSX),
	/*6853*/ uint16(xReadSlashR),
	/*6854*/ uint16(xArgR64),
	/*6855*/ uint16(xArgRM16),
	/*6856*/ uint16(xMatch),
	/*6857*/ uint16(xSetOp), uint16(XADD),
	/*6859*/ uint16(xReadSlashR),
	/*6860*/ uint16(xArgRM8),
	/*6861*/ uint16(xArgR8),
	/*6862*/ uint16(xMatch),
	/*6863*/ uint16(xCondIs64), 6866, 6882,
	/*6866*/ uint16(xCondDataSize), 6870, 6876, 0,
	/*6870*/ uint16(xSetOp), uint16(XADD),
	/*6872*/ uint16(xReadSlashR),
	/*6873*/ uint16(xArgRM16),
	/*6874*/ uint16(xArgR16),
	/*6875*/ uint16(xMatch),
	/*6876*/ uint16(xSetOp), uint16(XADD),
	/*6878*/ uint16(xReadSlashR),
	/*6879*/ uint16(xArgRM32),
	/*6880*/ uint16(xArgR32),
	/*6881*/ uint16(xMatch),
	/*6882*/ uint16(xCondDataSize), 6870, 6876, 6886,
	/*6886*/ uint16(xSetOp), uint16(XADD),
	/*6888*/ uint16(xReadSlashR),
	/*6889*/ uint16(xArgRM64),
	/*6890*/ uint16(xArgR64),
	/*6891*/ uint16(xMatch),
	/*6892*/ uint16(xCondPrefix), 4,
	0xF3, 6926,
	0xF2, 6918,
	0x66, 6910,
	0x0, 6902,
	/*6902*/ uint16(xSetOp), uint16(CMPPS),
	/*6904*/ uint16(xReadSlashR),
	/*6905*/ uint16(xReadIb),
	/*6906*/ uint16(xArgXmm1),
	/*6907*/ uint16(xArgXmm2M128),
	/*6908*/ uint16(xArgImm8u),
	/*6909*/ uint16(xMatch),
	/*6910*/ uint16(xSetOp), uint16(CMPPD),
	/*6912*/ uint16(xReadSlashR),
	/*6913*/ uint16(xReadIb),
	/*6914*/ uint16(xArgXmm1),
	/*6915*/ uint16(xArgXmm2M128),
	/*6916*/ uint16(xArgImm8u),
	/*6917*/ uint16(xMatch),
	/*6918*/ uint16(xSetOp), uint16(CMPSD_XMM),
	/*6920*/ uint16(xReadSlashR),
	/*6921*/ uint16(xReadIb),
	/*6922*/ uint16(xArgXmm1),
	/*6923*/ uint16(xArgXmm2M64),
	/*6924*/ uint16(xArgImm8u),
	/*6925*/ uint16(xMatch),
	/*6926*/ uint16(xSetOp), uint16(CMPSS),
	/*6928*/ uint16(xReadSlashR),
	/*6929*/ uint16(xReadIb),
	/*6930*/ uint16(xArgXmm1),
	/*6931*/ uint16(xArgXmm2M32),
	/*6932*/ uint16(xArgImm8u),
	/*6933*/ uint16(xMatch),
	/*6934*/ uint16(xCondIs64), 6937, 6953,
	/*6937*/ uint16(xCondDataSize), 6941, 6947, 0,
	/*6941*/ uint16(xSetOp), uint16(MOVNTI),
	/*6943*/ uint16(xReadSlashR),
	/*6944*/ uint16(xArgM32),
	/*6945*/ uint16(xArgR32),
	/*6946*/ uint16(xMatch),
	/*6947*/ uint16(xSetOp), uint16(MOVNTI),
	/*6949*/ uint16(xReadSlashR),
	/*6950*/ uint16(xArgM32),
	/*6951*/ uint16(xArgR32),
	/*6952*/ uint16(xMatch),
	/*6953*/ uint16(xCondDataSize), 6941, 6947, 6957,
	/*6957*/ uint16(xSetOp), uint16(MOVNTI),
	/*6959*/ uint16(xReadSlashR),
	/*6960*/ uint16(xArgM64),
	/*6961*/ uint16(xArgR64),
	/*6962*/ uint16(xMatch),
	/*6963*/ uint16(xCondPrefix), 2,
	0x66, 6977,
	0x0, 6969,
	/*6969*/ uint16(xSetOp), uint16(PINSRW),
	/*6971*/ uint16(xReadSlashR),
	/*6972*/ uint16(xReadIb),
	/*6973*/ uint16(xArgMm),
	/*6974*/ uint16(xArgR32M16),
	/*6975*/ uint16(xArgImm8u),
	/*6976*/ uint16(xMatch),
	/*6977*/ uint16(xSetOp), uint16(PINSRW),
	/*6979*/ uint16(xReadSlashR),
	/*6980*/ uint16(xReadIb),
	/*6981*/ uint16(xArgXmm),
	/*6982*/ uint16(xArgR32M16),
	/*6983*/ uint16(xArgImm8u),
	/*6984*/ uint16(xMatch),
	/*6985*/ uint16(xCondPrefix), 2,
	0x66, 6999,
	0x0, 6991,
	/*6991*/ uint16(xSetOp), uint16(PEXTRW),
	/*6993*/ uint16(xReadSlashR),
	/*6994*/ uint16(xReadIb),
	/*6995*/ uint16(xArgR32),
	/*6996*/ uint16(xArgMm2),
	/*6997*/ uint16(xArgImm8u),
	/*6998*/ uint16(xMatch),
	/*6999*/ uint16(xSetOp), uint16(PEXTRW),
	/*7001*/ uint16(xReadSlashR),
	/*7002*/ uint16(xReadIb),
	/*7003*/ uint16(xArgR32),
	/*7004*/ uint16(xArgXmm2),
	/*7005*/ uint16(xArgImm8u),
	/*7006*/ uint16(xMatch),
	/*7007*/ uint16(xCondPrefix), 2,
	0x66, 7021,
	0x0, 7013,
	/*7013*/ uint16(xSetOp), uint16(SHUFPS),
	/*7015*/ uint16(xReadSlashR),
	/*7016*/ uint16(xReadIb),
	/*7017*/ uint16(xArgXmm1),
	/*7018*/ uint16(xArgXmm2M128),
	/*7019*/ uint16(xArgImm8u),
	/*7020*/ uint16(xMatch),
	/*7021*/ uint16(xSetOp), uint16(SHUFPD),
	/*7023*/ uint16(xReadSlashR),
	/*7024*/ uint16(xReadIb),
	/*7025*/ uint16(xArgXmm1),
	/*7026*/ uint16(xArgXmm2M128),
	/*7027*/ uint16(xArgImm8u),
	/*7028*/ uint16(xMatch),
	/*7029*/ uint16(xCondSlashR),
	0,    // 0
	7038, // 1
	0,    // 2
	7061, // 3
	7084, // 4
	7107, // 5
	7130, // 6
	0,    // 7
	/*7038*/ uint16(xCondIs64), 7041, 7053,
	/*7041*/ uint16(xCondDataSize), 7045, 7049, 0,
	/*7045*/ uint16(xSetOp), uint16(CMPXCHG8B),
	/*7047*/ uint16(xArgM64),
	/*7048*/ uint16(xMatch),
	/*7049*/ uint16(xSetOp), uint16(CMPXCHG8B),
	/*7051*/ uint16(xArgM64),
	/*7052*/ uint16(xMatch),
	/*7053*/ uint16(xCondDataSize), 7045, 7049, 7057,
	/*7057*/ uint16(xSetOp), uint16(CMPXCHG16B),
	/*7059*/ uint16(xArgM128),
	/*7060*/ uint16(xMatch),
	/*7061*/ uint16(xCondIs64), 7064, 7076,
	/*7064*/ uint16(xCondDataSize), 7068, 7072, 0,
	/*7068*/ uint16(xSetOp), uint16(XRSTORS),
	/*7070*/ uint16(xArgMem),
	/*7071*/ uint16(xMatch),
	/*7072*/ uint16(xSetOp), uint16(XRSTORS),
	/*7074*/ uint16(xArgMem),
	/*7075*/ uint16(xMatch),
	/*7076*/ uint16(xCondDataSize), 7068, 7072, 7080,
	/*7080*/ uint16(xSetOp), uint16(XRSTORS64),
	/*7082*/ uint16(xArgMem),
	/*7083*/ uint16(xMatch),
	/*7084*/ uint16(xCondIs64), 7087, 7099,
	/*7087*/ uint16(xCondDataSize), 7091, 7095, 0,
	/*7091*/ uint16(xSetOp), uint16(XSAVEC),
	/*7093*/ uint16(xArgMem),
	/*7094*/ uint16(xMatch),
	/*7095*/ uint16(xSetOp), uint16(XSAVEC),
	/*7097*/ uint16(xArgMem),
	/*7098*/ uint16(xMatch),
	/*7099*/ uint16(xCondDataSize), 7091, 7095, 7103,
	/*7103*/ uint16(xSetOp), uint16(XSAVEC64),
	/*7105*/ uint16(xArgMem),
	/*7106*/ uint16(xMatch),
	/*7107*/ uint16(xCondIs64), 7110, 7122,
	/*7110*/ uint16(xCondDataSize), 7114, 7118, 0,
	/*7114*/ uint16(xSetOp), uint16(XSAVES),
	/*7116*/ uint16(xArgMem),
	/*7117*/ uint16(xMatch),
	/*7118*/ uint16(xSetOp), uint16(XSAVES),
	/*7120*/ uint16(xArgMem),
	/*7121*/ uint16(xMatch),
	/*7122*/ uint16(xCondDataSize), 7114, 7118, 7126,
	/*7126*/ uint16(xSetOp), uint16(XSAVES64),
	/*7128*/ uint16(xArgMem),
	/*7129*/ uint16(xMatch),
	/*7130*/ uint16(xCondIs64), 7133, 7151,
	/*7133*/ uint16(xCondDataSize), 7137, 7144, 0,
	/*7137*/ uint16(xCondIsMem), 7140, 0,
	/*7140*/ uint16(xSetOp), uint16(RDRAND),
	/*7142*/ uint16(xArgRmf16),
	/*7143*/ uint16(xMatch),
	/*7144*/ uint16(xCondIsMem), 7147, 0,
	/*7147*/ uint16(xSetOp), uint16(RDRAND),
	/*7149*/ uint16(xArgRmf32),
	/*7150*/ uint16(xMatch),
	/*7151*/ uint16(xCondDataSize), 7137, 7144, 7155,
	/*7155*/ uint16(xSetOp), uint16(RDRAND),
	/*7157*/ uint16(xMatch),
	/*7158*/ uint16(xCondIs64), 7161, 7173,
	/*7161*/ uint16(xCondDataSize), 7165, 7169, 0,
	/*7165*/ uint16(xSetOp), uint16(BSWAP),
	/*7167*/ uint16(xArgR16op),
	/*7168*/ uint16(xMatch),
	/*7169*/ uint16(xSetOp), uint16(BSWAP),
	/*7171*/ uint16(xArgR32op),
	/*7172*/ uint16(xMatch),
	/*7173*/ uint16(xCondDataSize), 7165, 7169, 7177,
	/*7177*/ uint16(xSetOp), uint16(BSWAP),
	/*7179*/ uint16(xArgR64op),
	/*7180*/ uint16(xMatch),
	/*7181*/ uint16(xCondPrefix), 2,
	0xF2, 7193,
	0x66, 7187,
	/*7187*/ uint16(xSetOp), uint16(ADDSUBPD),
	/*7189*/ uint16(xReadSlashR),
	/*7190*/ uint16(xArgXmm1),
	/*7191*/ uint16(xArgXmm2M128),
	/*7192*/ uint16(xMatch),
	/*7193*/ uint16(xSetOp), uint16(ADDSUBPS),
	/*7195*/ uint16(xReadSlashR),
	/*7196*/ uint16(xArgXmm1),
	/*7197*/ uint16(xArgXmm2M128),
	/*7198*/ uint16(xMatch),
	/*7199*/ uint16(xCondPrefix), 2,
	0x66, 7211,
	0x0, 7205,
	/*7205*/ uint16(xSetOp), uint16(PSRLW),
	/*7207*/ uint16(xReadSlashR),
	/*7208*/ uint16(xArgMm),
	/*7209*/ uint16(xArgMmM64),
	/*7210*/ uint16(xMatch),
	/*7211*/ uint16(xSetOp), uint16(PSRLW),
	/*7213*/ uint16(xReadSlashR),
	/*7214*/ uint16(xArgXmm1),
	/*7215*/ uint16(xArgXmm2M128),
	/*7216*/ uint16(xMatch),
	/*7217*/ uint16(xCondPrefix), 2,
	0x66, 7229,
	0x0, 7223,
	/*7223*/ uint16(xSetOp), uint16(PSRLD),
	/*7225*/ uint16(xReadSlashR),
	/*7226*/ uint16(xArgMm),
	/*7227*/ uint16(xArgMmM64),
	/*7228*/ uint16(xMatch),
	/*7229*/ uint16(xSetOp), uint16(PSRLD),
	/*7231*/ uint16(xReadSlashR),
	/*7232*/ uint16(xArgXmm1),
	/*7233*/ uint16(xArgXmm2M128),
	/*7234*/ uint16(xMatch),
	/*7235*/ uint16(xCondPrefix), 2,
	0x66, 7247,
	0x0, 7241,
	/*7241*/ uint16(xSetOp), uint16(PSRLQ),
	/*7243*/ uint16(xReadSlashR),
	/*7244*/ uint16(xArgMm),
	/*7245*/ uint16(xArgMmM64),
	/*7246*/ uint16(xMatch),
	/*7247*/ uint16(xSetOp), uint16(PSRLQ),
	/*7249*/ uint16(xReadSlashR),
	/*7250*/ uint16(xArgXmm1),
	/*7251*/ uint16(xArgXmm2M128),
	/*7252*/ uint16(xMatch),
	/*7253*/ uint16(xCondPrefix), 2,
	0x66, 7265,
	0x0, 7259,
	/*7259*/ uint16(xSetOp), uint16(PADDQ),
	/*7261*/ uint16(xReadSlashR),
	/*7262*/ uint16(xArgMm1),
	/*7263*/ uint16(xArgMm2M64),
	/*7264*/ uint16(xMatch),
	/*7265*/ uint16(xSetOp), uint16(PADDQ),
	/*7267*/ uint16(xReadSlashR),
	/*7268*/ uint16(xArgXmm1),
	/*7269*/ uint16(xArgXmm2M128),
	/*7270*/ uint16(xMatch),
	/*7271*/ uint16(xCondPrefix), 2,
	0x66, 7283,
	0x0, 7277,
	/*7277*/ uint16(xSetOp), uint16(PMULLW),
	/*7279*/ uint16(xReadSlashR),
	/*7280*/ uint16(xArgMm),
	/*7281*/ uint16(xArgMmM64),
	/*7282*/ uint16(xMatch),
	/*7283*/ uint16(xSetOp), uint16(PMULLW),
	/*7285*/ uint16(xReadSlashR),
	/*7286*/ uint16(xArgXmm1),
	/*7287*/ uint16(xArgXmm2M128),
	/*7288*/ uint16(xMatch),
	/*7289*/ uint16(xCondPrefix), 3,
	0xF3, 7309,
	0xF2, 7303,
	0x66, 7297,
	/*7297*/ uint16(xSetOp), uint16(MOVQ),
	/*7299*/ uint16(xReadSlashR),
	/*7300*/ uint16(xArgXmm2M64),
	/*7301*/ uint16(xArgXmm1),
	/*7302*/ uint16(xMatch),
	/*7303*/ uint16(xSetOp), uint16(MOVDQ2Q),
	/*7305*/ uint16(xReadSlashR),
	/*7306*/ uint16(xArgMm),
	/*7307*/ uint16(xArgXmm2),
	/*7308*/ uint16(xMatch),
	/*7309*/ uint16(xSetOp), uint16(MOVQ2DQ),
	/*7311*/ uint16(xReadSlashR),
	/*7312*/ uint16(xArgXmm1),
	/*7313*/ uint16(xArgMm2),
	/*7314*/ uint16(xMatch),
	/*7315*/ uint16(xCondPrefix), 2,
	0x66, 7327,
	0x0, 7321,
	/*7321*/ uint16(xSetOp), uint16(PMOVMSKB),
	/*7323*/ uint16(xReadSlashR),
	/*7324*/ uint16(xArgR32),
	/*7325*/ uint16(xArgMm2),
	/*7326*/ uint16(xMatch),
	/*7327*/ uint16(xSetOp), uint16(PMOVMSKB),
	/*7329*/ uint16(xReadSlashR),
	/*7330*/ uint16(xArgR32),
	/*7331*/ uint16(xArgXmm2),
	/*7332*/ uint16(xMatch),
	/*7333*/ uint16(xCondPrefix), 2,
	0x66, 7345,
	0x0, 7339,
	/*7339*/ uint16(xSetOp), uint16(PSUBUSB),
	/*7341*/ uint16(xReadSlashR),
	/*7342*/ uint16(xArgMm),
	/*7343*/ uint16(xArgMmM64),
	/*7344*/ uint16(xMatch),
	/*7345*/ uint16(xSetOp), uint16(PSUBUSB),
	/*7347*/ uint16(xReadSlashR),
	/*7348*/ uint16(xArgXmm1),
	/*7349*/ uint16(xArgXmm2M128),
	/*7350*/ uint16(xMatch),
	/*7351*/ uint16(xCondPrefix), 2,
	0x66, 7363,
	0x0, 7357,
	/*7357*/ uint16(xSetOp), uint16(PSUBUSW),
	/*7359*/ uint16(xReadSlashR),
	/*7360*/ uint16(xArgMm),
	/*7361*/ uint16(xArgMmM64),
	/*7362*/ uint16(xMatch),
	/*7363*/ uint16(xSetOp), uint16(PSUBUSW),
	/*7365*/ uint16(xReadSlashR),
	/*7366*/ uint16(xArgXmm1),
	/*7367*/ uint16(xArgXmm2M128),
	/*7368*/ uint16(xMatch),
	/*7369*/ uint16(xCondPrefix), 2,
	0x66, 7381,
	0x0, 7375,
	/*7375*/ uint16(xSetOp), uint16(PMINUB),
	/*7377*/ uint16(xReadSlashR),
	/*7378*/ uint16(xArgMm1),
	/*7379*/ uint16(xArgMm2M64),
	/*7380*/ uint16(xMatch),
	/*7381*/ uint16(xSetOp), uint16(PMINUB),
	/*7383*/ uint16(xReadSlashR),
	/*7384*/ uint16(xArgXmm1),
	/*7385*/ uint16(xArgXmm2M128),
	/*7386*/ uint16(xMatch),
	/*7387*/ uint16(xCondPrefix), 2,
	0x66, 7399,
	0x0, 7393,
	/*7393*/ uint16(xSetOp), uint16(PAND),
	/*7395*/ uint16(xReadSlashR),
	/*7396*/ uint16(xArgMm),
	/*7397*/ uint16(xArgMmM64),
	/*7398*/ uint16(xMatch),
	/*7399*/ uint16(xSetOp), uint16(PAND),
	/*7401*/ uint16(xReadSlashR),
	/*7402*/ uint16(xArgXmm1),
	/*7403*/ uint16(xArgXmm2M128),
	/*7404*/ uint16(xMatch),
	/*7405*/ uint16(xCondPrefix), 2,
	0x66, 7417,
	0x0, 7411,
	/*7411*/ uint16(xSetOp), uint16(PADDUSB),
	/*7413*/ uint16(xReadSlashR),
	/*7414*/ uint16(xArgMm),
	/*7415*/ uint16(xArgMmM64),
	/*7416*/ uint16(xMatch),
	/*7417*/ uint16(xSetOp), uint16(PADDUSB),
	/*7419*/ uint16(xReadSlashR),
	/*7420*/ uint16(xArgXmm1),
	/*7421*/ uint16(xArgXmm2M128),
	/*7422*/ uint16(xMatch),
	/*7423*/ uint16(xCondPrefix), 2,
	0x66, 7435,
	0x0, 7429,
	/*7429*/ uint16(xSetOp), uint16(PADDUSW),
	/*7431*/ uint16(xReadSlashR),
	/*7432*/ uint16(xArgMm),
	/*7433*/ uint16(xArgMmM64),
	/*7434*/ uint16(xMatch),
	/*7435*/ uint16(xSetOp), uint16(PADDUSW),
	/*7437*/ uint16(xReadSlashR),
	/*7438*/ uint16(xArgXmm1),
	/*7439*/ uint16(xArgXmm2M128),
	/*7440*/ uint16(xMatch),
	/*7441*/ uint16(xCondPrefix), 2,
	0x66, 7453,
	0x0, 7447,
	/*7447*/ uint16(xSetOp), uint16(PMAXUB),
	/*7449*/ uint16(xReadSlashR),
	/*7450*/ uint16(xArgMm1),
	/*7451*/ uint16(xArgMm2M64),
	/*7452*/ uint16(xMatch),
	/*7453*/ uint16(xSetOp), uint16(PMAXUB),
	/*7455*/ uint16(xReadSlashR),
	/*7456*/ uint16(xArgXmm1),
	/*7457*/ uint16(xArgXmm2M128),
	/*7458*/ uint16(xMatch),
	/*7459*/ uint16(xCondPrefix), 2,
	0x66, 7471,
	0x0, 7465,
	/*7465*/ uint16(xSetOp), uint16(PANDN),
	/*7467*/ uint16(xReadSlashR),
	/*7468*/ uint16(xArgMm),
	/*7469*/ uint16(xArgMmM64),
	/*7470*/ uint16(xMatch),
	/*7471*/ uint16(xSetOp), uint16(PANDN),
	/*7473*/ uint16(xReadSlashR),
	/*7474*/ uint16(xArgXmm1),
	/*7475*/ uint16(xArgXmm2M128),
	/*7476*/ uint16(xMatch),
	/*7477*/ uint16(xCondPrefix), 2,
	0x66, 7489,
	0x0, 7483,
	/*7483*/ uint16(xSetOp), uint16(PAVGB),
	/*7485*/ uint16(xReadSlashR),
	/*7486*/ uint16(xArgMm1),
	/*7487*/ uint16(xArgMm2M64),
	/*7488*/ uint16(xMatch),
	/*7489*/ uint16(xSetOp), uint16(PAVGB),
	/*7491*/ uint16(xReadSlashR),
	/*7492*/ uint16(xArgXmm1),
	/*7493*/ uint16(xArgXmm2M128),
	/*7494*/ uint16(xMatch),
	/*7495*/ uint16(xCondPrefix), 2,
	0x66, 7507,
	0x0, 7501,
	/*7501*/ uint16(xSetOp), uint16(PSRAW),
	/*7503*/ uint16(xReadSlashR),
	/*7504*/ uint16(xArgMm),
	/*7505*/ uint16(xArgMmM64),
	/*7506*/ uint16(xMatch),
	/*7507*/ uint16(xSetOp), uint16(PSRAW),
	/*7509*/ uint16(xReadSlashR),
	/*7510*/ uint16(xArgXmm1),
	/*7511*/ uint16(xArgXmm2M128),
	/*7512*/ uint16(xMatch),
	/*7513*/ uint16(xCondPrefix), 2,
	0x66, 7525,
	0x0, 7519,
	/*7519*/ uint16(xSetOp), uint16(PSRAD),
	/*7521*/ uint16(xReadSlashR),
	/*7522*/ uint16(xArgMm),
	/*7523*/ uint16(xArgMmM64),
	/*7524*/ uint16(xMatch),
	/*7525*/ uint16(xSetOp), uint16(PSRAD),
	/*7527*/ uint16(xReadSlashR),
	/*7528*/ uint16(xArgXmm1),
	/*7529*/ uint16(xArgXmm2M128),
	/*7530*/ uint16(xMatch),
	/*7531*/ uint16(xCondPrefix), 2,
	0x66, 7543,
	0x0, 7537,
	/*7537*/ uint16(xSetOp), uint16(PAVGW),
	/*7539*/ uint16(xReadSlashR),
	/*7540*/ uint16(xArgMm1),
	/*7541*/ uint16(xArgMm2M64),
	/*7542*/ uint16(xMatch),
	/*7543*/ uint16(xSetOp), uint16(PAVGW),
	/*7545*/ uint16(xReadSlashR),
	/*7546*/ uint16(xArgXmm1),
	/*7547*/ uint16(xArgXmm2M128),
	/*7548*/ uint16(xMatch),
	/*7549*/ uint16(xCondPrefix), 2,
	0x66, 7561,
	0x0, 7555,
	/*7555*/ uint16(xSetOp), uint16(PMULHUW),
	/*7557*/ uint16(xReadSlashR),
	/*7558*/ uint16(xArgMm1),
	/*7559*/ uint16(xArgMm2M64),
	/*7560*/ uint16(xMatch),
	/*7561*/ uint16(xSetOp), uint16(PMULHUW),
	/*7563*/ uint16(xReadSlashR),
	/*7564*/ uint16(xArgXmm1),
	/*7565*/ uint16(xArgXmm2M128),
	/*7566*/ uint16(xMatch),
	/*7567*/ uint16(xCondPrefix), 2,
	0x66, 7579,
	0x0, 7573,
	/*7573*/ uint16(xSetOp), uint16(PMULHW),
	/*7575*/ uint16(xReadSlashR),
	/*7576*/ uint16(xArgMm),
	/*7577*/ uint16(xArgMmM64),
	/*7578*/ uint16(xMatch),
	/*7579*/ uint16(xSetOp), uint16(PMULHW),
	/*7581*/ uint16(xReadSlashR),
	/*7582*/ uint16(xArgXmm1),
	/*7583*/ uint16(xArgXmm2M128),
	/*7584*/ uint16(xMatch),
	/*7585*/ uint16(xCondPrefix), 3,
	0xF3, 7605,
	0xF2, 7599,
	0x66, 7593,
	/*7593*/ uint16(xSetOp), uint16(CVTTPD2DQ),
	/*7595*/ uint16(xReadSlashR),
	/*7596*/ uint16(xArgXmm1),
	/*7597*/ uint16(xArgXmm2M128),
	/*7598*/ uint16(xMatch),
	/*7599*/ uint16(xSetOp), uint16(CVTPD2DQ),
	/*7601*/ uint16(xReadSlashR),
	/*7602*/ uint16(xArgXmm1),
	/*7603*/ uint16(xArgXmm2M128),
	/*7604*/ uint16(xMatch),
	/*7605*/ uint16(xSetOp), uint16(CVTDQ2PD),
	/*7607*/ uint16(xReadSlashR),
	/*7608*/ uint16(xArgXmm1),
	/*7609*/ uint16(xArgXmm2M64),
	/*7610*/ uint16(xMatch),
	/*7611*/ uint16(xCondPrefix), 2,
	0x66, 7623,
	0x0, 7617,
	/*7617*/ uint16(xSetOp), uint16(MOVNTQ),
	/*7619*/ uint16(xReadSlashR),
	/*7620*/ uint16(xArgM64),
	/*7621*/ uint16(xArgMm),
	/*7622*/ uint16(xMatch),
	/*7623*/ uint16(xSetOp), uint16(MOVNTDQ),
	/*7625*/ uint16(xReadSlashR),
	/*7626*/ uint16(xArgM128),
	/*7627*/ uint16(xArgXmm),
	/*7628*/ uint16(xMatch),
	/*7629*/ uint16(xCondPrefix), 2,
	0x66, 7641,
	0x0, 7635,
	/*7635*/ uint16(xSetOp), uint16(PSUBSB),
	/*7637*/ uint16(xReadSlashR),
	/*7638*/ uint16(xArgMm),
	/*7639*/ uint16(xArgMmM64),
	/*7640*/ uint16(xMatch),
	/*7641*/ uint16(xSetOp), uint16(PSUBSB),
	/*7643*/ uint16(xReadSlashR),
	/*7644*/ uint16(xArgXmm1),
	/*7645*/ uint16(xArgXmm2M128),
	/*7646*/ uint16(xMatch),
	/*7647*/ uint16(xCondPrefix), 2,
	0x66, 7659,
	0x0, 7653,
	/*7653*/ uint16(xSetOp), uint16(PSUBSW),
	/*7655*/ uint16(xReadSlashR),
	/*7656*/ uint16(xArgMm),
	/*7657*/ uint16(xArgMmM64),
	/*7658*/ uint16(xMatch),
	/*7659*/ uint16(xSetOp), uint16(PSUBSW),
	/*7661*/ uint16(xReadSlashR),
	/*7662*/ uint16(xArgXmm1),
	/*7663*/ uint16(xArgXmm2M128),
	/*7664*/ uint16(xMatch),
	/*7665*/ uint16(xCondPrefix), 2,
	0x66, 7677,
	0x0, 7671,
	/*7671*/ uint16(xSetOp), uint16(PMINSW),
	/*7673*/ uint16(xReadSlashR),
	/*7674*/ uint16(xArgMm1),
	/*7675*/ uint16(xArgMm2M64),
	/*7676*/ uint16(xMatch),
	/*7677*/ uint16(xSetOp), uint16(PMINSW),
	/*7679*/ uint16(xReadSlashR),
	/*7680*/ uint16(xArgXmm1),
	/*7681*/ uint16(xArgXmm2M128),
	/*7682*/ uint16(xMatch),
	/*7683*/ uint16(xCondPrefix), 2,
	0x66, 7695,
	0x0, 7689,
	/*7689*/ uint16(xSetOp), uint16(POR),
	/*7691*/ uint16(xReadSlashR),
	/*7692*/ uint16(xArgMm),
	/*7693*/ uint16(xArgMmM64),
	/*7694*/ uint16(xMatch),
	/*7695*/ uint16(xSetOp), uint16(POR),
	/*7697*/ uint16(xReadSlashR),
	/*7698*/ uint16(xArgXmm1),
	/*7699*/ uint16(xArgXmm2M128),
	/*7700*/ uint16(xMatch),
	/*7701*/ uint16(xCondPrefix), 2,
	0x66, 7713,
	0x0, 7707,
	/*7707*/ uint16(xSetOp), uint16(PADDSB),
	/*7709*/ uint16(xReadSlashR),
	/*7710*/ uint16(xArgMm),
	/*7711*/ uint16(xArgMmM64),
	/*7712*/ uint16(xMatch),
	/*7713*/ uint16(xSetOp), uint16(PADDSB),
	/*7715*/ uint16(xReadSlashR),
	/*7716*/ uint16(xArgXmm1),
	/*7717*/ uint16(xArgXmm2M128),
	/*7718*/ uint16(xMatch),
	/*7719*/ uint16(xCondPrefix), 2,
	0x66, 7731,
	0x0, 7725,
	/*7725*/ uint16(xSetOp), uint16(PADDSW),
	/*7727*/ uint16(xReadSlashR),
	/*7728*/ uint16(xArgMm),
	/*7729*/ uint16(xArgMmM64),
	/*7730*/ uint16(xMatch),
	/*7731*/ uint16(xSetOp), uint16(PADDSW),
	/*7733*/ uint16(xReadSlashR),
	/*7734*/ uint16(xArgXmm1),
	/*7735*/ uint16(xArgXmm2M128),
	/*7736*/ uint16(xMatch),
	/*7737*/ uint16(xCondPrefix), 2,
	0x66, 7749,
	0x0, 7743,
	/*7743*/ uint16(xSetOp), uint16(PMAXSW),
	/*7745*/ uint16(xReadSlashR),
	/*7746*/ uint16(xArgMm1),
	/*7747*/ uint16(xArgMm2M64),
	/*7748*/ uint16(xMatch),
	/*7749*/ uint16(xSetOp), uint16(PMAXSW),
	/*7751*/ uint16(xReadSlashR),
	/*7752*/ uint16(xArgXmm1),
	/*7753*/ uint16(xArgXmm2M128),
	/*7754*/ uint16(xMatch),
	/*7755*/ uint16(xCondPrefix), 2,
	0x66, 7767,
	0x0, 7761,
	/*7761*/ uint16(xSetOp), uint16(PXOR),
	/*7763*/ uint16(xReadSlashR),
	/*7764*/ uint16(xArgMm),
	/*7765*/ uint16(xArgMmM64),
	/*7766*/ uint16(xMatch),
	/*7767*/ uint16(xSetOp), uint16(PXOR),
	/*7769*/ uint16(xReadSlashR),
	/*7770*/ uint16(xArgXmm1),
	/*7771*/ uint16(xArgXmm2M128),
	/*7772*/ uint16(xMatch),
	/*7773*/ uint16(xCondPrefix), 1,
	0xF2, 7777,
	/*7777*/ uint16(xSetOp), uint16(LDDQU),
	/*7779*/ uint16(xReadSlashR),
	/*7780*/ uint16(xArgXmm1),
	/*7781*/ uint16(xArgM128),
	/*7782*/ uint16(xMatch),
	/*7783*/ uint16(xCondPrefix), 2,
	0x66, 7795,
	0x0, 7789,
	/*7789*/ uint16(xSetOp), uint16(PSLLW),
	/*7791*/ uint16(xReadSlashR),
	/*7792*/ uint16(xArgMm),
	/*7793*/ uint16(xArgMmM64),
	/*7794*/ uint16(xMatch),
	/*7795*/ uint16(xSetOp), uint16(PSLLW),
	/*7797*/ uint16(xReadSlashR),
	/*7798*/ uint16(xArgXmm1),
	/*7799*/ uint16(xArgXmm2M128),
	/*7800*/ uint16(xMatch),
	/*7801*/ uint16(xCondPrefix), 2,
	0x66, 7813,
	0x0, 7807,
	/*7807*/ uint16(xSetOp), uint16(PSLLD),
	/*7809*/ uint16(xReadSlashR),
	/*7810*/ uint16(xArgMm),
	/*7811*/ uint16(xArgMmM64),
	/*7812*/ uint16(xMatch),
	/*7813*/ uint16(xSetOp), uint16(PSLLD),
	/*7815*/ uint16(xReadSlashR),
	/*7816*/ uint16(xArgXmm1),
	/*7817*/ uint16(xArgXmm2M128),
	/*7818*/ uint16(xMatch),
	/*7819*/ uint16(xCondPrefix), 2,
	0x66, 7831,
	0x0, 7825,
	/*7825*/ uint16(xSetOp), uint16(PSLLQ),
	/*7827*/ uint16(xReadSlashR),
	/*7828*/ uint16(xArgMm),
	/*7829*/ uint16(xArgMmM64),
	/*7830*/ uint16(xMatch),
	/*7831*/ uint16(xSetOp), uint16(PSLLQ),
	/*7833*/ uint16(xReadSlashR),
	/*7834*/ uint16(xArgXmm1),
	/*7835*/ uint16(xArgXmm2M128),
	/*7836*/ uint16(xMatch),
	/*7837*/ uint16(xCondPrefix), 2,
	0x66, 7849,
	0x0, 7843,
	/*7843*/ uint16(xSetOp), uint16(PMULUDQ),
	/*7845*/ uint16(xReadSlashR),
	/*7846*/ uint16(xArgMm1),
	/*7847*/ uint16(xArgMm2M64),
	/*7848*/ uint16(xMatch),
	/*7849*/ uint16(xSetOp), uint16(PMULUDQ),
	/*7851*/ uint16(xReadSlashR),
	/*7852*/ uint16(xArgXmm1),
	/*7853*/ uint16(xArgXmm2M128),
	/*7854*/ uint16(xMatch),
	/*7855*/ uint16(xCondPrefix), 2,
	0x66, 7867,
	0x0, 7861,
	/*7861*/ uint16(xSetOp), uint16(PMADDWD),
	/*7863*/ uint16(xReadSlashR),
	/*7864*/ uint16(xArgMm),
	/*7865*/ uint16(xArgMmM64),
	/*7866*/ uint16(xMatch),
	/*7867*/ uint16(xSetOp), uint16(PMADDWD),
	/*7869*/ uint16(xReadSlashR),
	/*7870*/ uint16(xArgXmm1),
	/*7871*/ uint16(xArgXmm2M128),
	/*7872*/ uint16(xMatch),
	/*7873*/ uint16(xCondPrefix), 2,
	0x66, 7885,
	0x0, 7879,
	/*7879*/ uint16(xSetOp), uint16(PSADBW),
	/*7881*/ uint16(xReadSlashR),
	/*7882*/ uint16(xArgMm1),
	/*7883*/ uint16(xArgMm2M64),
	/*7884*/ uint16(xMatch),
	/*7885*/ uint16(xSetOp), uint16(PSADBW),
	/*7887*/ uint16(xReadSlashR),
	/*7888*/ uint16(xArgXmm1),
	/*7889*/ uint16(xArgXmm2M128),
	/*7890*/ uint16(xMatch),
	/*7891*/ uint16(xCondPrefix), 2,
	0x66, 7903,
	0x0, 7897,
	/*7897*/ uint16(xSetOp), uint16(MASKMOVQ),
	/*7899*/ uint16(xReadSlashR),
	/*7900*/ uint16(xArgMm1),
	/*7901*/ uint16(xArgMm2),
	/*7902*/ uint16(xMatch),
	/*7903*/ uint16(xSetOp), uint16(MASKMOVDQU),
	/*7905*/ uint16(xReadSlashR),
	/*7906*/ uint16(xArgXmm1),
	/*7907*/ uint16(xArgXmm2),
	/*7908*/ uint16(xMatch),
	/*7909*/ uint16(xCondPrefix), 2,
	0x66, 7921,
	0x0, 7915,
	/*7915*/ uint16(xSetOp), uint16(PSUBB),
	/*7917*/ uint16(xReadSlashR),
	/*7918*/ uint16(xArgMm),
	/*7919*/ uint16(xArgMmM64),
	/*7920*/ uint16(xMatch),
	/*7921*/ uint16(xSetOp), uint16(PSUBB),
	/*7923*/ uint16(xReadSlashR),
	/*7924*/ uint16(xArgXmm1),
	/*7925*/ uint16(xArgXmm2M128),
	/*7926*/ uint16(xMatch),
	/*7927*/ uint16(xCondPrefix), 2,
	0x66, 7939,
	0x0, 7933,
	/*7933*/ uint16(xSetOp), uint16(PSUBW),
	/*7935*/ uint16(xReadSlashR),
	/*7936*/ uint16(xArgMm),
	/*7937*/ uint16(xArgMmM64),
	/*7938*/ uint16(xMatch),
	/*7939*/ uint16(xSetOp), uint16(PSUBW),
	/*7941*/ uint16(xReadSlashR),
	/*7942*/ uint16(xArgXmm1),
	/*7943*/ uint16(xArgXmm2M128),
	/*7944*/ uint16(xMatch),
	/*7945*/ uint16(xCondPrefix), 2,
	0x66, 7957,
	0x0, 7951,
	/*7951*/ uint16(xSetOp), uint16(PSUBD),
	/*7953*/ uint16(xReadSlashR),
	/*7954*/ uint16(xArgMm),
	/*7955*/ uint16(xArgMmM64),
	/*7956*/ uint16(xMatch),
	/*7957*/ uint16(xSetOp), uint16(PSUBD),
	/*7959*/ uint16(xReadSlashR),
	/*7960*/ uint16(xArgXmm1),
	/*7961*/ uint16(xArgXmm2M128),
	/*7962*/ uint16(xMatch),
	/*7963*/ uint16(xCondPrefix), 2,
	0x66, 7975,
	0x0, 7969,
	/*7969*/ uint16(xSetOp), uint16(PSUBQ),
	/*7971*/ uint16(xReadSlashR),
	/*7972*/ uint16(xArgMm1),
	/*7973*/ uint16(xArgMm2M64),
	/*7974*/ uint16(xMatch),
	/*7975*/ uint16(xSetOp), uint16(PSUBQ),
	/*7977*/ uint16(xReadSlashR),
	/*7978*/ uint16(xArgXmm1),
	/*7979*/ uint16(xArgXmm2M128),
	/*7980*/ uint16(xMatch),
	/*7981*/ uint16(xCondPrefix), 2,
	0x66, 7993,
	0x0, 7987,
	/*7987*/ uint16(xSetOp), uint16(PADDB),
	/*7989*/ uint16(xReadSlashR),
	/*7990*/ uint16(xArgMm),
	/*7991*/ uint16(xArgMmM64),
	/*7992*/ uint16(xMatch),
	/*7993*/ uint16(xSetOp), uint16(PADDB),
	/*7995*/ uint16(xReadSlashR),
	/*7996*/ uint16(xArgXmm1),
	/*7997*/ uint16(xArgXmm2M128),
	/*7998*/ uint16(xMatch),
	/*7999*/ uint16(xCondPrefix), 2,
	0x66, 8011,
	0x0, 8005,
	/*8005*/ uint16(xSetOp), uint16(PADDW),
	/*8007*/ uint16(xReadSlashR),
	/*8008*/ uint16(xArgMm),
	/*8009*/ uint16(xArgMmM64),
	/*8010*/ uint16(xMatch),
	/*8011*/ uint16(xSetOp), uint16(PADDW),
	/*8013*/ uint16(xReadSlashR),
	/*8014*/ uint16(xArgXmm1),
	/*8015*/ uint16(xArgXmm2M128),
	/*8016*/ uint16(xMatch),
	/*8017*/ uint16(xCondPrefix), 2,
	0x66, 8029,
	0x0, 8023,
	/*8023*/ uint16(xSetOp), uint16(PADDD),
	/*8025*/ uint16(xReadSlashR),
	/*8026*/ uint16(xArgMm),
	/*8027*/ uint16(xArgMmM64),
	/*8028*/ uint16(xMatch),
	/*8029*/ uint16(xSetOp), uint16(PADDD),
	/*8031*/ uint16(xReadSlashR),
	/*8032*/ uint16(xArgXmm1),
	/*8033*/ uint16(xArgXmm2M128),
	/*8034*/ uint16(xMatch),
	/*8035*/ uint16(xCondDataSize), 0, 8039, 0,
	/*8039*/ uint16(xSetOp), uint16(UD0),
	/*8041*/ uint16(xReadSlashR),
	/*8042*/ uint16(xArgR32),
	/*8043*/ uint16(xArgRM32),
	/*8044*/ uint16(xMatch),
	/*8045*/ uint16(xSetOp), uint16(ADC),
	/*8047*/ uint16(xReadSlashR),
	/*8048*/ uint16(xArgRM8),
	/*8049*/ uint16(xArgR8),
	/*8050*/ uint16(xMatch),
	/*8051*/ uint16(xCondIs64), 8054, 8070,
	/*8054*/ uint16(xCondDataSize), 8058, 8064, 0,
	/*8058*/ uint16(xSetOp), uint16(ADC),
	/*8060*/ uint16(xReadSlashR),
	/*8061*/ uint16(xArgRM16),
	/*8062*/ uint16(xArgR16),
	/*8063*/ uint16(xMatch),
	/*8064*/ uint16(xSetOp), uint16(ADC),
	/*8066*/ uint16(xReadSlashR),
	/*8067*/ uint16(xArgRM32),
	/*8068*/ uint16(xArgR32),
	/*8069*/ uint16(xMatch),
	/*8070*/ uint16(xCondDataSize), 8058, 8064, 8074,
	/*8074*/ uint16(xSetOp), uint16(ADC),
	/*8076*/ uint16(xReadSlashR),
	/*8077*/ uint16(xArgRM64),
	/*8078*/ uint16(xArgR64),
	/*8079*/ uint16(xMatch),
	/*8080*/ uint16(xSetOp), uint16(ADC),
	/*8082*/ uint16(xReadSlashR),
	/*8083*/ uint16(xArgR8),
	/*8084*/ uint16(xArgRM8),
	/*8085*/ uint16(xMatch),
	/*8086*/ uint16(xCondIs64), 8089, 8105,
	/*8089*/ uint16(xCondDataSize), 8093, 8099, 0,
	/*8093*/ uint16(xSetOp), uint16(ADC),
	/*8095*/ uint16(xReadSlashR),
	/*8096*/ uint16(xArgR16),
	/*8097*/ uint16(xArgRM16),
	/*8098*/ uint16(xMatch),
	/*8099*/ uint16(xSetOp), uint16(ADC),
	/*8101*/ uint16(xReadSlashR),
	/*8102*/ uint16(xArgR32),
	/*8103*/ uint16(xArgRM32),
	/*8104*/ uint16(xMatch),
	/*8105*/ uint16(xCondDataSize), 8093, 8099, 8109,
	/*8109*/ uint16(xSetOp), uint16(ADC),
	/*8111*/ uint16(xReadSlashR),
	/*8112*/ uint16(xArgR64),
	/*8113*/ uint16(xArgRM64),
	/*8114*/ uint16(xMatch),
	/*8115*/ uint16(xSetOp), uint16(ADC),
	/*8117*/ uint16(xReadIb),
	/*8118*/ uint16(xArgAL),
	/*8119*/ uint16(xArgImm8u),
	/*8120*/ uint16(xMatch),
	/*8121*/ uint16(xCondIs64), 8124, 8140,
	/*8124*/ uint16(xCondDataSize), 8128, 8134, 0,
	/*8128*/ uint16(xSetOp), uint16(ADC),
	/*8130*/ uint16(xReadIw),
	/*8131*/ uint16(xArgAX),
	/*8132*/ uint16(xArgImm16),
	/*8133*/ uint16(xMatch),
	/*8134*/ uint16(xSetOp), uint16(ADC),
	/*8136*/ uint16(xReadId),
	/*8137*/ uint16(xArgEAX),
	/*8138*/ uint16(xArgImm32),
	/*8139*/ uint16(xMatch),
	/*8140*/ uint16(xCondDataSize), 8128, 8134, 8144,
	/*8144*/ uint16(xSetOp), uint16(ADC),
	/*8146*/ uint16(xReadId),
	/*8147*/ uint16(xArgRAX),
	/*8148*/ uint16(xArgImm32),
	/*8149*/ uint16(xMatch),
	/*8150*/ uint16(xCondIs64), 8153, 0,
	/*8153*/ uint16(xSetOp), uint16(PUSH),
	/*8155*/ uint16(xArgSS),
	/*8156*/ uint16(xMatch),
	/*8157*/ uint16(xCondIs64), 8160, 0,
	/*8160*/ uint16(xSetOp), uint16(POP),
	/*8162*/ uint16(xArgSS),
	/*8163*/ uint16(xMatch),
	/*8164*/ uint16(xSetOp), uint16(SBB),
	/*8166*/ uint16(xReadSlashR),
	/*8167*/ uint16(xArgRM8),
	/*8168*/ uint16(xArgR8),
	/*8169*/ uint16(xMatch),
	/*8170*/ uint16(xCondIs64), 8173, 8189,
	/*8173*/ uint16(xCondDataSize), 8177, 8183, 0,
	/*8177*/ uint16(xSetOp), uint16(SBB),
	/*8179*/ uint16(xReadSlashR),
	/*8180*/ uint16(xArgRM16),
	/*8181*/ uint16(xArgR16),
	/*8182*/ uint16(xMatch),
	/*8183*/ uint16(xSetOp), uint16(SBB),
	/*8185*/ uint16(xReadSlashR),
	/*8186*/ uint16(xArgRM32),
	/*8187*/ uint16(xArgR32),
	/*8188*/ uint16(xMatch),
	/*8189*/ uint16(xCondDataSize), 8177, 8183, 8193,
	/*8193*/ uint16(xSetOp), uint16(SBB),
	/*8195*/ uint16(xReadSlashR),
	/*8196*/ uint16(xArgRM64),
	/*8197*/ uint16(xArgR64),
	/*8198*/ uint16(xMatch),
	/*8199*/ uint16(xSetOp), uint16(SBB),
	/*8201*/ uint16(xReadSlashR),
	/*8202*/ uint16(xArgR8),
	/*8203*/ uint16(xArgRM8),
	/*8204*/ uint16(xMatch),
	/*8205*/ uint16(xCondIs64), 8208, 8224,
	/*8208*/ uint16(xCondDataSize), 8212, 8218, 0,
	/*8212*/ uint16(xSetOp), uint16(SBB),
	/*8214*/ uint16(xReadSlashR),
	/*8215*/ uint16(xArgR16),
	/*8216*/ uint16(xArgRM16),
	/*8217*/ uint16(xMatch),
	/*8218*/ uint16(xSetOp), uint16(SBB),
	/*8220*/ uint16(xReadSlashR),
	/*8221*/ uint16(xArgR32),
	/*8222*/ uint16(xArgRM32),
	/*8223*/ uint16(xMatch),
	/*8224*/ uint16(xCondDataSize), 8212, 8218, 8228,
	/*8228*/ uint16(xSetOp), uint16(SBB),
	/*8230*/ uint16(xReadSlashR),
	/*8231*/ uint16(xArgR64),
	/*8232*/ uint16(xArgRM64),
	/*8233*/ uint16(xMatch),
	/*8234*/ uint16(xSetOp), uint16(SBB),
	/*8236*/ uint16(xReadIb),
	/*8237*/ uint16(xArgAL),
	/*8238*/ uint16(xArgImm8u),
	/*8239*/ uint16(xMatch),
	/*8240*/ uint16(xCondIs64), 8243, 8259,
	/*8243*/ uint16(xCondDataSize), 8247, 8253, 0,
	/*8247*/ uint16(xSetOp), uint16(SBB),
	/*8249*/ uint16(xReadIw),
	/*8250*/ uint16(xArgAX),
	/*8251*/ uint16(xArgImm16),
	/*8252*/ uint16(xMatch),
	/*8253*/ uint16(xSetOp), uint16(SBB),
	/*8255*/ uint16(xReadId),
	/*8256*/ uint16(xArgEAX),
	/*8257*/ uint16(xArgImm32),
	/*8258*/ uint16(xMatch),
	/*8259*/ uint16(xCondDataSize), 8247, 8253, 8263,
	/*8263*/ uint16(xSetOp), uint16(SBB),
	/*8265*/ uint16(xReadId),
	/*8266*/ uint16(xArgRAX),
	/*8267*/ uint16(xArgImm32),
	/*8268*/ uint16(xMatch),
	/*8269*/ uint16(xCondIs64), 8272, 0,
	/*8272*/ uint16(xSetOp), uint16(PUSH),
	/*8274*/ uint16(xArgDS),
	/*8275*/ uint16(xMatch),
	/*8276*/ uint16(xCondIs64), 8279, 0,
	/*8279*/ uint16(xSetOp), uint16(POP),
	/*8281*/ uint16(xArgDS),
	/*8282*/ uint16(xMatch),
	/*8283*/ uint16(xSetOp), uint16(AND),
	/*8285*/ uint16(xReadSlashR),
	/*8286*/ uint16(xArgRM8),
	/*8287*/ uint16(xArgR8),
	/*8288*/ uint16(xMatch),
	/*8289*/ uint16(xCondIs64), 8292, 8308,
	/*8292*/ uint16(xCondDataSize), 8296, 8302, 0,
	/*8296*/ uint16(xSetOp), uint16(AND),
	/*8298*/ uint16(xReadSlashR),
	/*8299*/ uint16(xArgRM16),
	/*8300*/ uint16(xArgR16),
	/*8301*/ uint16(xMatch),
	/*8302*/ uint16(xSetOp), uint16(AND),
	/*8304*/ uint16(xReadSlashR),
	/*8305*/ uint16(xArgRM32),
	/*8306*/ uint16(xArgR32),
	/*8307*/ uint16(xMatch),
	/*8308*/ uint16(xCondDataSize), 8296, 8302, 8312,
	/*8312*/ uint16(xSetOp), uint16(AND),
	/*8314*/ uint16(xReadSlashR),
	/*8315*/ uint16(xArgRM64),
	/*8316*/ uint16(xArgR64),
	/*8317*/ uint16(xMatch),
	/*8318*/ uint16(xSetOp), uint16(AND),
	/*8320*/ uint16(xReadSlashR),
	/*8321*/ uint16(xArgR8),
	/*8322*/ uint16(xArgRM8),
	/*8323*/ uint16(xMatch),
	/*8324*/ uint16(xCondIs64), 8327, 8343,
	/*8327*/ uint16(xCondDataSize), 8331, 8337, 0,
	/*8331*/ uint16(xSetOp), uint16(AND),
	/*8333*/ uint16(xReadSlashR),
	/*8334*/ uint16(xArgR16),
	/*8335*/ uint16(xArgRM16),
	/*8336*/ uint16(xMatch),
	/*8337*/ uint16(xSetOp), uint16(AND),
	/*8339*/ uint16(xReadSlashR),
	/*8340*/ uint16(xArgR32),
	/*8341*/ uint16(xArgRM32),
	/*8342*/ uint16(xMatch),
	/*8343*/ uint16(xCondDataSize), 8331, 8337, 8347,
	/*8347*/ uint16(xSetOp), uint16(AND),
	/*8349*/ uint16(xReadSlashR),
	/*8350*/ uint16(xArgR64),
	/*8351*/ uint16(xArgRM64),
	/*8352*/ uint16(xMatch),
	/*8353*/ uint16(xSetOp), uint16(AND),
	/*8355*/ uint16(xReadIb),
	/*8356*/ uint16(xArgAL),
	/*8357*/ uint16(xArgImm8u),
	/*8358*/ uint16(xMatch),
	/*8359*/ uint16(xCondIs64), 8362, 8378,
	/*8362*/ uint16(xCondDataSize), 8366, 8372, 0,
	/*8366*/ uint16(xSetOp), uint16(AND),
	/*8368*/ uint16(xReadIw),
	/*8369*/ uint16(xArgAX),
	/*8370*/ uint16(xArgImm16),
	/*8371*/ uint16(xMatch),
	/*8372*/ uint16(xSetOp), uint16(AND),
	/*8374*/ uint16(xReadId),
	/*8375*/ uint16(xArgEAX),
	/*8376*/ uint16(xArgImm32),
	/*8377*/ uint16(xMatch),
	/*8378*/ uint16(xCondDataSize), 8366, 8372, 8382,
	/*8382*/ uint16(xSetOp), uint16(AND),
	/*8384*/ uint16(xReadId),
	/*8385*/ uint16(xArgRAX),
	/*8386*/ uint16(xArgImm32),
	/*8387*/ uint16(xMatch),
	/*8388*/ uint16(xCondIs64), 8391, 0,
	/*8391*/ uint16(xSetOp), uint16(DAA),
	/*8393*/ uint16(xMatch),
	/*8394*/ uint16(xSetOp), uint16(SUB),
	/*8396*/ uint16(xReadSlashR),
	/*8397*/ uint16(xArgRM8),
	/*8398*/ uint16(xArgR8),
	/*8399*/ uint16(xMatch),
	/*8400*/ uint16(xCondIs64), 8403, 8419,
	/*8403*/ uint16(xCondDataSize), 8407, 8413, 0,
	/*8407*/ uint16(xSetOp), uint16(SUB),
	/*8409*/ uint16(xReadSlashR),
	/*8410*/ uint16(xArgRM16),
	/*8411*/ uint16(xArgR16),
	/*8412*/ uint16(xMatch),
	/*8413*/ uint16(xSetOp), uint16(SUB),
	/*8415*/ uint16(xReadSlashR),
	/*8416*/ uint16(xArgRM32),
	/*8417*/ uint16(xArgR32),
	/*8418*/ uint16(xMatch),
	/*8419*/ uint16(xCondDataSize), 8407, 8413, 8423,
	/*8423*/ uint16(xSetOp), uint16(SUB),
	/*8425*/ uint16(xReadSlashR),
	/*8426*/ uint16(xArgRM64),
	/*8427*/ uint16(xArgR64),
	/*8428*/ uint16(xMatch),
	/*8429*/ uint16(xCondPrefix), 3,
	0xC5, 8457,
	0xC4, 8443,
	0x0, 8437,
	/*8437*/ uint16(xSetOp), uint16(SUB),
	/*8439*/ uint16(xReadSlashR),
	/*8440*/ uint16(xArgR8),
	/*8441*/ uint16(xArgRM8),
	/*8442*/ uint16(xMatch),
	/*8443*/ uint16(xCondPrefix), 1,
	0x66, 8447,
	/*8447*/ uint16(xCondPrefix), 1,
	0x0F38, 8451,
	/*8451*/ uint16(xSetOp), uint16(VMOVNTDQA),
	/*8453*/ uint16(xReadSlashR),
	/*8454*/ uint16(xArgYmm1),
	/*8455*/ uint16(xArgM256),
	/*8456*/ uint16(xMatch),
	/*8457*/ uint16(xCondPrefix), 1,
	0x66, 8461,
	/*8461*/ uint16(xCondPrefix), 1,
	0x0F38, 8465,
	/*8465*/ uint16(xSetOp), uint16(VMOVNTDQA),
	/*8467*/ uint16(xReadSlashR),
	/*8468*/ uint16(xArgYmm1),
	/*8469*/ uint16(xArgM256),
	/*8470*/ uint16(xMatch),
	/*8471*/ uint16(xCondIs64), 8474, 8490,
	/*8474*/ uint16(xCondDataSize), 8478, 8484, 0,
	/*8478*/ uint16(xSetOp), uint16(SUB),
	/*8480*/ uint16(xReadSlashR),
	/*8481*/ uint16(xArgR16),
	/*8482*/ uint16(xArgRM16),
	/*8483*/ uint16(xMatch),
	/*8484*/ uint16(xSetOp), uint16(SUB),
	/*8486*/ uint16(xReadSlashR),
	/*8487*/ uint16(xArgR32),
	/*8488*/ uint16(xArgRM32),
	/*8489*/ uint16(xMatch),
	/*8490*/ uint16(xCondDataSize), 8478, 8484, 8494,
	/*8494*/ uint16(xSetOp), uint16(SUB),
	/*8496*/ uint16(xReadSlashR),
	/*8497*/ uint16(xArgR64),
	/*8498*/ uint16(xArgRM64),
	/*8499*/ uint16(xMatch),
	/*8500*/ uint16(xSetOp), uint16(SUB),
	/*8502*/ uint16(xReadIb),
	/*8503*/ uint16(xArgAL),
	/*8504*/ uint16(xArgImm8u),
	/*8505*/ uint16(xMatch),
	/*8506*/ uint16(xCondIs64), 8509, 8525,
	/*8509*/ uint16(xCondDataSize), 8513, 8519, 0,
	/*8513*/ uint16(xSetOp), uint16(SUB),
	/*8515*/ uint16(xReadIw),
	/*8516*/ uint16(xArgAX),
	/*8517*/ uint16(xArgImm16),
	/*8518*/ uint16(xMatch),
	/*8519*/ uint16(xSetOp), uint16(SUB),
	/*8521*/ uint16(xReadId),
	/*8522*/ uint16(xArgEAX),
	/*8523*/ uint16(xArgImm32),
	/*8524*/ uint16(xMatch),
	/*8525*/ uint16(xCondDataSize), 8513, 8519, 8529,
	/*8529*/ uint16(xSetOp), uint16(SUB),
	/*8531*/ uint16(xReadId),
	/*8532*/ uint16(xArgRAX),
	/*8533*/ uint16(xArgImm32),
	/*8534*/ uint16(xMatch),
	/*8535*/ uint16(xCondIs64), 8538, 0,
	/*8538*/ uint16(xSetOp), uint16(DAS),
	/*8540*/ uint16(xMatch),
	/*8541*/ uint16(xSetOp), uint16(XOR),
	/*8543*/ uint16(xReadSlashR),
	/*8544*/ uint16(xArgRM8),
	/*8545*/ uint16(xArgR8),
	/*8546*/ uint16(xMatch),
	/*8547*/ uint16(xCondIs64), 8550, 8566,
	/*8550*/ uint16(xCondDataSize), 8554, 8560, 0,
	/*8554*/ uint16(xSetOp), uint16(XOR),
	/*8556*/ uint16(xReadSlashR),
	/*8557*/ uint16(xArgRM16),
	/*8558*/ uint16(xArgR16),
	/*8559*/ uint16(xMatch),
	/*8560*/ uint16(xSetOp), uint16(XOR),
	/*8562*/ uint16(xReadSlashR),
	/*8563*/ uint16(xArgRM32),
	/*8564*/ uint16(xArgR32),
	/*8565*/ uint16(xMatch),
	/*8566*/ uint16(xCondDataSize), 8554, 8560, 8570,
	/*8570*/ uint16(xSetOp), uint16(XOR),
	/*8572*/ uint16(xReadSlashR),
	/*8573*/ uint16(xArgRM64),
	/*8574*/ uint16(xArgR64),
	/*8575*/ uint16(xMatch),
	/*8576*/ uint16(xSetOp), uint16(XOR),
	/*8578*/ uint16(xReadSlashR),
	/*8579*/ uint16(xArgR8),
	/*8580*/ uint16(xArgRM8),
	/*8581*/ uint16(xMatch),
	/*8582*/ uint16(xCondIs64), 8585, 8601,
	/*8585*/ uint16(xCondDataSize), 8589, 8595, 0,
	/*8589*/ uint16(xSetOp), uint16(XOR),
	/*8591*/ uint16(xReadSlashR),
	/*8592*/ uint16(xArgR16),
	/*8593*/ uint16(xArgRM16),
	/*8594*/ uint16(xMatch),
	/*8595*/ uint16(xSetOp), uint16(XOR),
	/*8597*/ uint16(xReadSlashR),
	/*8598*/ uint16(xArgR32),
	/*8599*/ uint16(xArgRM32),
	/*8600*/ uint16(xMatch),
	/*8601*/ uint16(xCondDataSize), 8589, 8595, 8605,
	/*8605*/ uint16(xSetOp), uint16(XOR),
	/*8607*/ uint16(xReadSlashR),
	/*8608*/ uint16(xArgR64),
	/*8609*/ uint16(xArgRM64),
	/*8610*/ uint16(xMatch),
	/*8611*/ uint16(xSetOp), uint16(XOR),
	/*8613*/ uint16(xReadIb),
	/*8614*/ uint16(xArgAL),
	/*8615*/ uint16(xArgImm8u),
	/*8616*/ uint16(xMatch),
	/*8617*/ uint16(xCondIs64), 8620, 8636,
	/*8620*/ uint16(xCondDataSize), 8624, 8630, 0,
	/*8624*/ uint16(xSetOp), uint16(XOR),
	/*8626*/ uint16(xReadIw),
	/*8627*/ uint16(xArgAX),
	/*8628*/ uint16(xArgImm16),
	/*8629*/ uint16(xMatch),
	/*8630*/ uint16(xSetOp), uint16(XOR),
	/*8632*/ uint16(xReadId),
	/*8633*/ uint16(xArgEAX),
	/*8634*/ uint16(xArgImm32),
	/*8635*/ uint16(xMatch),
	/*8636*/ uint16(xCondDataSize), 8624, 8630, 8640,
	/*8640*/ uint16(xSetOp), uint16(XOR),
	/*8642*/ uint16(xReadId),
	/*8643*/ uint16(xArgRAX),
	/*8644*/ uint16(xArgImm32),
	/*8645*/ uint16(xMatch),
	/*8646*/ uint16(xCondIs64), 8649, 0,
	/*8649*/ uint16(xSetOp), uint16(AAA),
	/*8651*/ uint16(xMatch),
	/*8652*/ uint16(xSetOp), uint16(CMP),
	/*8654*/ uint16(xReadSlashR),
	/*8655*/ uint16(xArgRM8),
	/*8656*/ uint16(xArgR8),
	/*8657*/ uint16(xMatch),
	/*8658*/ uint16(xCondIs64), 8661, 8677,
	/*8661*/ uint16(xCondDataSize), 8665, 8671, 0,
	/*8665*/ uint16(xSetOp), uint16(CMP),
	/*8667*/ uint16(xReadSlashR),
	/*8668*/ uint16(xArgRM16),
	/*8669*/ uint16(xArgR16),
	/*8670*/ uint16(xMatch),
	/*8671*/ uint16(xSetOp), uint16(CMP),
	/*8673*/ uint16(xReadSlashR),
	/*8674*/ uint16(xArgRM32),
	/*8675*/ uint16(xArgR32),
	/*8676*/ uint16(xMatch),
	/*8677*/ uint16(xCondDataSize), 8665, 8671, 8681,
	/*8681*/ uint16(xSetOp), uint16(CMP),
	/*8683*/ uint16(xReadSlashR),
	/*8684*/ uint16(xArgRM64),
	/*8685*/ uint16(xArgR64),
	/*8686*/ uint16(xMatch),
	/*8687*/ uint16(xSetOp), uint16(CMP),
	/*8689*/ uint16(xReadSlashR),
	/*8690*/ uint16(xArgR8),
	/*8691*/ uint16(xArgRM8),
	/*8692*/ uint16(xMatch),
	/*8693*/ uint16(xCondIs64), 8696, 8712,
	/*8696*/ uint16(xCondDataSize), 8700, 8706, 0,
	/*8700*/ uint16(xSetOp), uint16(CMP),
	/*8702*/ uint16(xReadSlashR),
	/*8703*/ uint16(xArgR16),
	/*8704*/ uint16(xArgRM16),
	/*8705*/ uint16(xMatch),
	/*8706*/ uint16(xSetOp), uint16(CMP),
	/*8708*/ uint16(xReadSlashR),
	/*8709*/ uint16(xArgR32),
	/*8710*/ uint16(xArgRM32),
	/*8711*/ uint16(xMatch),
	/*8712*/ uint16(xCondDataSize), 8700, 8706, 8716,
	/*8716*/ uint16(xSetOp), uint16(CMP),
	/*8718*/ uint16(xReadSlashR),
	/*8719*/ uint16(xArgR64),
	/*8720*/ uint16(xArgRM64),
	/*8721*/ uint16(xMatch),
	/*8722*/ uint16(xSetOp), uint16(CMP),
	/*8724*/ uint16(xReadIb),
	/*8725*/ uint16(xArgAL),
	/*8726*/ uint16(xArgImm8u),
	/*8727*/ uint16(xMatch),
	/*8728*/ uint16(xCondIs64), 8731, 8747,
	/*8731*/ uint16(xCondDataSize), 8735, 8741, 0,
	/*8735*/ uint16(xSetOp), uint16(CMP),
	/*8737*/ uint16(xReadIw),
	/*8738*/ uint16(xArgAX),
	/*8739*/ uint16(xArgImm16),
	/*8740*/ uint16(xMatch),
	/*8741*/ uint16(xSetOp), uint16(CMP),
	/*8743*/ uint16(xReadId),
	/*8744*/ uint16(xArgEAX),
	/*8745*/ uint16(xArgImm32),
	/*8746*/ uint16(xMatch),
	/*8747*/ uint16(xCondDataSize), 8735, 8741, 8751,
	/*8751*/ uint16(xSetOp), uint16(CMP),
	/*8753*/ uint16(xReadId),
	/*8754*/ uint16(xArgRAX),
	/*8755*/ uint16(xArgImm32),
	/*8756*/ uint16(xMatch),
	/*8757*/ uint16(xCondIs64), 8760, 0,
	/*8760*/ uint16(xSetOp), uint16(AAS),
	/*8762*/ uint16(xMatch),
	/*8763*/ uint16(xCondIs64), 8766, 0,
	/*8766*/ uint16(xCondDataSize), 8770, 8774, 0,
	/*8770*/ uint16(xSetOp), uint16(INC),
	/*8772*/ uint16(xArgR16op),
	/*8773*/ uint16(xMatch),
	/*8774*/ uint16(xSetOp), uint16(INC),
	/*8776*/ uint16(xArgR32op),
	/*8777*/ uint16(xMatch),
	/*8778*/ uint16(xCondIs64), 8781, 0,
	/*8781*/ uint16(xCondDataSize), 8785, 8789, 0,
	/*8785*/ uint16(xSetOp), uint16(DEC),
	/*8787*/ uint16(xArgR16op),
	/*8788*/ uint16(xMatch),
	/*8789*/ uint16(xSetOp), uint16(DEC),
	/*8791*/ uint16(xArgR32op),
	/*8792*/ uint16(xMatch),
	/*8793*/ uint16(xCondIs64), 8796, 8808,
	/*8796*/ uint16(xCondDataSize), 8800, 8804, 0,
	/*8800*/ uint16(xSetOp), uint16(PUSH),
	/*8802*/ uint16(xArgR16op),
	/*8803*/ uint16(xMatch),
	/*8804*/ uint16(xSetOp), uint16(PUSH),
	/*8806*/ uint16(xArgR32op),
	/*8807*/ uint16(xMatch),
	/*8808*/ uint16(xCondDataSize), 8800, 8812, 8816,
	/*8812*/ uint16(xSetOp), uint16(PUSH),
	/*8814*/ uint16(xArgR64op),
	/*8815*/ uint16(xMatch),
	/*8816*/ uint16(xSetOp), uint16(PUSH),
	/*8818*/ uint16(xArgR64op),
	/*8819*/ uint16(xMatch),
	/*8820*/ uint16(xCondIs64), 8823, 8835,
	/*8823*/ uint16(xCondDataSize), 8827, 8831, 0,
	/*8827*/ uint16(xSetOp), uint16(POP),
	/*8829*/ uint16(xArgR16op),
	/*8830*/ uint16(xMatch),
	/*8831*/ uint16(xSetOp), uint16(POP),
	/*8833*/ uint16(xArgR32op),
	/*8834*/ uint16(xMatch),
	/*8835*/ uint16(xCondDataSize), 8827, 8839, 8843,
	/*8839*/ uint16(xSetOp), uint16(POP),
	/*8841*/ uint16(xArgR64op),
	/*8842*/ uint16(xMatch),
	/*8843*/ uint16(xSetOp), uint16(POP),
	/*8845*/ uint16(xArgR64op),
	/*8846*/ uint16(xMatch),
	/*8847*/ uint16(xCondIs64), 8850, 0,
	/*8850*/ uint16(xCondDataSize), 8854, 8857, 0,
	/*8854*/ uint16(xSetOp), uint16(PUSHA),
	/*8856*/ uint16(xMatch),
	/*8857*/ uint16(xSetOp), uint16(PUSHAD),
	/*8859*/ uint16(xMatch),
	/*8860*/ uint16(xCondIs64), 8863, 0,
	/*8863*/ uint16(xCondDataSize), 8867, 8870, 0,
	/*8867*/ uint16(xSetOp), uint16(POPA),
	/*8869*/ uint16(xMatch),
	/*8870*/ uint16(xSetOp), uint16(POPAD),
	/*8872*/ uint16(xMatch),
	/*8873*/ uint16(xCondIs64), 8876, 0,
	/*8876*/ uint16(xCondDataSize), 8880, 8886, 0,
	/*8880*/ uint16(xSetOp), uint16(BOUND),
	/*8882*/ uint16(xReadSlashR),
	/*8883*/ uint16(xArgR16),
	/*8884*/ uint16(xArgM16and16),
	/*8885*/ uint16(xMatch),
	/*8886*/ uint16(xSetOp), uint16(BOUND),
	/*8888*/ uint16(xReadSlashR),
	/*8889*/ uint16(xArgR32),
	/*8890*/ uint16(xArgM32and32),
	/*8891*/ uint16(xMatch),
	/*8892*/ uint16(xCondIs64), 8895, 8901,
	/*8895*/ uint16(xSetOp), uint16(ARPL),
	/*8897*/ uint16(xReadSlashR),
	/*8898*/ uint16(xArgRM16),
	/*8899*/ uint16(xArgR16),
	/*8900*/ uint16(xMatch),
	/*8901*/ uint16(xCondDataSize), 8905, 8911, 8917,
	/*8905*/ uint16(xSetOp), uint16(MOVSXD),
	/*8907*/ uint16(xReadSlashR),
	/*8908*/ uint16(xArgR16),
	/*8909*/ uint16(xArgRM32),
	/*8910*/ uint16(xMatch),
	/*8911*/ uint16(xSetOp), uint16(MOVSXD),
	/*8913*/ uint16(xReadSlashR),
	/*8914*/ uint16(xArgR32),
	/*8915*/ uint16(xArgRM32),
	/*8916*/ uint16(xMatch),
	/*8917*/ uint16(xSetOp), uint16(MOVSXD),
	/*8919*/ uint16(xReadSlashR),
	/*8920*/ uint16(xArgR64),
	/*8921*/ uint16(xArgRM32),
	/*8922*/ uint16(xMatch),
	/*8923*/ uint16(xCondDataSize), 8927, 8932, 8937,
	/*8927*/ uint16(xSetOp), uint16(PUSH),
	/*8929*/ uint16(xReadIw),
	/*8930*/ uint16(xArgImm16),
	/*8931*/ uint16(xMatch),
	/*8932*/ uint16(xSetOp), uint16(PUSH),
	/*8934*/ uint16(xReadId),
	/*8935*/ uint16(xArgImm32),
	/*8936*/ uint16(xMatch),
	/*8937*/ uint16(xSetOp), uint16(PUSH),
	/*8939*/ uint16(xReadId),
	/*8940*/ uint16(xArgImm32),
	/*8941*/ uint16(xMatch),
	/*8942*/ uint16(xCondIs64), 8945, 8965,
	/*8945*/ uint16(xCondDataSize), 8949, 8957, 0,
	/*8949*/ uint16(xSetOp), uint16(IMUL),
	/*8951*/ uint16(xReadSlashR),
	/*8952*/ uint16(xReadIw),
	/*8953*/ uint16(xArgR16),
	/*8954*/ uint16(xArgRM16),
	/*8955*/ uint16(xArgImm16),
	/*8956*/ uint16(xMatch),
	/*8957*/ uint16(xSetOp), uint16(IMUL),
	/*8959*/ uint16(xReadSlashR),
	/*8960*/ uint16(xReadId),
	/*8961*/ uint16(xArgR32),
	/*8962*/ uint16(xArgRM32),
	/*8963*/ uint16(xArgImm32),
	/*8964*/ uint16(xMatch),
	/*8965*/ uint16(xCondDataSize), 8949, 8957, 8969,
	/*8969*/ uint16(xSetOp), uint16(IMUL),
	/*8971*/ uint16(xReadSlashR),
	/*8972*/ uint16(xReadId),
	/*8973*/ uint16(xArgR64),
	/*8974*/ uint16(xArgRM64),
	/*8975*/ uint16(xArgImm32),
	/*8976*/ uint16(xMatch),
	/*8977*/ uint16(xSetOp), uint16(PUSH),
	/*8979*/ uint16(xReadIb),
	/*8980*/ uint16(xArgImm8),
	/*8981*/ uint16(xMatch),
	/*8982*/ uint16(xCondIs64), 8985, 9005,
	/*8985*/ uint16(xCondDataSize), 8989, 8997, 0,
	/*8989*/ uint16(xSetOp), uint16(IMUL),
	/*8991*/ uint16(xReadSlashR),
	/*8992*/ uint16(xReadIb),
	/*8993*/ uint16(xArgR16),
	/*8994*/ uint16(xArgRM16),
	/*8995*/ uint16(xArgImm8),
	/*8996*/ uint16(xMatch),
	/*8997*/ uint16(xSetOp), uint16(IMUL),
	/*8999*/ uint16(xReadSlashR),
	/*9000*/ uint16(xReadIb),
	/*9001*/ uint16(xArgR32),
	/*9002*/ uint16(xArgRM32),
	/*9003*/ uint16(xArgImm8),
	/*9004*/ uint16(xMatch),
	/*9005*/ uint16(xCondDataSize), 8989, 8997, 9009,
	/*9009*/ uint16(xSetOp), uint16(IMUL),
	/*9011*/ uint16(xReadSlashR),
	/*9012*/ uint16(xReadIb),
	/*9013*/ uint16(xArgR64),
	/*9014*/ uint16(xArgRM64),
	/*9015*/ uint16(xArgImm8),
	/*9016*/ uint16(xMatch),
	/*9017*/ uint16(xSetOp), uint16(INSB),
	/*9019*/ uint16(xMatch),
	/*9020*/ uint16(xCondDataSize), 9024, 9027, 9030,
	/*9024*/ uint16(xSetOp), uint16(INSW),
	/*9026*/ uint16(xMatch),
	/*9027*/ uint16(xSetOp), uint16(INSD),
	/*9029*/ uint16(xMatch),
	/*9030*/ uint16(xSetOp), uint16(INSD),
	/*9032*/ uint16(xMatch),
	/*9033*/ uint16(xSetOp), uint16(OUTSB),
	/*9035*/ uint16(xMatch),
	/*9036*/ uint16(xCondPrefix), 3,
	0xC5, 9083,
	0xC4, 9057,
	0x0, 9044,
	/*9044*/ uint16(xCondDataSize), 9048, 9051, 9054,
	/*9048*/ uint16(xSetOp), uint16(OUTSW),
	/*9050*/ uint16(xMatch),
	/*9051*/ uint16(xSetOp), uint16(OUTSD),
	/*9053*/ uint16(xMatch),
	/*9054*/ uint16(xSetOp), uint16(OUTSD),
	/*9056*/ uint16(xMatch),
	/*9057*/ uint16(xCondPrefix), 2,
	0xF3, 9073,
	0x66, 9063,
	/*9063*/ uint16(xCondPrefix), 1,
	0x0F, 9067,
	/*9067*/ uint16(xSetOp), uint16(VMOVDQA),
	/*9069*/ uint16(xReadSlashR),
	/*9070*/ uint16(xArgYmm1),
	/*9071*/ uint16(xArgYmm2M256),
	/*9072*/ uint16(xMatch),
	/*9073*/ uint16(xCondPrefix), 1,
	0x0F, 9077,
	/*9077*/ uint16(xSetOp), uint16(VMOVDQU),
	/*9079*/ uint16(xReadSlashR),
	/*9080*/ uint16(xArgYmm1),
	/*9081*/ uint16(xArgYmm2M256),
	/*9082*/ uint16(xMatch),
	/*9083*/ uint16(xCondPrefix), 2,
	0xF3, 9099,
	0x66, 9089,
	/*9089*/ uint16(xCondPrefix), 1,
	0x0F, 9093,
	/*9093*/ uint16(xSetOp), uint16(VMOVDQA),
	/*9095*/ uint16(xReadSlashR),
	/*9096*/ uint16(xArgYmm1),
	/*9097*/ uint16(xArgYmm2M256),
	/*9098*/ uint16(xMatch),
	/*9099*/ uint16(xCondPrefix), 1,
	0x0F, 9103,
	/*9103*/ uint16(xSetOp), uint16(VMOVDQU),
	/*9105*/ uint16(xReadSlashR),
	/*9106*/ uint16(xArgYmm1),
	/*9107*/ uint16(xArgYmm2M256),
	/*9108*/ uint16(xMatch),
	/*9109*/ uint16(xSetOp), uint16(JO),
	/*9111*/ uint16(xReadCb),
	/*9112*/ uint16(xArgRel8),
	/*9113*/ uint16(xMatch),
	/*9114*/ uint16(xSetOp), uint16(JNO),
	/*9116*/ uint16(xReadCb),
	/*9117*/ uint16(xArgRel8),
	/*9118*/ uint16(xMatch),
	/*9119*/ uint16(xSetOp), uint16(JB),
	/*9121*/ uint16(xReadCb),
	/*9122*/ uint16(xArgRel8),
	/*9123*/ uint16(xMatch),
	/*9124*/ uint16(xSetOp), uint16(JAE),
	/*9126*/ uint16(xReadCb),
	/*9127*/ uint16(xArgRel8),
	/*9128*/ uint16(xMatch),
	/*9129*/ uint16(xSetOp), uint16(JE),
	/*9131*/ uint16(xReadCb),
	/*9132*/ uint16(xArgRel8),
	/*9133*/ uint16(xMatch),
	/*9134*/ uint16(xSetOp), uint16(JNE),
	/*9136*/ uint16(xReadCb),
	/*9137*/ uint16(xArgRel8),
	/*9138*/ uint16(xMatch),
	/*9139*/ uint16(xSetOp), uint16(JBE),
	/*9141*/ uint16(xReadCb),
	/*9142*/ uint16(xArgRel8),
	/*9143*/ uint16(xMatch),
	/*9144*/ uint16(xCondPrefix), 3,
	0xC5, 9164,
	0xC4, 9157,
	0x0, 9152,
	/*9152*/ uint16(xSetOp), uint16(JA),
	/*9154*/ uint16(xReadCb),
	/*9155*/ uint16(xArgRel8),
	/*9156*/ uint16(xMatch),
	/*9157*/ uint16(xCondPrefix), 1,
	0x0F, 9161,
	/*9161*/ uint16(xSetOp), uint16(VZEROUPPER),
	/*9163*/ uint16(xMatch),
	/*9164*/ uint16(xCondPrefix), 1,
	0x0F, 9168,
	/*9168*/ uint16(xSetOp), uint16(VZEROUPPER),
	/*9170*/ uint16(xMatch),
	/*9171*/ uint16(xSetOp), uint16(JS),
	/*9173*/ uint16(xReadCb),
	/*9174*/ uint16(xArgRel8),
	/*9175*/ uint16(xMatch),
	/*9176*/ uint16(xSetOp), uint16(JNS),
	/*9178*/ uint16(xReadCb),
	/*9179*/ uint16(xArgRel8),
	/*9180*/ uint16(xMatch),
	/*9181*/ uint16(xSetOp), uint16(JP),
	/*9183*/ uint16(xReadCb),
	/*9184*/ uint16(xArgRel8),
	/*9185*/ uint16(xMatch),
	/*9186*/ uint16(xSetOp), uint16(JNP),
	/*9188*/ uint16(xReadCb),
	/*9189*/ uint16(xArgRel8),
	/*9190*/ uint16(xMatch),
	/*9191*/ uint16(xSetOp), uint16(JL),
	/*9193*/ uint16(xReadCb),
	/*9194*/ uint16(xArgRel8),
	/*9195*/ uint16(xMatch),
	/*9196*/ uint16(xSetOp), uint16(JGE),
	/*9198*/ uint16(xReadCb),
	/*9199*/ uint16(xArgRel8),
	/*9200*/ uint16(xMatch),
	/*9201*/ uint16(xSetOp), uint16(JLE),
	/*9203*/ uint16(xReadCb),
	/*9204*/ uint16(xArgRel8),
	/*9205*/ uint16(xMatch),
	/*9206*/ uint16(xCondPrefix), 3,
	0xC5, 9245,
	0xC4, 9219,
	0x0, 9214,
	/*9214*/ uint16(xSetOp), uint16(JG),
	/*9216*/ uint16(xReadCb),
	/*9217*/ uint16(xArgRel8),
	/*9218*/ uint16(xMatch),
	/*9219*/ uint16(xCondPrefix), 2,
	0xF3, 9235,
	0x66, 9225,
	/*9225*/ uint16(xCondPrefix), 1,
	0x0F, 9229,
	/*9229*/ uint16(xSetOp), uint16(VMOVDQA),
	/*9231*/ uint16(xReadSlashR),
	/*9232*/ uint16(xArgYmm2M256),
	/*9233*/ uint16(xArgYmm1),
	/*9234*/ uint16(xMatch),
	/*9235*/ uint16(xCondPrefix), 1,
	0x0F, 9239,
	/*9239*/ uint16(xSetOp), uint16(VMOVDQU),
	/*9241*/ uint16(xReadSlashR),
	/*9242*/ uint16(xArgYmm2M256),
	/*9243*/ uint16(xArgYmm1),
	/*9244*/ uint16(xMatch),
	/*9245*/ uint16(xCondPrefix), 2,
	0xF3, 9261,
	0x66, 9251,
	/*9251*/ uint16(xCondPrefix), 1,
	0x0F, 9255,
	/*9255*/ uint16(xSetOp), uint16(VMOVDQA),
	/*9257*/ uint16(xReadSlashR),
	/*9258*/ uint16(xArgYmm2M256),
	/*9259*/ uint16(xArgYmm1),
	/*9260*/ uint16(xMatch),
	/*9261*/ uint16(xCondPrefix), 1,
	0x0F, 9265,
	/*9265*/ uint16(xSetOp), uint16(VMOVDQU),
	/*9267*/ uint16(xReadSlashR),
	/*9268*/ uint16(xArgYmm2M256),
	/*9269*/ uint16(xArgYmm1),
	/*9270*/ uint16(xMatch),
	/*9271*/ uint16(xCondSlashR),
	9280, // 0
	9286, // 1
	9292, // 2
	9298, // 3
	9304, // 4
	9310, // 5
	9316, // 6
	9322, // 7
	/*9280*/ uint16(xSetOp), uint16(ADD),
	/*9282*/ uint16(xReadIb),
	/*9283*/ uint16(xArgRM8),
	/*9284*/ uint16(xArgImm8u),
	/*9285*/ uint16(xMatch),
	/*9286*/ uint16(xSetOp), uint16(OR),
	/*9288*/ uint16(xReadIb),
	/*9289*/ uint16(xArgRM8),
	/*9290*/ uint16(xArgImm8u),
	/*9291*/ uint16(xMatch),
	/*9292*/ uint16(xSetOp), uint16(ADC),
	/*9294*/ uint16(xReadIb),
	/*9295*/ uint16(xArgRM8),
	/*9296*/ uint16(xArgImm8u),
	/*9297*/ uint16(xMatch),
	/*9298*/ uint16(xSetOp), uint16(SBB),
	/*9300*/ uint16(xReadIb),
	/*9301*/ uint16(xArgRM8),
	/*9302*/ uint16(xArgImm8u),
	/*9303*/ uint16(xMatch),
	/*9304*/ uint16(xSetOp), uint16(AND),
	/*9306*/ uint16(xReadIb),
	/*9307*/ uint16(xArgRM8),
	/*9308*/ uint16(xArgImm8u),
	/*9309*/ uint16(xMatch),
	/*9310*/ uint16(xSetOp), uint16(SUB),
	/*9312*/ uint16(xReadIb),
	/*9313*/ uint16(xArgRM8),
	/*9314*/ uint16(xArgImm8u),
	/*9315*/ uint16(xMatch),
	/*9316*/ uint16(xSetOp), uint16(XOR),
	/*9318*/ uint16(xReadIb),
	/*9319*/ uint16(xArgRM8),
	/*9320*/ uint16(xArgImm8u),
	/*9321*/ uint16(xMatch),
	/*9322*/ uint16(xSetOp), uint16(CMP),
	/*9324*/ uint16(xReadIb),
	/*9325*/ uint16(xArgRM8),
	/*9326*/ uint16(xArgImm8u),
	/*9327*/ uint16(xMatch),
	/*9328*/ uint16(xCondSlashR),
	9337, // 0
	9366, // 1
	9395, // 2
	9424, // 3
	9453, // 4
	9482, // 5
	9511, // 6
	9540, // 7
	/*9337*/ uint16(xCondIs64), 9340, 9356,
	/*9340*/ uint16(xCondDataSize), 9344, 9350, 0,
	/*9344*/ uint16(xSetOp), uint16(ADD),
	/*9346*/ uint16(xReadIw),
	/*9347*/ uint16(xArgRM16),
	/*9348*/ uint16(xArgImm16),
	/*9349*/ uint16(xMatch),
	/*9350*/ uint16(xSetOp), uint16(ADD),
	/*9352*/ uint16(xReadId),
	/*9353*/ uint16(xArgRM32),
	/*9354*/ uint16(xArgImm32),
	/*9355*/ uint16(xMatch),
	/*9356*/ uint16(xCondDataSize), 9344, 9350, 9360,
	/*9360*/ uint16(xSetOp), uint16(ADD),
	/*9362*/ uint16(xReadId),
	/*9363*/ uint16(xArgRM64),
	/*9364*/ uint16(xArgImm32),
	/*9365*/ uint16(xMatch),
	/*9366*/ uint16(xCondIs64), 9369, 9385,
	/*9369*/ uint16(xCondDataSize), 9373, 9379, 0,
	/*9373*/ uint16(xSetOp), uint16(OR),
	/*9375*/ uint16(xReadIw),
	/*9376*/ uint16(xArgRM16),
	/*9377*/ uint16(xArgImm16),
	/*9378*/ uint16(xMatch),
	/*9379*/ uint16(xSetOp), uint16(OR),
	/*9381*/ uint16(xReadId),
	/*9382*/ uint16(xArgRM32),
	/*9383*/ uint16(xArgImm32),
	/*9384*/ uint16(xMatch),
	/*9385*/ uint16(xCondDataSize), 9373, 9379, 9389,
	/*9389*/ uint16(xSetOp), uint16(OR),
	/*9391*/ uint16(xReadId),
	/*9392*/ uint16(xArgRM64),
	/*9393*/ uint16(xArgImm32),
	/*9394*/ uint16(xMatch),
	/*9395*/ uint16(xCondIs64), 9398, 9414,
	/*9398*/ uint16(xCondDataSize), 9402, 9408, 0,
	/*9402*/ uint16(xSetOp), uint16(ADC),
	/*9404*/ uint16(xReadIw),
	/*9405*/ uint16(xArgRM16),
	/*9406*/ uint16(xArgImm16),
	/*9407*/ uint16(xMatch),
	/*9408*/ uint16(xSetOp), uint16(ADC),
	/*9410*/ uint16(xReadId),
	/*9411*/ uint16(xArgRM32),
	/*9412*/ uint16(xArgImm32),
	/*9413*/ uint16(xMatch),
	/*9414*/ uint16(xCondDataSize), 9402, 9408, 9418,
	/*9418*/ uint16(xSetOp), uint16(ADC),
	/*9420*/ uint16(xReadId),
	/*9421*/ uint16(xArgRM64),
	/*9422*/ uint16(xArgImm32),
	/*9423*/ uint16(xMatch),
	/*9424*/ uint16(xCondIs64), 9427, 9443,
	/*9427*/ uint16(xCondDataSize), 9431, 9437, 0,
	/*9431*/ uint16(xSetOp), uint16(SBB),
	/*9433*/ uint16(xReadIw),
	/*9434*/ uint16(xArgRM16),
	/*9435*/ uint16(xArgImm16),
	/*9436*/ uint16(xMatch),
	/*9437*/ uint16(xSetOp), uint16(SBB),
	/*9439*/ uint16(xReadId),
	/*9440*/ uint16(xArgRM32),
	/*9441*/ uint16(xArgImm32),
	/*9442*/ uint16(xMatch),
	/*9443*/ uint16(xCondDataSize), 9431, 9437, 9447,
	/*9447*/ uint16(xSetOp), uint16(SBB),
	/*9449*/ uint16(xReadId),
	/*9450*/ uint16(xArgRM64),
	/*9451*/ uint16(xArgImm32),
	/*9452*/ uint16(xMatch),
	/*9453*/ uint16(xCondIs64), 9456, 9472,
	/*9456*/ uint16(xCondDataSize), 9460, 9466, 0,
	/*9460*/ uint16(xSetOp), uint16(AND),
	/*9462*/ uint16(xReadIw),
	/*9463*/ uint16(xArgRM16),
	/*9464*/ uint16(xArgImm16),
	/*9465*/ uint16(xMatch),
	/*9466*/ uint16(xSetOp), uint16(AND),
	/*9468*/ uint16(xReadId),
	/*9469*/ uint16(xArgRM32),
	/*9470*/ uint16(xArgImm32),
	/*9471*/ uint16(xMatch),
	/*9472*/ uint16(xCondDataSize), 9460, 9466, 9476,
	/*9476*/ uint16(xSetOp), uint16(AND),
	/*9478*/ uint16(xReadId),
	/*9479*/ uint16(xArgRM64),
	/*9480*/ uint16(xArgImm32),
	/*9481*/ uint16(xMatch),
	/*9482*/ uint16(xCondIs64), 9485, 9501,
	/*9485*/ uint16(xCondDataSize), 9489, 9495, 0,
	/*9489*/ uint16(xSetOp), uint16(SUB),
	/*9491*/ uint16(xReadIw),
	/*9492*/ uint16(xArgRM16),
	/*9493*/ uint16(xArgImm16),
	/*9494*/ uint16(xMatch),
	/*9495*/ uint16(xSetOp), uint16(SUB),
	/*9497*/ uint16(xReadId),
	/*9498*/ uint16(xArgRM32),
	/*9499*/ uint16(xArgImm32),
	/*9500*/ uint16(xMatch),
	/*9501*/ uint16(xCondDataSize), 9489, 9495, 9505,
	/*9505*/ uint16(xSetOp), uint16(SUB),
	/*9507*/ uint16(xReadId),
	/*9508*/ uint16(xArgRM64),
	/*9509*/ uint16(xArgImm32),
	/*9510*/ uint16(xMatch),
	/*9511*/ uint16(xCondIs64), 9514, 9530,
	/*9514*/ uint16(xCondDataSize), 9518, 9524, 0,
	/*9518*/ uint16(xSetOp), uint16(XOR),
	/*9520*/ uint16(xReadIw),
	/*9521*/ uint16(xArgRM16),
	/*9522*/ uint16(xArgImm16),
	/*9523*/ uint16(xMatch),
	/*9524*/ uint16(xSetOp), uint16(XOR),
	/*9526*/ uint16(xReadId),
	/*9527*/ uint16(xArgRM32),
	/*9528*/ uint16(xArgImm32),
	/*9529*/ uint16(xMatch),
	/*9530*/ uint16(xCondDataSize), 9518, 9524, 9534,
	/*9534*/ uint16(xSetOp), uint16(XOR),
	/*9536*/ uint16(xReadId),
	/*9537*/ uint16(xArgRM64),
	/*9538*/ uint16(xArgImm32),
	/*9539*/ uint16(xMatch),
	/*9540*/ uint16(xCondIs64), 9543, 9559,
	/*9543*/ uint16(xCondDataSize), 9547, 9553, 0,
	/*9547*/ uint16(xSetOp), uint16(CMP),
	/*9549*/ uint16(xReadIw),
	/*9550*/ uint16(xArgRM16),
	/*9551*/ uint16(xArgImm16),
	/*9552*/ uint16(xMatch),
	/*9553*/ uint16(xSetOp), uint16(CMP),
	/*9555*/ uint16(xReadId),
	/*9556*/ uint16(xArgRM32),
	/*9557*/ uint16(xArgImm32),
	/*9558*/ uint16(xMatch),
	/*9559*/ uint16(xCondDataSize), 9547, 9553, 9563,
	/*9563*/ uint16(xSetOp), uint16(CMP),
	/*9565*/ uint16(xReadId),
	/*9566*/ uint16(xArgRM64),
	/*9567*/ uint16(xArgImm32),
	/*9568*/ uint16(xMatch),
	/*9569*/ uint16(xCondSlashR),
	9578, // 0
	9607, // 1
	9636, // 2
	9665, // 3
	9694, // 4
	9723, // 5
	9752, // 6
	9781, // 7
	/*9578*/ uint16(xCondIs64), 9581, 9597,
	/*9581*/ uint16(xCondDataSize), 9585, 9591, 0,
	/*9585*/ uint16(xSetOp), uint16(ADD),
	/*9587*/ uint16(xReadIb),
	/*9588*/ uint16(xArgRM16),
	/*9589*/ uint16(xArgImm8),
	/*9590*/ uint16(xMatch),
	/*9591*/ uint16(xSetOp), uint16(ADD),
	/*9593*/ uint16(xReadIb),
	/*9594*/ uint16(xArgRM32),
	/*9595*/ uint16(xArgImm8),
	/*9596*/ uint16(xMatch),
	/*9597*/ uint16(xCondDataSize), 9585, 9591, 9601,
	/*9601*/ uint16(xSetOp), uint16(ADD),
	/*9603*/ uint16(xReadIb),
	/*9604*/ uint16(xArgRM64),
	/*9605*/ uint16(xArgImm8),
	/*9606*/ uint16(xMatch),
	/*9607*/ uint16(xCondIs64), 9610, 9626,
	/*9610*/ uint16(xCondDataSize), 9614, 9620, 0,
	/*9614*/ uint16(xSetOp), uint16(OR),
	/*9616*/ uint16(xReadIb),
	/*9617*/ uint16(xArgRM16),
	/*9618*/ uint16(xArgImm8),
	/*9619*/ uint16(xMatch),
	/*9620*/ uint16(xSetOp), uint16(OR),
	/*9622*/ uint16(xReadIb),
	/*9623*/ uint16(xArgRM32),
	/*9624*/ uint16(xArgImm8),
	/*9625*/ uint16(xMatch),
	/*9626*/ uint16(xCondDataSize), 9614, 9620, 9630,
	/*9630*/ uint16(xSetOp), uint16(OR),
	/*9632*/ uint16(xReadIb),
	/*9633*/ uint16(xArgRM64),
	/*9634*/ uint16(xArgImm8),
	/*9635*/ uint16(xMatch),
	/*9636*/ uint16(xCondIs64), 9639, 9655,
	/*9639*/ uint16(xCondDataSize), 9643, 9649, 0,
	/*9643*/ uint16(xSetOp), uint16(ADC),
	/*9645*/ uint16(xReadIb),
	/*9646*/ uint16(xArgRM16),
	/*9647*/ uint16(xArgImm8),
	/*9648*/ uint16(xMatch),
	/*9649*/ uint16(xSetOp), uint16(ADC),
	/*9651*/ uint16(xReadIb),
	/*9652*/ uint16(xArgRM32),
	/*9653*/ uint16(xArgImm8),
	/*9654*/ uint16(xMatch),
	/*9655*/ uint16(xCondDataSize), 9643, 9649, 9659,
	/*9659*/ uint16(xSetOp), uint16(ADC),
	/*9661*/ uint16(xReadIb),
	/*9662*/ uint16(xArgRM64),
	/*9663*/ uint16(xArgImm8),
	/*9664*/ uint16(xMatch),
	/*9665*/ uint16(xCondIs64), 9668, 9684,
	/*9668*/ uint16(xCondDataSize), 9672, 9678, 0,
	/*9672*/ uint16(xSetOp), uint16(SBB),
	/*9674*/ uint16(xReadIb),
	/*9675*/ uint16(xArgRM16),
	/*9676*/ uint16(xArgImm8),
	/*9677*/ uint16(xMatch),
	/*9678*/ uint16(xSetOp), uint16(SBB),
	/*9680*/ uint16(xReadIb),
	/*9681*/ uint16(xArgRM32),
	/*9682*/ uint16(xArgImm8),
	/*9683*/ uint16(xMatch),
	/*9684*/ uint16(xCondDataSize), 9672, 9678, 9688,
	/*9688*/ uint16(xSetOp), uint16(SBB),
	/*9690*/ uint16(xReadIb),
	/*9691*/ uint16(xArgRM64),
	/*9692*/ uint16(xArgImm8),
	/*9693*/ uint16(xMatch),
	/*9694*/ uint16(xCondIs64), 9697, 9713,
	/*9697*/ uint16(xCondDataSize), 9701, 9707, 0,
	/*9701*/ uint16(xSetOp), uint16(AND),
	/*9703*/ uint16(xReadIb),
	/*9704*/ uint16(xArgRM16),
	/*9705*/ uint16(xArgImm8),
	/*9706*/ uint16(xMatch),
	/*9707*/ uint16(xSetOp), uint16(AND),
	/*9709*/ uint16(xReadIb),
	/*9710*/ uint16(xArgRM32),
	/*9711*/ uint16(xArgImm8),
	/*9712*/ uint16(xMatch),
	/*9713*/ uint16(xCondDataSize), 9701, 9707, 9717,
	/*9717*/ uint16(xSetOp), uint16(AND),
	/*9719*/ uint16(xReadIb),
	/*9720*/ uint16(xArgRM64),
	/*9721*/ uint16(xArgImm8),
	/*9722*/ uint16(xMatch),
	/*9723*/ uint16(xCondIs64), 9726, 9742,
	/*9726*/ uint16(xCondDataSize), 9730, 9736, 0,
	/*9730*/ uint16(xSetOp), uint16(SUB),
	/*9732*/ uint16(xReadIb),
	/*9733*/ uint16(xArgRM16),
	/*9734*/ uint16(xArgImm8),
	/*9735*/ uint16(xMatch),
	/*9736*/ uint16(xSetOp), uint16(SUB),
	/*9738*/ uint16(xReadIb),
	/*9739*/ uint16(xArgRM32),
	/*9740*/ uint16(xArgImm8),
	/*9741*/ uint16(xMatch),
	/*9742*/ uint16(xCondDataSize), 9730, 9736, 9746,
	/*9746*/ uint16(xSetOp), uint16(SUB),
	/*9748*/ uint16(xReadIb),
	/*9749*/ uint16(xArgRM64),
	/*9750*/ uint16(xArgImm8),
	/*9751*/ uint16(xMatch),
	/*9752*/ uint16(xCondIs64), 9755, 9771,
	/*9755*/ uint16(xCondDataSize), 9759, 9765, 0,
	/*9759*/ uint16(xSetOp), uint16(XOR),
	/*9761*/ uint16(xReadIb),
	/*9762*/ uint16(xArgRM16),
	/*9763*/ uint16(xArgImm8),
	/*9764*/ uint16(xMatch),
	/*9765*/ uint16(xSetOp), uint16(XOR),
	/*9767*/ uint16(xReadIb),
	/*9768*/ uint16(xArgRM32),
	/*9769*/ uint16(xArgImm8),
	/*9770*/ uint16(xMatch),
	/*9771*/ uint16(xCondDataSize), 9759, 9765, 9775,
	/*9775*/ uint16(xSetOp), uint16(XOR),
	/*9777*/ uint16(xReadIb),
	/*9778*/ uint16(xArgRM64),
	/*9779*/ uint16(xArgImm8),
	/*9780*/ uint16(xMatch),
	/*9781*/ uint16(xCondIs64), 9784, 9800,
	/*9784*/ uint16(xCondDataSize), 9788, 9794, 0,
	/*9788*/ uint16(xSetOp), uint16(CMP),
	/*9790*/ uint16(xReadIb),
	/*9791*/ uint16(xArgRM16),
	/*9792*/ uint16(xArgImm8),
	/*9793*/ uint16(xMatch),
	/*9794*/ uint16(xSetOp), uint16(CMP),
	/*9796*/ uint16(xReadIb),
	/*9797*/ uint16(xArgRM32),
	/*9798*/ uint16(xArgImm8),
	/*9799*/ uint16(xMatch),
	/*9800*/ uint16(xCondDataSize), 9788, 9794, 9804,
	/*9804*/ uint16(xSetOp), uint16(CMP),
	/*9806*/ uint16(xReadIb),
	/*9807*/ uint16(xArgRM64),
	/*9808*/ uint16(xArgImm8),
	/*9809*/ uint16(xMatch),
	/*9810*/ uint16(xSetOp), uint16(TEST),
	/*9812*/ uint16(xReadSlashR),
	/*9813*/ uint16(xArgRM8),
	/*9814*/ uint16(xArgR8),
	/*9815*/ uint16(xMatch),
	/*9816*/ uint16(xCondIs64), 9819, 9835,
	/*9819*/ uint16(xCondDataSize), 9823, 9829, 0,
	/*9823*/ uint16(xSetOp), uint16(TEST),
	/*9825*/ uint16(xReadSlashR),
	/*9826*/ uint16(xArgRM16),
	/*9827*/ uint16(xArgR16),
	/*9828*/ uint16(xMatch),
	/*9829*/ uint16(xSetOp), uint16(TEST),
	/*9831*/ uint16(xReadSlashR),
	/*9832*/ uint16(xArgRM32),
	/*9833*/ uint16(xArgR32),
	/*9834*/ uint16(xMatch),
	/*9835*/ uint16(xCondDataSize), 9823, 9829, 9839,
	/*9839*/ uint16(xSetOp), uint16(TEST),
	/*9841*/ uint16(xReadSlashR),
	/*9842*/ uint16(xArgRM64),
	/*9843*/ uint16(xArgR64),
	/*9844*/ uint16(xMatch),
	/*9845*/ uint16(xSetOp), uint16(XCHG),
	/*9847*/ uint16(xReadSlashR),
	/*9848*/ uint16(xArgRM8),
	/*9849*/ uint16(xArgR8),
	/*9850*/ uint16(xMatch),
	/*9851*/ uint16(xCondIs64), 9854, 9870,
	/*9854*/ uint16(xCondDataSize), 9858, 9864, 0,
	/*9858*/ uint16(xSetOp), uint16(XCHG),
	/*9860*/ uint16(xReadSlashR),
	/*9861*/ uint16(xArgRM16),
	/*9862*/ uint16(xArgR16),
	/*9863*/ uint16(xMatch),
	/*9864*/ uint16(xSetOp), uint16(XCHG),
	/*9866*/ uint16(xReadSlashR),
	/*9867*/ uint16(xArgRM32),
	/*9868*/ uint16(xArgR32),
	/*9869*/ uint16(xMatch),
	/*9870*/ uint16(xCondDataSize), 9858, 9864, 9874,
	/*9874*/ uint16(xSetOp), uint16(XCHG),
	/*9876*/ uint16(xReadSlashR),
	/*9877*/ uint16(xArgRM64),
	/*9878*/ uint16(xArgR64),
	/*9879*/ uint16(xMatch),
	/*9880*/ uint16(xSetOp), uint16(MOV),
	/*9882*/ uint16(xReadSlashR),
	/*9883*/ uint16(xArgRM8),
	/*9884*/ uint16(xArgR8),
	/*9885*/ uint16(xMatch),
	/*9886*/ uint16(xCondDataSize), 9890, 9896, 9902,
	/*9890*/ uint16(xSetOp), uint16(MOV),
	/*9892*/ uint16(xReadSlashR),
	/*9893*/ uint16(xArgRM16),
	/*9894*/ uint16(xArgR16),
	/*9895*/ uint16(xMatch),
	/*9896*/ uint16(xSetOp), uint16(MOV),
	/*9898*/ uint16(xReadSlashR),
	/*9899*/ uint16(xArgRM32),
	/*9900*/ uint16(xArgR32),
	/*9901*/ uint16(xMatch),
	/*9902*/ uint16(xSetOp), uint16(MOV),
	/*9904*/ uint16(xReadSlashR),
	/*9905*/ uint16(xArgRM64),
	/*9906*/ uint16(xArgR64),
	/*9907*/ uint16(xMatch),
	/*9908*/ uint16(xSetOp), uint16(MOV),
	/*9910*/ uint16(xReadSlashR),
	/*9911*/ uint16(xArgR8),
	/*9912*/ uint16(xArgRM8),
	/*9913*/ uint16(xMatch),
	/*9914*/ uint16(xCondDataSize), 9918, 9924, 9930,
	/*9918*/ uint16(xSetOp), uint16(MOV),
	/*9920*/ uint16(xReadSlashR),
	/*9921*/ uint16(xArgR16),
	/*9922*/ uint16(xArgRM16),
	/*9923*/ uint16(xMatch),
	/*9924*/ uint16(xSetOp), uint16(MOV),
	/*9926*/ uint16(xReadSlashR),
	/*9927*/ uint16(xArgR32),
	/*9928*/ uint16(xArgRM32),
	/*9929*/ uint16(xMatch),
	/*9930*/ uint16(xSetOp), uint16(MOV),
	/*9932*/ uint16(xReadSlashR),
	/*9933*/ uint16(xArgR64),
	/*9934*/ uint16(xArgRM64),
	/*9935*/ uint16(xMatch),
	/*9936*/ uint16(xCondIs64), 9939, 9955,
	/*9939*/ uint16(xCondDataSize), 9943, 9949, 0,
	/*9943*/ uint16(xSetOp), uint16(MOV),
	/*9945*/ uint16(xReadSlashR),
	/*9946*/ uint16(xArgRM16),
	/*9947*/ uint16(xArgSreg),
	/*9948*/ uint16(xMatch),
	/*9949*/ uint16(xSetOp), uint16(MOV),
	/*9951*/ uint16(xReadSlashR),
	/*9952*/ uint16(xArgR32M16),
	/*9953*/ uint16(xArgSreg),
	/*9954*/ uint16(xMatch),
	/*9955*/ uint16(xCondDataSize), 9943, 9949, 9959,
	/*9959*/ uint16(xSetOp), uint16(MOV),
	/*9961*/ uint16(xReadSlashR),
	/*9962*/ uint16(xArgR64M16),
	/*9963*/ uint16(xArgSreg),
	/*9964*/ uint16(xMatch),
	/*9965*/ uint16(xCondIs64), 9968, 9984,
	/*9968*/ uint16(xCondDataSize), 9972, 9978, 0,
	/*9972*/ uint16(xSetOp), uint16(LEA),
	/*9974*/ uint16(xReadSlashR),
	/*9975*/ uint16(xArgR16),
	/*9976*/ uint16(xArgM),
	/*9977*/ uint16(xMatch),
	/*9978*/ uint16(xSetOp), uint16(LEA),
	/*9980*/ uint16(xReadSlashR),
	/*9981*/ uint16(xArgR32),
	/*9982*/ uint16(xArgM),
	/*9983*/ uint16(xMatch),
	/*9984*/ uint16(xCondDataSize), 9972, 9978, 9988,
	/*9988*/ uint16(xSetOp), uint16(LEA),
	/*9990*/ uint16(xReadSlashR),
	/*9991*/ uint16(xArgR64),
	/*9992*/ uint16(xArgM),
	/*9993*/ uint16(xMatch),
	/*9994*/ uint16(xCondIs64), 9997, 10013,
	/*9997*/ uint16(xCondDataSize), 10001, 10007, 0,
	/*10001*/ uint16(xSetOp), uint16(MOV),
	/*10003*/ uint16(xReadSlashR),
	/*10004*/ uint16(xArgSreg),
	/*10005*/ uint16(xArgRM16),
	/*10006*/ uint16(xMatch),
	/*10007*/ uint16(xSetOp), uint16(MOV),
	/*10009*/ uint16(xReadSlashR),
	/*10010*/ uint16(xArgSreg),
	/*10011*/ uint16(xArgR32M16),
	/*10012*/ uint16(xMatch),
	/*10013*/ uint16(xCondDataSize), 10001, 10007, 10017,
	/*10017*/ uint16(xSetOp), uint16(MOV),
	/*10019*/ uint16(xReadSlashR),
	/*10020*/ uint16(xArgSreg),
	/*10021*/ uint16(xArgR64M16),
	/*10022*/ uint16(xMatch),
	/*10023*/ uint16(xCondSlashR),
	10032, // 0
	0,     // 1
	0,     // 2
	0,     // 3
	0,     // 4
	0,     // 5
	0,     // 6
	0,     // 7
	/*10032*/ uint16(xCondIs64), 10035, 10047,
	/*10035*/ uint16(xCondDataSize), 10039, 10043, 0,
	/*10039*/ uint16(xSetOp), uint16(POP),
	/*10041*/ uint16(xArgRM16),
	/*10042*/ uint16(xMatch),
	/*10043*/ uint16(xSetOp), uint16(POP),
	/*10045*/ uint16(xArgRM32),
	/*10046*/ uint16(xMatch),
	/*10047*/ uint16(xCondDataSize), 10039, 10051, 10055,
	/*10051*/ uint16(xSetOp), uint16(POP),
	/*10053*/ uint16(xArgRM64),
	/*10054*/ uint16(xMatch),
	/*10055*/ uint16(xSetOp), uint16(POP),
	/*10057*/ uint16(xArgRM64),
	/*10058*/ uint16(xMatch),
	/*10059*/ uint16(xCondIs64), 10062, 10076,
	/*10062*/ uint16(xCondDataSize), 10066, 10071, 0,
	/*10066*/ uint16(xSetOp), uint16(XCHG),
	/*10068*/ uint16(xArgR16op),
	/*10069*/ uint16(xArgAX),
	/*10070*/ uint16(xMatch),
	/*10071*/ uint16(xSetOp), uint16(XCHG),
	/*10073*/ uint16(xArgR32op),
	/*10074*/ uint16(xArgEAX),
	/*10075*/ uint16(xMatch),
	/*10076*/ uint16(xCondDataSize), 10066, 10071, 10080,
	/*10080*/ uint16(xSetOp), uint16(XCHG),
	/*10082*/ uint16(xArgR64op),
	/*10083*/ uint16(xArgRAX),
	/*10084*/ uint16(xMatch),
	/*10085*/ uint16(xCondIs64), 10088, 10098,
	/*10088*/ uint16(xCondDataSize), 10092, 10095, 0,
	/*10092*/ uint16(xSetOp), uint16(CBW),
	/*10094*/ uint16(xMatch),
	/*10095*/ uint16(xSetOp), uint16(CWDE),
	/*10097*/ uint16(xMatch),
	/*10098*/ uint16(xCondDataSize), 10092, 10095, 10102,
	/*10102*/ uint16(xSetOp), uint16(CDQE),
	/*10104*/ uint16(xMatch),
	/*10105*/ uint16(xCondIs64), 10108, 10118,
	/*10108*/ uint16(xCondDataSize), 10112, 10115, 0,
	/*10112*/ uint16(xSetOp), uint16(CWD),
	/*10114*/ uint16(xMatch),
	/*10115*/ uint16(xSetOp), uint16(CDQ),
	/*10117*/ uint16(xMatch),
	/*10118*/ uint16(xCondDataSize), 10112, 10115, 10122,
	/*10122*/ uint16(xSetOp), uint16(CQO),
	/*10124*/ uint16(xMatch),
	/*10125*/ uint16(xCondIs64), 10128, 0,
	/*10128*/ uint16(xCondDataSize), 10132, 10137, 0,
	/*10132*/ uint16(xSetOp), uint16(LCALL),
	/*10134*/ uint16(xReadCd),
	/*10135*/ uint16(xArgPtr16colon16),
	/*10136*/ uint16(xMatch),
	/*10137*/ uint16(xSetOp), uint16(LCALL),
	/*10139*/ uint16(xReadCp),
	/*10140*/ uint16(xArgPtr16colon32),
	/*10141*/ uint16(xMatch),
	/*10142*/ uint16(xSetOp), uint16(FWAIT),
	/*10144*/ uint16(xMatch),
	/*10145*/ uint16(xCondIs64), 10148, 10158,
	/*10148*/ uint16(xCondDataSize), 10152, 10155, 0,
	/*10152*/ uint16(xSetOp), uint16(PUSHF),
	/*10154*/ uint16(xMatch),
	/*10155*/ uint16(xSetOp), uint16(PUSHFD),
	/*10157*/ uint16(xMatch),
	/*10158*/ uint16(xCondDataSize), 10152, 10162, 10165,
	/*10162*/ uint16(xSetOp), uint16(PUSHFQ),
	/*10164*/ uint16(xMatch),
	/*10165*/ uint16(xSetOp), uint16(PUSHFQ),
	/*10167*/ uint16(xMatch),
	/*10168*/ uint16(xCondIs64), 10171, 10181,
	/*10171*/ uint16(xCondDataSize), 10175, 10178, 0,
	/*10175*/ uint16(xSetOp), uint16(POPF),
	/*10177*/ uint16(xMatch),
	/*10178*/ uint16(xSetOp), uint16(POPFD),
	/*10180*/ uint16(xMatch),
	/*10181*/ uint16(xCondDataSize), 10175, 10185, 10188,
	/*10185*/ uint16(xSetOp), uint16(POPFQ),
	/*10187*/ uint16(xMatch),
	/*10188*/ uint16(xSetOp), uint16(POPFQ),
	/*10190*/ uint16(xMatch),
	/*10191*/ uint16(xSetOp), uint16(SAHF),
	/*10193*/ uint16(xMatch),
	/*10194*/ uint16(xSetOp), uint16(LAHF),
	/*10196*/ uint16(xMatch),
	/*10197*/ uint16(xCondIs64), 10200, 10206,
	/*10200*/ uint16(xSetOp), uint16(MOV),
	/*10202*/ uint16(xReadCm),
	/*10203*/ uint16(xArgAL),
	/*10204*/ uint16(xArgMoffs8),
	/*10205*/ uint16(xMatch),
	/*10206*/ uint16(xCondDataSize), 10200, 10200, 10210,
	/*10210*/ uint16(xSetOp), uint16(MOV),
	/*10212*/ uint16(xReadCm),
	/*10213*/ uint16(xArgAL),
	/*10214*/ uint16(xArgMoffs8),
	/*10215*/ uint16(xMatch),
	/*10216*/ uint16(xCondDataSize), 10220, 10226, 10232,
	/*10220*/ uint16(xSetOp), uint16(MOV),
	/*10222*/ uint16(xReadCm),
	/*10223*/ uint16(xArgAX),
	/*10224*/ uint16(xArgMoffs16),
	/*10225*/ uint16(xMatch),
	/*10226*/ uint16(xSetOp), uint16(MOV),
	/*10228*/ uint16(xReadCm),
	/*10229*/ uint16(xArgEAX),
	/*10230*/ uint16(xArgMoffs32),
	/*10231*/ uint16(xMatch),
	/*10232*/ uint16(xSetOp), uint16(MOV),
	/*10234*/ uint16(xReadCm),
	/*10235*/ uint16(xArgRAX),
	/*10236*/ uint16(xArgMoffs64),
	/*10237*/ uint16(xMatch),
	/*10238*/ uint16(xCondIs64), 10241, 10247,
	/*10241*/ uint16(xSetOp), uint16(MOV),
	/*10243*/ uint16(xReadCm),
	/*10244*/ uint16(xArgMoffs8),
	/*10245*/ uint16(xArgAL),
	/*10246*/ uint16(xMatch),
	/*10247*/ uint16(xCondDataSize), 10241, 10241, 10251,
	/*10251*/ uint16(xSetOp), uint16(MOV),
	/*10253*/ uint16(xReadCm),
	/*10254*/ uint16(xArgMoffs8),
	/*10255*/ uint16(xArgAL),
	/*10256*/ uint16(xMatch),
	/*10257*/ uint16(xCondDataSize), 10261, 10267, 10273,
	/*10261*/ uint16(xSetOp), uint16(MOV),
	/*10263*/ uint16(xReadCm),
	/*10264*/ uint16(xArgMoffs16),
	/*10265*/ uint16(xArgAX),
	/*10266*/ uint16(xMatch),
	/*10267*/ uint16(xSetOp), uint16(MOV),
	/*10269*/ uint16(xReadCm),
	/*10270*/ uint16(xArgMoffs32),
	/*10271*/ uint16(xArgEAX),
	/*10272*/ uint16(xMatch),
	/*10273*/ uint16(xSetOp), uint16(MOV),
	/*10275*/ uint16(xReadCm),
	/*10276*/ uint16(xArgMoffs64),
	/*10277*/ uint16(xArgRAX),
	/*10278*/ uint16(xMatch),
	/*10279*/ uint16(xSetOp), uint16(MOVSB),
	/*10281*/ uint16(xMatch),
	/*10282*/ uint16(xCondIs64), 10285, 10295,
	/*10285*/ uint16(xCondDataSize), 10289, 10292, 0,
	/*10289*/ uint16(xSetOp), uint16(MOVSW),
	/*10291*/ uint16(xMatch),
	/*10292*/ uint16(xSetOp), uint16(MOVSD),
	/*10294*/ uint16(xMatch),
	/*10295*/ uint16(xCondDataSize), 10289, 10292, 10299,
	/*10299*/ uint16(xSetOp), uint16(MOVSQ),
	/*10301*/ uint16(xMatch),
	/*10302*/ uint16(xSetOp), uint16(CMPSB),
	/*10304*/ uint16(xMatch),
	/*10305*/ uint16(xCondIs64), 10308, 10318,
	/*10308*/ uint16(xCondDataSize), 10312, 10315, 0,
	/*10312*/ uint16(xSetOp), uint16(CMPSW),
	/*10314*/ uint16(xMatch),
	/*10315*/ uint16(xSetOp), uint16(CMPSD),
	/*10317*/ uint16(xMatch),
	/*10318*/ uint16(xCondDataSize), 10312, 10315, 10322,
	/*10322*/ uint16(xSetOp), uint16(CMPSQ),
	/*10324*/ uint16(xMatch),
	/*10325*/ uint16(xSetOp), uint16(TEST),
	/*10327*/ uint16(xReadIb),
	/*10328*/ uint16(xArgAL),
	/*10329*/ uint16(xArgImm8u),
	/*10330*/ uint16(xMatch),
	/*10331*/ uint16(xCondIs64), 10334, 10350,
	/*10334*/ uint16(xCondDataSize), 10338, 10344, 0,
	/*10338*/ uint16(xSetOp), uint16(TEST),
	/*10340*/ uint16(xReadIw),
	/*10341*/ uint16(xArgAX),
	/*10342*/ uint16(xArgImm16),
	/*10343*/ uint16(xMatch),
	/*10344*/ uint16(xSetOp), uint16(TEST),
	/*10346*/ uint16(xReadId),
	/*10347*/ uint16(xArgEAX),
	/*10348*/ uint16(xArgImm32),
	/*10349*/ uint16(xMatch),
	/*10350*/ uint16(xCondDataSize), 10338, 10344, 10354,
	/*10354*/ uint16(xSetOp), uint16(TEST),
	/*10356*/ uint16(xReadId),
	/*10357*/ uint16(xArgRAX),
	/*10358*/ uint16(xArgImm32),
	/*10359*/ uint16(xMatch),
	/*10360*/ uint16(xSetOp), uint16(STOSB),
	/*10362*/ uint16(xMatch),
	/*10363*/ uint16(xCondIs64), 10366, 10376,
	/*10366*/ uint16(xCondDataSize), 10370, 10373, 0,
	/*10370*/ uint16(xSetOp), uint16(STOSW),
	/*10372*/ uint16(xMatch),
	/*10373*/ uint16(xSetOp), uint16(STOSD),
	/*10375*/ uint16(xMatch),
	/*10376*/ uint16(xCondDataSize), 10370, 10373, 10380,
	/*10380*/ uint16(xSetOp), uint16(STOSQ),
	/*10382*/ uint16(xMatch),
	/*10383*/ uint16(xSetOp), uint16(LODSB),
	/*10385*/ uint16(xMatch),
	/*10386*/ uint16(xCondIs64), 10389, 10399,
	/*10389*/ uint16(xCondDataSize), 10393, 10396, 0,
	/*10393*/ uint16(xSetOp), uint16(LODSW),
	/*10395*/ uint16(xMatch),
	/*10396*/ uint16(xSetOp), uint16(LODSD),
	/*10398*/ uint16(xMatch),
	/*10399*/ uint16(xCondDataSize), 10393, 10396, 10403,
	/*10403*/ uint16(xSetOp), uint16(LODSQ),
	/*10405*/ uint16(xMatch),
	/*10406*/ uint16(xSetOp), uint16(SCASB),
	/*10408*/ uint16(xMatch),
	/*10409*/ uint16(xCondIs64), 10412, 10422,
	/*10412*/ uint16(xCondDataSize), 10416, 10419, 0,
	/*10416*/ uint16(xSetOp), uint16(SCASW),
	/*10418*/ uint16(xMatch),
	/*10419*/ uint16(xSetOp), uint16(SCASD),
	/*10421*/ uint16(xMatch),
	/*10422*/ uint16(xCondDataSize), 10416, 10419, 10426,
	/*10426*/ uint16(xSetOp), uint16(SCASQ),
	/*10428*/ uint16(xMatch),
	/*10429*/ uint16(xSetOp), uint16(MOV),
	/*10431*/ uint16(xReadIb),
	/*10432*/ uint16(xArgR8op),
	/*10433*/ uint16(xArgImm8u),
	/*10434*/ uint16(xMatch),
	/*10435*/ uint16(xCondIs64), 10438, 10454,
	/*10438*/ uint16(xCondDataSize), 10442, 10448, 0,
	/*10442*/ uint16(xSetOp), uint16(MOV),
	/*10444*/ uint16(xReadIw),
	/*10445*/ uint16(xArgR16op),
	/*10446*/ uint16(xArgImm16),
	/*10447*/ uint16(xMatch),
	/*10448*/ uint16(xSetOp), uint16(MOV),
	/*10450*/ uint16(xReadId),
	/*10451*/ uint16(xArgR32op),
	/*10452*/ uint16(xArgImm32),
	/*10453*/ uint16(xMatch),
	/*10454*/ uint16(xCondDataSize), 10442, 10448, 10458,
	/*10458*/ uint16(xSetOp), uint16(MOV),
	/*10460*/ uint16(xReadIo),
	/*10461*/ uint16(xArgR64op),
	/*10462*/ uint16(xArgImm64),
	/*10463*/ uint16(xMatch),
	/*10464*/ uint16(xCondSlashR),
	10473, // 0
	10479, // 1
	10485, // 2
	10491, // 3
	10497, // 4
	10503, // 5
	0,     // 6
	10509, // 7
	/*10473*/ uint16(xSetOp), uint16(ROL),
	/*10475*/ uint16(xReadIb),
	/*10476*/ uint16(xArgRM8),
	/*10477*/ uint16(xArgImm8u),
	/*10478*/ uint16(xMatch),
	/*10479*/ uint16(xSetOp), uint16(ROR),
	/*10481*/ uint16(xReadIb),
	/*10482*/ uint16(xArgRM8),
	/*10483*/ uint16(xArgImm8u),
	/*10484*/ uint16(xMatch),
	/*10485*/ uint16(xSetOp), uint16(RCL),
	/*10487*/ uint16(xReadIb),
	/*10488*/ uint16(xArgRM8),
	/*10489*/ uint16(xArgImm8u),
	/*10490*/ uint16(xMatch),
	/*10491*/ uint16(xSetOp), uint16(RCR),
	/*10493*/ uint16(xReadIb),
	/*10494*/ uint16(xArgRM8),
	/*10495*/ uint16(xArgImm8u),
	/*10496*/ uint16(xMatch),
	/*10497*/ uint16(xSetOp), uint16(SHL),
	/*10499*/ uint16(xReadIb),
	/*10500*/ uint16(xArgRM8),
	/*10501*/ uint16(xArgImm8u),
	/*10502*/ uint16(xMatch),
	/*10503*/ uint16(xSetOp), uint16(SHR),
	/*10505*/ uint16(xReadIb),
	/*10506*/ uint16(xArgRM8),
	/*10507*/ uint16(xArgImm8u),
	/*10508*/ uint16(xMatch),
	/*10509*/ uint16(xSetOp), uint16(SAR),
	/*10511*/ uint16(xReadIb),
	/*10512*/ uint16(xArgRM8),
	/*10513*/ uint16(xArgImm8u),
	/*10514*/ uint16(xMatch),
	/*10515*/ uint16(xCondSlashR),
	10524, // 0
	10546, // 1
	10568, // 2
	10597, // 3
	10626, // 4
	10655, // 5
	0,     // 6
	10684, // 7
	/*10524*/ uint16(xCondDataSize), 10528, 10534, 10540,
	/*10528*/ uint16(xSetOp), uint16(ROL),
	/*10530*/ uint16(xReadIb),
	/*10531*/ uint16(xArgRM16),
	/*10532*/ uint16(xArgImm8u),
	/*10533*/ uint16(xMatch),
	/*10534*/ uint16(xSetOp), uint16(ROL),
	/*10536*/ uint16(xReadIb),
	/*10537*/ uint16(xArgRM32),
	/*10538*/ uint16(xArgImm8u),
	/*10539*/ uint16(xMatch),
	/*10540*/ uint16(xSetOp), uint16(ROL),
	/*10542*/ uint16(xReadIb),
	/*10543*/ uint16(xArgRM64),
	/*10544*/ uint16(xArgImm8u),
	/*10545*/ uint16(xMatch),
	/*10546*/ uint16(xCondDataSize), 10550, 10556, 10562,
	/*10550*/ uint16(xSetOp), uint16(ROR),
	/*10552*/ uint16(xReadIb),
	/*10553*/ uint16(xArgRM16),
	/*10554*/ uint16(xArgImm8u),
	/*10555*/ uint16(xMatch),
	/*10556*/ uint16(xSetOp), uint16(ROR),
	/*10558*/ uint16(xReadIb),
	/*10559*/ uint16(xArgRM32),
	/*10560*/ uint16(xArgImm8u),
	/*10561*/ uint16(xMatch),
	/*10562*/ uint16(xSetOp), uint16(ROR),
	/*10564*/ uint16(xReadIb),
	/*10565*/ uint16(xArgRM64),
	/*10566*/ uint16(xArgImm8u),
	/*10567*/ uint16(xMatch),
	/*10568*/ uint16(xCondIs64), 10571, 10587,
	/*10571*/ uint16(xCondDataSize), 10575, 10581, 0,
	/*10575*/ uint16(xSetOp), uint16(RCL),
	/*10577*/ uint16(xReadIb),
	/*10578*/ uint16(xArgRM16),
	/*10579*/ uint16(xArgImm8u),
	/*10580*/ uint16(xMatch),
	/*10581*/ uint16(xSetOp), uint16(RCL),
	/*10583*/ uint16(xReadIb),
	/*10584*/ uint16(xArgRM32),
	/*10585*/ uint16(xArgImm8u),
	/*10586*/ uint16(xMatch),
	/*10587*/ uint16(xCondDataSize), 10575, 10581, 10591,
	/*10591*/ uint16(xSetOp), uint16(RCL),
	/*10593*/ uint16(xReadIb),
	/*10594*/ uint16(xArgRM64),
	/*10595*/ uint16(xArgImm8u),
	/*10596*/ uint16(xMatch),
	/*10597*/ uint16(xCondIs64), 10600, 10616,
	/*10600*/ uint16(xCondDataSize), 10604, 10610, 0,
	/*10604*/ uint16(xSetOp), uint16(RCR),
	/*10606*/ uint16(xReadIb),
	/*10607*/ uint16(xArgRM16),
	/*10608*/ uint16(xArgImm8u),
	/*10609*/ uint16(xMatch),
	/*10610*/ uint16(xSetOp), uint16(RCR),
	/*10612*/ uint16(xReadIb),
	/*10613*/ uint16(xArgRM32),
	/*10614*/ uint16(xArgImm8u),
	/*10615*/ uint16(xMatch),
	/*10616*/ uint16(xCondDataSize), 10604, 10610, 10620,
	/*10620*/ uint16(xSetOp), uint16(RCR),
	/*10622*/ uint16(xReadIb),
	/*10623*/ uint16(xArgRM64),
	/*10624*/ uint16(xArgImm8u),
	/*10625*/ uint16(xMatch),
	/*10626*/ uint16(xCondIs64), 10629, 10645,
	/*10629*/ uint16(xCondDataSize), 10633, 10639, 0,
	/*10633*/ uint16(xSetOp), uint16(SHL),
	/*10635*/ uint16(xReadIb),
	/*10636*/ uint16(xArgRM16),
	/*10637*/ uint16(xArgImm8u),
	/*10638*/ uint16(xMatch),
	/*10639*/ uint16(xSetOp), uint16(SHL),
	/*10641*/ uint16(xReadIb),
	/*10642*/ uint16(xArgRM32),
	/*10643*/ uint16(xArgImm8u),
	/*10644*/ uint16(xMatch),
	/*10645*/ uint16(xCondDataSize), 10633, 10639, 10649,
	/*10649*/ uint16(xSetOp), uint16(SHL),
	/*10651*/ uint16(xReadIb),
	/*10652*/ uint16(xArgRM64),
	/*10653*/ uint16(xArgImm8u),
	/*10654*/ uint16(xMatch),
	/*10655*/ uint16(xCondIs64), 10658, 10674,
	/*10658*/ uint16(xCondDataSize), 10662, 10668, 0,
	/*10662*/ uint16(xSetOp), uint16(SHR),
	/*10664*/ uint16(xReadIb),
	/*10665*/ uint16(xArgRM16),
	/*10666*/ uint16(xArgImm8u),
	/*10667*/ uint16(xMatch),
	/*10668*/ uint16(xSetOp), uint16(SHR),
	/*10670*/ uint16(xReadIb),
	/*10671*/ uint16(xArgRM32),
	/*10672*/ uint16(xArgImm8u),
	/*10673*/ uint16(xMatch),
	/*10674*/ uint16(xCondDataSize), 10662, 10668, 10678,
	/*10678*/ uint16(xSetOp), uint16(SHR),
	/*10680*/ uint16(xReadIb),
	/*10681*/ uint16(xArgRM64),
	/*10682*/ uint16(xArgImm8u),
	/*10683*/ uint16(xMatch),
	/*10684*/ uint16(xCondIs64), 10687, 10703,
	/*10687*/ uint16(xCondDataSize), 10691, 10697, 0,
	/*10691*/ uint16(xSetOp), uint16(SAR),
	/*10693*/ uint16(xReadIb),
	/*10694*/ uint16(xArgRM16),
	/*10695*/ uint16(xArgImm8u),
	/*10696*/ uint16(xMatch),
	/*10697*/ uint16(xSetOp), uint16(SAR),
	/*10699*/ uint16(xReadIb),
	/*10700*/ uint16(xArgRM32),
	/*10701*/ uint16(xArgImm8u),
	/*10702*/ uint16(xMatch),
	/*10703*/ uint16(xCondDataSize), 10691, 10697, 10707,
	/*10707*/ uint16(xSetOp), uint16(SAR),
	/*10709*/ uint16(xReadIb),
	/*10710*/ uint16(xArgRM64),
	/*10711*/ uint16(xArgImm8u),
	/*10712*/ uint16(xMatch),
	/*10713*/ uint16(xSetOp), uint16(RET),
	/*10715*/ uint16(xReadIw),
	/*10716*/ uint16(xArgImm16u),
	/*10717*/ uint16(xMatch),
	/*10718*/ uint16(xSetOp), uint16(RET),
	/*10720*/ uint16(xMatch),
	/*10721*/ uint16(xCondIs64), 10724, 0,
	/*10724*/ uint16(xCondDataSize), 10728, 10734, 0,
	/*10728*/ uint16(xSetOp), uint16(LES),
	/*10730*/ uint16(xReadSlashR),
	/*10731*/ uint16(xArgR16),
	/*10732*/ uint16(xArgM16colon16),
	/*10733*/ uint16(xMatch),
	/*10734*/ uint16(xSetOp), uint16(LES),
	/*10736*/ uint16(xReadSlashR),
	/*10737*/ uint16(xArgR32),
	/*10738*/ uint16(xArgM16colon32),
	/*10739*/ uint16(xMatch),
	/*10740*/ uint16(xCondIs64), 10743, 0,
	/*10743*/ uint16(xCondDataSize), 10747, 10753, 0,
	/*10747*/ uint16(xSetOp), uint16(LDS),
	/*10749*/ uint16(xReadSlashR),
	/*10750*/ uint16(xArgR16),
	/*10751*/ uint16(xArgM16colon16),
	/*10752*/ uint16(xMatch),
	/*10753*/ uint16(xSetOp), uint16(LDS),
	/*10755*/ uint16(xReadSlashR),
	/*10756*/ uint16(xArgR32),
	/*10757*/ uint16(xArgM16colon32),
	/*10758*/ uint16(xMatch),
	/*10759*/ uint16(xCondByte), 1,
	0xF8, 10778,
	/*10763*/ uint16(xCondSlashR),
	10772, // 0
	0,     // 1
	0,     // 2
	0,     // 3
	0,     // 4
	0,     // 5
	0,     // 6
	0,     // 7
	/*10772*/ uint16(xSetOp), uint16(MOV),
	/*10774*/ uint16(xReadIb),
	/*10775*/ uint16(xArgRM8),
	/*10776*/ uint16(xArgImm8u),
	/*10777*/ uint16(xMatch),
	/*10778*/ uint16(xSetOp), uint16(XABORT),
	/*10780*/ uint16(xReadIb),
	/*10781*/ uint16(xArgImm8u),
	/*10782*/ uint16(xMatch),
	/*10783*/ uint16(xCondByte), 1,
	0xF8, 10825,
	/*10787*/ uint16(xCondSlashR),
	10796, // 0
	0,     // 1
	0,     // 2
	0,     // 3
	0,     // 4
	0,     // 5
	0,     // 6
	0,     // 7
	/*10796*/ uint16(xCondIs64), 10799, 10815,
	/*10799*/ uint16(xCondDataSize), 10803, 10809, 0,
	/*10803*/ uint16(xSetOp), uint16(MOV),
	/*10805*/ uint16(xReadIw),
	/*10806*/ uint16(xArgRM16),
	/*10807*/ uint16(xArgImm16),
	/*10808*/ uint16(xMatch),
	/*10809*/ uint16(xSetOp), uint16(MOV),
	/*10811*/ uint16(xReadId),
	/*10812*/ uint16(xArgRM32),
	/*10813*/ uint16(xArgImm32),
	/*10814*/ uint16(xMatch),
	/*10815*/ uint16(xCondDataSize), 10803, 10809, 10819,
	/*10819*/ uint16(xSetOp), uint16(MOV),
	/*10821*/ uint16(xReadId),
	/*10822*/ uint16(xArgRM64),
	/*10823*/ uint16(xArgImm32),
	/*10824*/ uint16(xMatch),
	/*10825*/ uint16(xCondDataSize), 10829, 10834, 10839,
	/*10829*/ uint16(xSetOp), uint16(XBEGIN),
	/*10831*/ uint16(xReadCw),
	/*10832*/ uint16(xArgRel16),
	/*10833*/ uint16(xMatch),
	/*10834*/ uint16(xSetOp), uint16(XBEGIN),
	/*10836*/ uint16(xReadCd),
	/*10837*/ uint16(xArgRel32),
	/*10838*/ uint16(xMatch),
	/*10839*/ uint16(xSetOp), uint16(XBEGIN),
	/*10841*/ uint16(xReadCd),
	/*10842*/ uint16(xArgRel32),
	/*10843*/ uint16(xMatch),
	/*10844*/ uint16(xSetOp), uint16(ENTER),
	/*10846*/ uint16(xReadIw),
	/*10847*/ uint16(xReadIb),
	/*10848*/ uint16(xArgImm16u),
	/*10849*/ uint16(xArgImm8u),
	/*10850*/ uint16(xMatch),
	/*10851*/ uint16(xCondIs64), 10854, 10864,
	/*10854*/ uint16(xCondDataSize), 10858, 10861, 0,
	/*10858*/ uint16(xSetOp), uint16(LEAVE),
	/*10860*/ uint16(xMatch),
	/*10861*/ uint16(xSetOp), uint16(LEAVE),
	/*10863*/ uint16(xMatch),
	/*10864*/ uint16(xCondDataSize), 10858, 10868, 10871,
	/*10868*/ uint16(xSetOp), uint16(LEAVE),
	/*10870*/ uint16(xMatch),
	/*10871*/ uint16(xSetOp), uint16(LEAVE),
	/*10873*/ uint16(xMatch),
	/*10874*/ uint16(xSetOp), uint16(LRET),
	/*10876*/ uint16(xReadIw),
	/*10877*/ uint16(xArgImm16u),
	/*10878*/ uint16(xMatch),
	/*10879*/ uint16(xSetOp), uint16(LRET),
	/*10881*/ uint16(xMatch),
	/*10882*/ uint16(xSetOp), uint16(INT),
	/*10884*/ uint16(xArg3),
	/*10885*/ uint16(xMatch),
	/*10886*/ uint16(xSetOp), uint16(INT),
	/*10888*/ uint16(xReadIb),
	/*10889*/ uint16(xArgImm8u),
	/*10890*/ uint16(xMatch),
	/*10891*/ uint16(xCondIs64), 10894, 0,
	/*10894*/ uint16(xSetOp), uint16(INTO),
	/*10896*/ uint16(xMatch),
	/*10897*/ uint16(xCondIs64), 10900, 10910,
	/*10900*/ uint16(xCondDataSize), 10904, 10907, 0,
	/*10904*/ uint16(xSetOp), uint16(IRET),
	/*10906*/ uint16(xMatch),
	/*10907*/ uint16(xSetOp), uint16(IRETD),
	/*10909*/ uint16(xMatch),
	/*10910*/ uint16(xCondDataSize), 10904, 10907, 10914,
	/*10914*/ uint16(xSetOp), uint16(IRETQ),
	/*10916*/ uint16(xMatch),
	/*10917*/ uint16(xCondSlashR),
	10926, // 0
	10931, // 1
	10936, // 2
	10941, // 3
	10946, // 4
	10951, // 5
	0,     // 6
	10956, // 7
	/*10926*/ uint16(xSetOp), uint16(ROL),
	/*10928*/ uint16(xArgRM8),
	/*10929*/ uint16(xArg1),
	/*10930*/ uint16(xMatch),
	/*10931*/ uint16(xSetOp), uint16(ROR),
	/*10933*/ uint16(xArgRM8),
	/*10934*/ uint16(xArg1),
	/*10935*/ uint16(xMatch),
	/*10936*/ uint16(xSetOp), uint16(RCL),
	/*10938*/ uint16(xArgRM8),
	/*10939*/ uint16(xArg1),
	/*10940*/ uint16(xMatch),
	/*10941*/ uint16(xSetOp), uint16(RCR),
	/*10943*/ uint16(xArgRM8),
	/*10944*/ uint16(xArg1),
	/*10945*/ uint16(xMatch),
	/*10946*/ uint16(xSetOp), uint16(SHL),
	/*10948*/ uint16(xArgRM8),
	/*10949*/ uint16(xArg1),
	/*10950*/ uint16(xMatch),
	/*10951*/ uint16(xSetOp), uint16(SHR),
	/*10953*/ uint16(xArgRM8),
	/*10954*/ uint16(xArg1),
	/*10955*/ uint16(xMatch),
	/*10956*/ uint16(xSetOp), uint16(SAR),
	/*10958*/ uint16(xArgRM8),
	/*10959*/ uint16(xArg1),
	/*10960*/ uint16(xMatch),
	/*10961*/ uint16(xCondSlashR),
	10970, // 0
	10996, // 1
	11022, // 2
	11048, // 3
	11074, // 4
	11100, // 5
	0,     // 6
	11126, // 7
	/*10970*/ uint16(xCondIs64), 10973, 10987,
	/*10973*/ uint16(xCondDataSize), 10977, 10982, 0,
	/*10977*/ uint16(xSetOp), uint16(ROL),
	/*10979*/ uint16(xArgRM16),
	/*10980*/ uint16(xArg1),
	/*10981*/ uint16(xMatch),
	/*10982*/ uint16(xSetOp), uint16(ROL),
	/*10984*/ uint16(xArgRM32),
	/*10985*/ uint16(xArg1),
	/*10986*/ uint16(xMatch),
	/*10987*/ uint16(xCondDataSize), 10977, 10982, 10991,
	/*10991*/ uint16(xSetOp), uint16(ROL),
	/*10993*/ uint16(xArgRM64),
	/*10994*/ uint16(xArg1),
	/*10995*/ uint16(xMatch),
	/*10996*/ uint16(xCondIs64), 10999, 11013,
	/*10999*/ uint16(xCondDataSize), 11003, 11008, 0,
	/*11003*/ uint16(xSetOp), uint16(ROR),
	/*11005*/ uint16(xArgRM16),
	/*11006*/ uint16(xArg1),
	/*11007*/ uint16(xMatch),
	/*11008*/ uint16(xSetOp), uint16(ROR),
	/*11010*/ uint16(xArgRM32),
	/*11011*/ uint16(xArg1),
	/*11012*/ uint16(xMatch),
	/*11013*/ uint16(xCondDataSize), 11003, 11008, 11017,
	/*11017*/ uint16(xSetOp), uint16(ROR),
	/*11019*/ uint16(xArgRM64),
	/*11020*/ uint16(xArg1),
	/*11021*/ uint16(xMatch),
	/*11022*/ uint16(xCondIs64), 11025, 11039,
	/*11025*/ uint16(xCondDataSize), 11029, 11034, 0,
	/*11029*/ uint16(xSetOp), uint16(RCL),
	/*11031*/ uint16(xArgRM16),
	/*11032*/ uint16(xArg1),
	/*11033*/ uint16(xMatch),
	/*11034*/ uint16(xSetOp), uint16(RCL),
	/*11036*/ uint16(xArgRM32),
	/*11037*/ uint16(xArg1),
	/*11038*/ uint16(xMatch),
	/*11039*/ uint16(xCondDataSize), 11029, 11034, 11043,
	/*11043*/ uint16(xSetOp), uint16(RCL),
	/*11045*/ uint16(xArgRM64),
	/*11046*/ uint16(xArg1),
	/*11047*/ uint16(xMatch),
	/*11048*/ uint16(xCondIs64), 11051, 11065,
	/*11051*/ uint16(xCondDataSize), 11055, 11060, 0,
	/*11055*/ uint16(xSetOp), uint16(RCR),
	/*11057*/ uint16(xArgRM16),
	/*11058*/ uint16(xArg1),
	/*11059*/ uint16(xMatch),
	/*11060*/ uint16(xSetOp), uint16(RCR),
	/*11062*/ uint16(xArgRM32),
	/*11063*/ uint16(xArg1),
	/*11064*/ uint16(xMatch),
	/*11065*/ uint16(xCondDataSize), 11055, 11060, 11069,
	/*11069*/ uint16(xSetOp), uint16(RCR),
	/*11071*/ uint16(xArgRM64),
	/*11072*/ uint16(xArg1),
	/*11073*/ uint16(xMatch),
	/*11074*/ uint16(xCondIs64), 11077, 11091,
	/*11077*/ uint16(xCondDataSize), 11081, 11086, 0,
	/*11081*/ uint16(xSetOp), uint16(SHL),
	/*11083*/ uint16(xArgRM16),
	/*11084*/ uint16(xArg1),
	/*11085*/ uint16(xMatch),
	/*11086*/ uint16(xSetOp), uint16(SHL),
	/*11088*/ uint16(xArgRM32),
	/*11089*/ uint16(xArg1),
	/*11090*/ uint16(xMatch),
	/*11091*/ uint16(xCondDataSize), 11081, 11086, 11095,
	/*11095*/ uint16(xSetOp), uint16(SHL),
	/*11097*/ uint16(xArgRM64),
	/*11098*/ uint16(xArg1),
	/*11099*/ uint16(xMatch),
	/*11100*/ uint16(xCondIs64), 11103, 11117,
	/*11103*/ uint16(xCondDataSize), 11107, 11112, 0,
	/*11107*/ uint16(xSetOp), uint16(SHR),
	/*11109*/ uint16(xArgRM16),
	/*11110*/ uint16(xArg1),
	/*11111*/ uint16(xMatch),
	/*11112*/ uint16(xSetOp), uint16(SHR),
	/*11114*/ uint16(xArgRM32),
	/*11115*/ uint16(xArg1),
	/*11116*/ uint16(xMatch),
	/*11117*/ uint16(xCondDataSize), 11107, 11112, 11121,
	/*11121*/ uint16(xSetOp), uint16(SHR),
	/*11123*/ uint16(xArgRM64),
	/*11124*/ uint16(xArg1),
	/*11125*/ uint16(xMatch),
	/*11126*/ uint16(xCondIs64), 11129, 11143,
	/*11129*/ uint16(xCondDataSize), 11133, 11138, 0,
	/*11133*/ uint16(xSetOp), uint16(SAR),
	/*11135*/ uint16(xArgRM16),
	/*11136*/ uint16(xArg1),
	/*11137*/ uint16(xMatch),
	/*11138*/ uint16(xSetOp), uint16(SAR),
	/*11140*/ uint16(xArgRM32),
	/*11141*/ uint16(xArg1),
	/*11142*/ uint16(xMatch),
	/*11143*/ uint16(xCondDataSize), 11133, 11138, 11147,
	/*11147*/ uint16(xSetOp), uint16(SAR),
	/*11149*/ uint16(xArgRM64),
	/*11150*/ uint16(xArg1),
	/*11151*/ uint16(xMatch),
	/*11152*/ uint16(xCondSlashR),
	11161, // 0
	11166, // 1
	11171, // 2
	11176, // 3
	11181, // 4
	11186, // 5
	0,     // 6
	11191, // 7
	/*11161*/ uint16(xSetOp), uint16(ROL),
	/*11163*/ uint16(xArgRM8),
	/*11164*/ uint16(xArgCL),
	/*11165*/ uint16(xMatch),
	/*11166*/ uint16(xSetOp), uint16(ROR),
	/*11168*/ uint16(xArgRM8),
	/*11169*/ uint16(xArgCL),
	/*11170*/ uint16(xMatch),
	/*11171*/ uint16(xSetOp), uint16(RCL),
	/*11173*/ uint16(xArgRM8),
	/*11174*/ uint16(xArgCL),
	/*11175*/ uint16(xMatch),
	/*11176*/ uint16(xSetOp), uint16(RCR),
	/*11178*/ uint16(xArgRM8),
	/*11179*/ uint16(xArgCL),
	/*11180*/ uint16(xMatch),
	/*11181*/ uint16(xSetOp), uint16(SHL),
	/*11183*/ uint16(xArgRM8),
	/*11184*/ uint16(xArgCL),
	/*11185*/ uint16(xMatch),
	/*11186*/ uint16(xSetOp), uint16(SHR),
	/*11188*/ uint16(xArgRM8),
	/*11189*/ uint16(xArgCL),
	/*11190*/ uint16(xMatch),
	/*11191*/ uint16(xSetOp), uint16(SAR),
	/*11193*/ uint16(xArgRM8),
	/*11194*/ uint16(xArgCL),
	/*11195*/ uint16(xMatch),
	/*11196*/ uint16(xCondSlashR),
	11205, // 0
	11231, // 1
	11257, // 2
	11283, // 3
	11309, // 4
	11335, // 5
	0,     // 6
	11361, // 7
	/*11205*/ uint16(xCondIs64), 11208, 11222,
	/*11208*/ uint16(xCondDataSize), 11212, 11217, 0,
	/*11212*/ uint16(xSetOp), uint16(ROL),
	/*11214*/ uint16(xArgRM16),
	/*11215*/ uint16(xArgCL),
	/*11216*/ uint16(xMatch),
	/*11217*/ uint16(xSetOp), uint16(ROL),
	/*11219*/ uint16(xArgRM32),
	/*11220*/ uint16(xArgCL),
	/*11221*/ uint16(xMatch),
	/*11222*/ uint16(xCondDataSize), 11212, 11217, 11226,
	/*11226*/ uint16(xSetOp), uint16(ROL),
	/*11228*/ uint16(xArgRM64),
	/*11229*/ uint16(xArgCL),
	/*11230*/ uint16(xMatch),
	/*11231*/ uint16(xCondIs64), 11234, 11248,
	/*11234*/ uint16(xCondDataSize), 11238, 11243, 0,
	/*11238*/ uint16(xSetOp), uint16(ROR),
	/*11240*/ uint16(xArgRM16),
	/*11241*/ uint16(xArgCL),
	/*11242*/ uint16(xMatch),
	/*11243*/ uint16(xSetOp), uint16(ROR),
	/*11245*/ uint16(xArgRM32),
	/*11246*/ uint16(xArgCL),
	/*11247*/ uint16(xMatch),
	/*11248*/ uint16(xCondDataSize), 11238, 11243, 11252,
	/*11252*/ uint16(xSetOp), uint16(ROR),
	/*11254*/ uint16(xArgRM64),
	/*11255*/ uint16(xArgCL),
	/*11256*/ uint16(xMatch),
	/*11257*/ uint16(xCondIs64), 11260, 11274,
	/*11260*/ uint16(xCondDataSize), 11264, 11269, 0,
	/*11264*/ uint16(xSetOp), uint16(RCL),
	/*11266*/ uint16(xArgRM16),
	/*11267*/ uint16(xArgCL),
	/*11268*/ uint16(xMatch),
	/*11269*/ uint16(xSetOp), uint16(RCL),
	/*11271*/ uint16(xArgRM32),
	/*11272*/ uint16(xArgCL),
	/*11273*/ uint16(xMatch),
	/*11274*/ uint16(xCondDataSize), 11264, 11269, 11278,
	/*11278*/ uint16(xSetOp), uint16(RCL),
	/*11280*/ uint16(xArgRM64),
	/*11281*/ uint16(xArgCL),
	/*11282*/ uint16(xMatch),
	/*11283*/ uint16(xCondIs64), 11286, 11300,
	/*11286*/ uint16(xCondDataSize), 11290, 11295, 0,
	/*11290*/ uint16(xSetOp), uint16(RCR),
	/*11292*/ uint16(xArgRM16),
	/*11293*/ uint16(xArgCL),
	/*11294*/ uint16(xMatch),
	/*11295*/ uint16(xSetOp), uint16(RCR),
	/*11297*/ uint16(xArgRM32),
	/*11298*/ uint16(xArgCL),
	/*11299*/ uint16(xMatch),
	/*11300*/ uint16(xCondDataSize), 11290, 11295, 11304,
	/*11304*/ uint16(xSetOp), uint16(RCR),
	/*11306*/ uint16(xArgRM64),
	/*11307*/ uint16(xArgCL),
	/*11308*/ uint16(xMatch),
	/*11309*/ uint16(xCondIs64), 11312, 11326,
	/*11312*/ uint16(xCondDataSize), 11316, 11321, 0,
	/*11316*/ uint16(xSetOp), uint16(SHL),
	/*11318*/ uint16(xArgRM16),
	/*11319*/ uint16(xArgCL),
	/*11320*/ uint16(xMatch),
	/*11321*/ uint16(xSetOp), uint16(SHL),
	/*11323*/ uint16(xArgRM32),
	/*11324*/ uint16(xArgCL),
	/*11325*/ uint16(xMatch),
	/*11326*/ uint16(xCondDataSize), 11316, 11321, 11330,
	/*11330*/ uint16(xSetOp), uint16(SHL),
	/*11332*/ uint16(xArgRM64),
	/*11333*/ uint16(xArgCL),
	/*11334*/ uint16(xMatch),
	/*11335*/ uint16(xCondIs64), 11338, 11352,
	/*11338*/ uint16(xCondDataSize), 11342, 11347, 0,
	/*11342*/ uint16(xSetOp), uint16(SHR),
	/*11344*/ uint16(xArgRM16),
	/*11345*/ uint16(xArgCL),
	/*11346*/ uint16(xMatch),
	/*11347*/ uint16(xSetOp), uint16(SHR),
	/*11349*/ uint16(xArgRM32),
	/*11350*/ uint16(xArgCL),
	/*11351*/ uint16(xMatch),
	/*11352*/ uint16(xCondDataSize), 11342, 11347, 11356,
	/*11356*/ uint16(xSetOp), uint16(SHR),
	/*11358*/ uint16(xArgRM64),
	/*11359*/ uint16(xArgCL),
	/*11360*/ uint16(xMatch),
	/*11361*/ uint16(xCondIs64), 11364, 11378,
	/*11364*/ uint16(xCondDataSize), 11368, 11373, 0,
	/*11368*/ uint16(xSetOp), uint16(SAR),
	/*11370*/ uint16(xArgRM16),
	/*11371*/ uint16(xArgCL),
	/*11372*/ uint16(xMatch),
	/*11373*/ uint16(xSetOp), uint16(SAR),
	/*11375*/ uint16(xArgRM32),
	/*11376*/ uint16(xArgCL),
	/*11377*/ uint16(xMatch),
	/*11378*/ uint16(xCondDataSize), 11368, 11373, 11382,
	/*11382*/ uint16(xSetOp), uint16(SAR),
	/*11384*/ uint16(xArgRM64),
	/*11385*/ uint16(xArgCL),
	/*11386*/ uint16(xMatch),
	/*11387*/ uint16(xCondIs64), 11390, 0,
	/*11390*/ uint16(xSetOp), uint16(AAM),
	/*11392*/ uint16(xReadIb),
	/*11393*/ uint16(xArgImm8u),
	/*11394*/ uint16(xMatch),
	/*11395*/ uint16(xCondIs64), 11398, 0,
	/*11398*/ uint16(xSetOp), uint16(AAD),
	/*11400*/ uint16(xReadIb),
	/*11401*/ uint16(xArgImm8u),
	/*11402*/ uint16(xMatch),
	/*11403*/ uint16(xCondIs64), 11406, 11409,
	/*11406*/ uint16(xSetOp), uint16(XLATB),
	/*11408*/ uint16(xMatch),
	/*11409*/ uint16(xCondDataSize), 11406, 11406, 11413,
	/*11413*/ uint16(xSetOp), uint16(XLATB),
	/*11415*/ uint16(xMatch),
	/*11416*/ uint16(xCondByte), 64,
	0xc0, 11587,
	0xc1, 11587,
	0xc2, 11587,
	0xc3, 11587,
	0xc4, 11587,
	0xc5, 11587,
	0xc6, 11587,
	0xc7, 11587,
	0xc8, 11592,
	0xc9, 11592,
	0xca, 11592,
	0xcb, 11592,
	0xcc, 11592,
	0xcd, 11592,
	0xce, 11592,
	0xcf, 11592,
	0xd0, 11597,
	0xd1, 11597,
	0xd2, 11597,
	0xd3, 11597,
	0xd4, 11597,
	0xd5, 11597,
	0xd6, 11597,
	0xd7, 11597,
	0xd8, 11601,
	0xd9, 11601,
	0xda, 11601,
	0xdb, 11601,
	0xdc, 11601,
	0xdd, 11601,
	0xde, 11601,
	0xdf, 11601,
	0xe0, 11605,
	0xe1, 11605,
	0xe2, 11605,
	0xe3, 11605,
	0xe4, 11605,
	0xe5, 11605,
	0xe6, 11605,
	0xe7, 11605,
	0xe8, 11610,
	0xe9, 11610,
	0xea, 11610,
	0xeb, 11610,
	0xec, 11610,
	0xed, 11610,
	0xee, 11610,
	0xef, 11610,
	0xf0, 11615,
	0xf1, 11615,
	0xf2, 11615,
	0xf3, 11615,
	0xf4, 11615,
	0xf5, 11615,
	0xf6, 11615,
	0xf7, 11615,
	0xf8, 11620,
	0xf9, 11620,
	0xfa, 11620,
	0xfb, 11620,
	0xfc, 11620,
	0xfd, 11620,
	0xfe, 11620,
	0xff, 11620,
	/*11546*/ uint16(xCondSlashR),
	11555, // 0
	11559, // 1
	11563, // 2
	11567, // 3
	11571, // 4
	11575, // 5
	11579, // 6
	11583, // 7
	/*11555*/ uint16(xSetOp), uint16(FADD),
	/*11557*/ uint16(xArgM32fp),
	/*11558*/ uint16(xMatch),
	/*11559*/ uint16(xSetOp), uint16(FMUL),
	/*11561*/ uint16(xArgM32fp),
	/*11562*/ uint16(xMatch),
	/*11563*/ uint16(xSetOp), uint16(FCOM),
	/*11565*/ uint16(xArgM32fp),
	/*11566*/ uint16(xMatch),
	/*11567*/ uint16(xSetOp), uint16(FCOMP),
	/*11569*/ uint16(xArgM32fp),
	/*11570*/ uint16(xMatch),
	/*11571*/ uint16(xSetOp), uint16(FSUB),
	/*11573*/ uint16(xArgM32fp),
	/*11574*/ uint16(xMatch),
	/*11575*/ uint16(xSetOp), uint16(FSUBR),
	/*11577*/ uint16(xArgM32fp),
	/*11578*/ uint16(xMatch),
	/*11579*/ uint16(xSetOp), uint16(FDIV),
	/*11581*/ uint16(xArgM32fp),
	/*11582*/ uint16(xMatch),
	/*11583*/ uint16(xSetOp), uint16(FDIVR),
	/*11585*/ uint16(xArgM32fp),
	/*11586*/ uint16(xMatch),
	/*11587*/ uint16(xSetOp), uint16(FADD),
	/*11589*/ uint16(xArgST),
	/*11590*/ uint16(xArgSTi),
	/*11591*/ uint16(xMatch),
	/*11592*/ uint16(xSetOp), uint16(FMUL),
	/*11594*/ uint16(xArgST),
	/*11595*/ uint16(xArgSTi),
	/*11596*/ uint16(xMatch),
	/*11597*/ uint16(xSetOp), uint16(FCOM),
	/*11599*/ uint16(xArgSTi),
	/*11600*/ uint16(xMatch),
	/*11601*/ uint16(xSetOp), uint16(FCOMP),
	/*11603*/ uint16(xArgSTi),
	/*11604*/ uint16(xMatch),
	/*11605*/ uint16(xSetOp), uint16(FSUB),
	/*11607*/ uint16(xArgST),
	/*11608*/ uint16(xArgSTi),
	/*11609*/ uint16(xMatch),
	/*11610*/ uint16(xSetOp), uint16(FSUBR),
	/*11612*/ uint16(xArgST),
	/*11613*/ uint16(xArgSTi),
	/*11614*/ uint16(xMatch),
	/*11615*/ uint16(xSetOp), uint16(FDIV),
	/*11617*/ uint16(xArgST),
	/*11618*/ uint16(xArgSTi),
	/*11619*/ uint16(xMatch),
	/*11620*/ uint16(xSetOp), uint16(FDIVR),
	/*11622*/ uint16(xArgST),
	/*11623*/ uint16(xArgSTi),
	/*11624*/ uint16(xMatch),
	/*11625*/ uint16(xCondByte), 44,
	0xc0, 11752,
	0xc1, 11752,
	0xc2, 11752,
	0xc3, 11752,
	0xc4, 11752,
	0xc5, 11752,
	0xc6, 11752,
	0xc7, 11752,
	0xc8, 11756,
	0xc9, 11756,
	0xca, 11756,
	0xcb, 11756,
	0xcc, 11756,
	0xcd, 11756,
	0xce, 11756,
	0xcf, 11756,
	0xD0, 11760,
	0xE0, 11763,
	0xE1, 11766,
	0xE4, 11769,
	0xE5, 11772,
	0xE8, 11775,
	0xE9, 11778,
	0xEA, 11781,
	0xEB, 11784,
	0xEC, 11787,
	0xED, 11790,
	0xEE, 11793,
	0xF0, 11796,
	0xF1, 11799,
	0xF2, 11802,
	0xF3, 11805,
	0xF4, 11808,
	0xF5, 11811,
	0xF6, 11814,
	0xF7, 11817,
	0xF8, 11820,
	0xF9, 11823,
	0xFA, 11826,
	0xFB, 11829,
	0xFC, 11832,
	0xFD, 11835,
	0xFE, 11838,
	0xFF, 11841,
	/*11715*/ uint16(xCondSlashR),
	11724, // 0
	0,     // 1
	11728, // 2
	11732, // 3
	11736, // 4
	11740, // 5
	11744, // 6
	11748, // 7
	/*11724*/ uint16(xSetOp), uint16(FLD),
	/*11726*/ uint16(xArgM32fp),
	/*11727*/ uint16(xMatch),
	/*11728*/ uint16(xSetOp), uint16(FST),
	/*11730*/ uint16(xArgM32fp),
	/*11731*/ uint16(xMatch),
	/*11732*/ uint16(xSetOp), uint16(FSTP),
	/*11734*/ uint16(xArgM32fp),
	/*11735*/ uint16(xMatch),
	/*11736*/ uint16(xSetOp), uint16(FLDENV),
	/*11738*/ uint16(xArgM1428byte),
	/*11739*/ uint16(xMatch),
	/*11740*/ uint16(xSetOp), uint16(FLDCW),
	/*11742*/ uint16(xArgM2byte),
	/*11743*/ uint16(xMatch),
	/*11744*/ uint16(xSetOp), uint16(FNSTENV),
	/*11746*/ uint16(xArgM1428byte),
	/*11747*/ uint16(xMatch),
	/*11748*/ uint16(xSetOp), uint16(FNSTCW),
	/*11750*/ uint16(xArgM2byte),
	/*11751*/ uint16(xMatch),
	/*11752*/ uint16(xSetOp), uint16(FLD),
	/*11754*/ uint16(xArgSTi),
	/*11755*/ uint16(xMatch),
	/*11756*/ uint16(xSetOp), uint16(FXCH),
	/*11758*/ uint16(xArgSTi),
	/*11759*/ uint16(xMatch),
	/*11760*/ uint16(xSetOp), uint16(FNOP),
	/*11762*/ uint16(xMatch),
	/*11763*/ uint16(xSetOp), uint16(FCHS),
	/*11765*/ uint16(xMatch),
	/*11766*/ uint16(xSetOp), uint16(FABS),
	/*11768*/ uint16(xMatch),
	/*11769*/ uint16(xSetOp), uint16(FTST),
	/*11771*/ uint16(xMatch),
	/*11772*/ uint16(xSetOp), uint16(FXAM),
	/*11774*/ uint16(xMatch),
	/*11775*/ uint16(xSetOp), uint16(FLD1),
	/*11777*/ uint16(xMatch),
	/*11778*/ uint16(xSetOp), uint16(FLDL2T),
	/*11780*/ uint16(xMatch),
	/*11781*/ uint16(xSetOp), uint16(FLDL2E),
	/*11783*/ uint16(xMatch),
	/*11784*/ uint16(xSetOp), uint16(FLDPI),
	/*11786*/ uint16(xMatch),
	/*11787*/ uint16(xSetOp), uint16(FLDLG2),
	/*11789*/ uint16(xMatch),
	/*11790*/ uint16(xSetOp), uint16(FLDLN2),
	/*11792*/ uint16(xMatch),
	/*11793*/ uint16(xSetOp), uint16(FLDZ),
	/*11795*/ uint16(xMatch),
	/*11796*/ uint16(xSetOp), uint16(F2XM1),
	/*11798*/ uint16(xMatch),
	/*11799*/ uint16(xSetOp), uint16(FYL2X),
	/*11801*/ uint16(xMatch),
	/*11802*/ uint16(xSetOp), uint16(FPTAN),
	/*11804*/ uint16(xMatch),
	/*11805*/ uint16(xSetOp), uint16(FPATAN),
	/*11807*/ uint16(xMatch),
	/*11808*/ uint16(xSetOp), uint16(FXTRACT),
	/*11810*/ uint16(xMatch),
	/*11811*/ uint16(xSetOp), uint16(FPREM1),
	/*11813*/ uint16(xMatch),
	/*11814*/ uint16(xSetOp), uint16(FDECSTP),
	/*11816*/ uint16(xMatch),
	/*11817*/ uint16(xSetOp), uint16(FINCSTP),
	/*11819*/ uint16(xMatch),
	/*11820*/ uint16(xSetOp), uint16(FPREM),
	/*11822*/ uint16(xMatch),
	/*11823*/ uint16(xSetOp), uint16(FYL2XP1),
	/*11825*/ uint16(xMatch),
	/*11826*/ uint16(xSetOp), uint16(FSQRT),
	/*11828*/ uint16(xMatch),
	/*11829*/ uint16(xSetOp), uint16(FSINCOS),
	/*11831*/ uint16(xMatch),
	/*11832*/ uint16(xSetOp), uint16(FRNDINT),
	/*11834*/ uint16(xMatch),
	/*11835*/ uint16(xSetOp), uint16(FSCALE),
	/*11837*/ uint16(xMatch),
	/*11838*/ uint16(xSetOp), uint16(FSIN),
	/*11840*/ uint16(xMatch),
	/*11841*/ uint16(xSetOp), uint16(FCOS),
	/*11843*/ uint16(xMatch),
	/*11844*/ uint16(xCondByte), 33,
	0xc0, 11953,
	0xc1, 11953,
	0xc2, 11953,
	0xc3, 11953,
	0xc4, 11953,
	0xc5, 11953,
	0xc6, 11953,
	0xc7, 11953,
	0xc8, 11958,
	0xc9, 11958,
	0xca, 11958,
	0xcb, 11958,
	0xcc, 11958,
	0xcd, 11958,
	0xce, 11958,
	0xcf, 11958,
	0xd0, 11963,
	0xd1, 11963,
	0xd2, 11963,
	0xd3, 11963,
	0xd4, 11963,
	0xd5, 11963,
	0xd6, 11963,
	0xd7, 11963,
	0xd8, 11968,
	0xd9, 11968,
	0xda, 11968,
	0xdb, 11968,
	0xdc, 11968,
	0xdd, 11968,
	0xde, 11968,
	0xdf, 11968,
	0xE9, 11973,
	/*11912*/ uint16(xCondSlashR),
	11921, // 0
	11925, // 1
	11929, // 2
	11933, // 3
	11937, // 4
	11941, // 5
	11945, // 6
	11949, // 7
	/*11921*/ uint16(xSetOp), uint16(FIADD),
	/*11923*/ uint16(xArgM32int),
	/*11924*/ uint16(xMatch),
	/*11925*/ uint16(xSetOp), uint16(FIMUL),
	/*11927*/ uint16(xArgM32int),
	/*11928*/ uint16(xMatch),
	/*11929*/ uint16(xSetOp), uint16(FICOM),
	/*11931*/ uint16(xArgM32int),
	/*11932*/ uint16(xMatch),
	/*11933*/ uint16(xSetOp), uint16(FICOMP),
	/*11935*/ uint16(xArgM32int),
	/*11936*/ uint16(xMatch),
	/*11937*/ uint16(xSetOp), uint16(FISUB),
	/*11939*/ uint16(xArgM32int),
	/*11940*/ uint16(xMatch),
	/*11941*/ uint16(xSetOp), uint16(FISUBR),
	/*11943*/ uint16(xArgM32int),
	/*11944*/ uint16(xMatch),
	/*11945*/ uint16(xSetOp), uint16(FIDIV),
	/*11947*/ uint16(xArgM32int),
	/*11948*/ uint16(xMatch),
	/*11949*/ uint16(xSetOp), uint16(FIDIVR),
	/*11951*/ uint16(xArgM32int),
	/*11952*/ uint16(xMatch),
	/*11953*/ uint16(xSetOp), uint16(FCMOVB),
	/*11955*/ uint16(xArgST),
	/*11956*/ uint16(xArgSTi),
	/*11957*/ uint16(xMatch),
	/*11958*/ uint16(xSetOp), uint16(FCMOVE),
	/*11960*/ uint16(xArgST),
	/*11961*/ uint16(xArgSTi),
	/*11962*/ uint16(xMatch),
	/*11963*/ uint16(xSetOp), uint16(FCMOVBE),
	/*11965*/ uint16(xArgST),
	/*11966*/ uint16(xArgSTi),
	/*11967*/ uint16(xMatch),
	/*11968*/ uint16(xSetOp), uint16(FCMOVU),
	/*11970*/ uint16(xArgST),
	/*11971*/ uint16(xArgSTi),
	/*11972*/ uint16(xMatch),
	/*11973*/ uint16(xSetOp), uint16(FUCOMPP),
	/*11975*/ uint16(xMatch),
	/*11976*/ uint16(xCondByte), 50,
	0xc0, 12111,
	0xc1, 12111,
	0xc2, 12111,
	0xc3, 12111,
	0xc4, 12111,
	0xc5, 12111,
	0xc6, 12111,
	0xc7, 12111,
	0xc8, 12116,
	0xc9, 12116,
	0xca, 12116,
	0xcb, 12116,
	0xcc, 12116,
	0xcd, 12116,
	0xce, 12116,
	0xcf, 12116,
	0xd0, 12121,
	0xd1, 12121,
	0xd2, 12121,
	0xd3, 12121,
	0xd4, 12121,
	0xd5, 12121,
	0xd6, 12121,
	0xd7, 12121,
	0xd8, 12126,
	0xd9, 12126,
	0xda, 12126,
	0xdb, 12126,
	0xdc, 12126,
	0xdd, 12126,
	0xde, 12126,
	0xdf, 12126,
	0xE2, 12131,
	0xE3, 12134,
	0xe8, 12137,
	0xe9, 12137,
	0xea, 12137,
	0xeb, 12137,
	0xec, 12137,
	0xed, 12137,
	0xee, 12137,
	0xef, 12137,
	0xf0, 12142,
	0xf1, 12142,
	0xf2, 12142,
	0xf3, 12142,
	0xf4, 12142,
	0xf5, 12142,
	0xf6, 12142,
	0xf7, 12142,
	/*12078*/ uint16(xCondSlashR),
	12087, // 0
	12091, // 1
	12095, // 2
	12099, // 3
	0,     // 4
	12103, // 5
	0,     // 6
	12107, // 7
	/*12087*/ uint16(xSetOp), uint16(FILD),
	/*12089*/ uint16(xArgM32int),
	/*12090*/ uint16(xMatch),
	/*12091*/ uint16(xSetOp), uint16(FISTTP),
	/*12093*/ uint16(xArgM32int),
	/*12094*/ uint16(xMatch),
	/*12095*/ uint16(xSetOp), uint16(FIST),
	/*12097*/ uint16(xArgM32int),
	/*12098*/ uint16(xMatch),
	/*12099*/ uint16(xSetOp), uint16(FISTP),
	/*12101*/ uint16(xArgM32int),
	/*12102*/ uint16(xMatch),
	/*12103*/ uint16(xSetOp), uint16(FLD),
	/*12105*/ uint16(xArgM80fp),
	/*12106*/ uint16(xMatch),
	/*12107*/ uint16(xSetOp), uint16(FSTP),
	/*12109*/ uint16(xArgM80fp),
	/*12110*/ uint16(xMatch),
	/*12111*/ uint16(xSetOp), uint16(FCMOVNB),
	/*12113*/ uint16(xArgST),
	/*12114*/ uint16(xArgSTi),
	/*12115*/ uint16(xMatch),
	/*12116*/ uint16(xSetOp), uint16(FCMOVNE),
	/*12118*/ uint16(xArgST),
	/*12119*/ uint16(xArgSTi),
	/*12120*/ uint16(xMatch),
	/*12121*/ uint16(xSetOp), uint16(FCMOVNBE),
	/*12123*/ uint16(xArgST),
	/*12124*/ uint16(xArgSTi),
	/*12125*/ uint16(xMatch),
	/*12126*/ uint16(xSetOp), uint16(FCMOVNU),
	/*12128*/ uint16(xArgST),
	/*12129*/ uint16(xArgSTi),
	/*12130*/ uint16(xMatch),
	/*12131*/ uint16(xSetOp), uint16(FNCLEX),
	/*12133*/ uint16(xMatch),
	/*12134*/ uint16(xSetOp), uint16(FNINIT),
	/*12136*/ uint16(xMatch),
	/*12137*/ uint16(xSetOp), uint16(FUCOMI),
	/*12139*/ uint16(xArgST),
	/*12140*/ uint16(xArgSTi),
	/*12141*/ uint16(xMatch),
	/*12142*/ uint16(xSetOp), uint16(FCOMI),
	/*12144*/ uint16(xArgST),
	/*12145*/ uint16(xArgSTi),
	/*12146*/ uint16(xMatch),
	/*12147*/ uint16(xCondByte), 48,
	0xc0, 12286,
	0xc1, 12286,
	0xc2, 12286,
	0xc3, 12286,
	0xc4, 12286,
	0xc5, 12286,
	0xc6, 12286,
	0xc7, 12286,
	0xc8, 12291,
	0xc9, 12291,
	0xca, 12291,
	0xcb, 12291,
	0xcc, 12291,
	0xcd, 12291,
	0xce, 12291,
	0xcf, 12291,
	0xe0, 12296,
	0xe1, 12296,
	0xe2, 12296,
	0xe3, 12296,
	0xe4, 12296,
	0xe5, 12296,
	0xe6, 12296,
	0xe7, 12296,
	0xe8, 12301,
	0xe9, 12301,
	0xea, 12301,
	0xeb, 12301,
	0xec, 12301,
	0xed, 12301,
	0xee, 12301,
	0xef, 12301,
	0xf0, 12306,
	0xf1, 12306,
	0xf2, 12306,
	0xf3, 12306,
	0xf4, 12306,
	0xf5, 12306,
	0xf6, 12306,
	0xf7, 12306,
	0xf8, 12311,
	0xf9, 12311,
	0xfa, 12311,
	0xfb, 12311,
	0xfc, 12311,
	0xfd, 12311,
	0xfe, 12311,
	0xff, 12311,
	/*12245*/ uint16(xCondSlashR),
	12254, // 0
	12258, // 1
	12262, // 2
	12266, // 3
	12270, // 4
	12274, // 5
	12278, // 6
	12282, // 7
	/*12254*/ uint16(xSetOp), uint16(FADD),
	/*12256*/ uint16(xArgM64fp),
	/*12257*/ uint16(xMatch),
	/*12258*/ uint16(xSetOp), uint16(FMUL),
	/*12260*/ uint16(xArgM64fp),
	/*12261*/ uint16(xMatch),
	/*12262*/ uint16(xSetOp), uint16(FCOM),
	/*12264*/ uint16(xArgM64fp),
	/*12265*/ uint16(xMatch),
	/*12266*/ uint16(xSetOp), uint16(FCOMP),
	/*12268*/ uint16(xArgM64fp),
	/*12269*/ uint16(xMatch),
	/*12270*/ uint16(xSetOp), uint16(FSUB),
	/*12272*/ uint16(xArgM64fp),
	/*12273*/ uint16(xMatch),
	/*12274*/ uint16(xSetOp), uint16(FSUBR),
	/*12276*/ uint16(xArgM64fp),
	/*12277*/ uint16(xMatch),
	/*12278*/ uint16(xSetOp), uint16(FDIV),
	/*12280*/ uint16(xArgM64fp),
	/*12281*/ uint16(xMatch),
	/*12282*/ uint16(xSetOp), uint16(FDIVR),
	/*12284*/ uint16(xArgM64fp),
	/*12285*/ uint16(xMatch),
	/*12286*/ uint16(xSetOp), uint16(FADD),
	/*12288*/ uint16(xArgSTi),
	/*12289*/ uint16(xArgST),
	/*12290*/ uint16(xMatch),
	/*12291*/ uint16(xSetOp), uint16(FMUL),
	/*12293*/ uint16(xArgSTi),
	/*12294*/ uint16(xArgST),
	/*12295*/ uint16(xMatch),
	/*12296*/ uint16(xSetOp), uint16(FSUBR),
	/*12298*/ uint16(xArgSTi),
	/*12299*/ uint16(xArgST),
	/*12300*/ uint16(xMatch),
	/*12301*/ uint16(xSetOp), uint16(FSUB),
	/*12303*/ uint16(xArgSTi),
	/*12304*/ uint16(xArgST),
	/*12305*/ uint16(xMatch),
	/*12306*/ uint16(xSetOp), uint16(FDIVR),
	/*12308*/ uint16(xArgSTi),
	/*12309*/ uint16(xArgST),
	/*12310*/ uint16(xMatch),
	/*12311*/ uint16(xSetOp), uint16(FDIV),
	/*12313*/ uint16(xArgSTi),
	/*12314*/ uint16(xArgST),
	/*12315*/ uint16(xMatch),
	/*12316*/ uint16(xCondByte), 40,
	0xc0, 12435,
	0xc1, 12435,
	0xc2, 12435,
	0xc3, 12435,
	0xc4, 12435,
	0xc5, 12435,
	0xc6, 12435,
	0xc7, 12435,
	0xd0, 12439,
	0xd1, 12439,
	0xd2, 12439,
	0xd3, 12439,
	0xd4, 12439,
	0xd5, 12439,
	0xd6, 12439,
	0xd7, 12439,
	0xd8, 12443,
	0xd9, 12443,
	0xda, 12443,
	0xdb, 12443,
	0xdc, 12443,
	0xdd, 12443,
	0xde, 12443,
	0xdf, 12443,
	0xe0, 12447,
	0xe1, 12447,
	0xe2, 12447,
	0xe3, 12447,
	0xe4, 12447,
	0xe5, 12447,
	0xe6, 12447,
	0xe7, 12447,
	0xe8, 12451,
	0xe9, 12451,
	0xea, 12451,
	0xeb, 12451,
	0xec, 12451,
	0xed, 12451,
	0xee, 12451,
	0xef, 12451,
	/*12398*/ uint16(xCondSlashR),
	12407, // 0
	12411, // 1
	12415, // 2
	12419, // 3
	12423, // 4
	0,     // 5
	12427, // 6
	12431, // 7
	/*12407*/ uint16(xSetOp), uint16(FLD),
	/*12409*/ uint16(xArgM64fp),
	/*12410*/ uint16(xMatch),
	/*12411*/ uint16(xSetOp), uint16(FISTTP),
	/*12413*/ uint16(xArgM64int),
	/*12414*/ uint16(xMatch),
	/*12415*/ uint16(xSetOp), uint16(FST),
	/*12417*/ uint16(xArgM64fp),
	/*12418*/ uint16(xMatch),
	/*12419*/ uint16(xSetOp), uint16(FSTP),
	/*12421*/ uint16(xArgM64fp),
	/*12422*/ uint16(xMatch),
	/*12423*/ uint16(xSetOp), uint16(FRSTOR),
	/*12425*/ uint16(xArgM94108byte),
	/*12426*/ uint16(xMatch),
	/*12427*/ uint16(xSetOp), uint16(FNSAVE),
	/*12429*/ uint16(xArgM94108byte),
	/*12430*/ uint16(xMatch),
	/*12431*/ uint16(xSetOp), uint16(FNSTSW),
	/*12433*/ uint16(xArgM2byte),
	/*12434*/ uint16(xMatch),
	/*12435*/ uint16(xSetOp), uint16(FFREE),
	/*12437*/ uint16(xArgSTi),
	/*12438*/ uint16(xMatch),
	/*12439*/ uint16(xSetOp), uint16(FST),
	/*12441*/ uint16(xArgSTi),
	/*12442*/ uint16(xMatch),
	/*12443*/ uint16(xSetOp), uint16(FSTP),
	/*12445*/ uint16(xArgSTi),
	/*12446*/ uint16(xMatch),
	/*12447*/ uint16(xSetOp), uint16(FUCOM),
	/*12449*/ uint16(xArgSTi),
	/*12450*/ uint16(xMatch),
	/*12451*/ uint16(xSetOp), uint16(FUCOMP),
	/*12453*/ uint16(xArgSTi),
	/*12454*/ uint16(xMatch),
	/*12455*/ uint16(xCondByte), 49,
	0xc0, 12596,
	0xc1, 12596,
	0xc2, 12596,
	0xc3, 12596,
	0xc4, 12596,
	0xc5, 12596,
	0xc6, 12596,
	0xc7, 12596,
	0xc8, 12601,
	0xc9, 12601,
	0xca, 12601,
	0xcb, 12601,
	0xcc, 12601,
	0xcd, 12601,
	0xce, 12601,
	0xcf, 12601,
	0xD9, 12606,
	0xe0, 12609,
	0xe1, 12609,
	0xe2, 12609,
	0xe3, 12609,
	0xe4, 12609,
	0xe5, 12609,
	0xe6, 12609,
	0xe7, 12609,
	0xe8, 12614,
	0xe9, 12614,
	0xea, 12614,
	0xeb, 12614,
	0xec, 12614,
	0xed, 12614,
	0xee, 12614,
	0xef, 12614,
	0xf0, 12619,
	0xf1, 12619,
	0xf2, 12619,
	0xf3, 12619,
	0xf4, 12619,
	0xf5, 12619,
	0xf6, 12619,
	0xf7, 12619,
	0xf8, 12624,
	0xf9, 12624,
	0xfa, 12624,
	0xfb, 12624,
	0xfc, 12624,
	0xfd, 12624,
	0xfe, 12624,
	0xff, 12624,
	/*12555*/ uint16(xCondSlashR),
	12564, // 0
	12568, // 1
	12572, // 2
	12576, // 3
	12580, // 4
	12584, // 5
	12588, // 6
	12592, // 7
	/*12564*/ uint16(xSetOp), uint16(FIADD),
	/*12566*/ uint16(xArgM16int),
	/*12567*/ uint16(xMatch),
	/*12568*/ uint16(xSetOp), uint16(FIMUL),
	/*12570*/ uint16(xArgM16int),
	/*12571*/ uint16(xMatch),
	/*12572*/ uint16(xSetOp), uint16(FICOM),
	/*12574*/ uint16(xArgM16int),
	/*12575*/ uint16(xMatch),
	/*12576*/ uint16(xSetOp), uint16(FICOMP),
	/*12578*/ uint16(xArgM16int),
	/*12579*/ uint16(xMatch),
	/*12580*/ uint16(xSetOp), uint16(FISUB),
	/*12582*/ uint16(xArgM16int),
	/*12583*/ uint16(xMatch),
	/*12584*/ uint16(xSetOp), uint16(FISUBR),
	/*12586*/ uint16(xArgM16int),
	/*12587*/ uint16(xMatch),
	/*12588*/ uint16(xSetOp), uint16(FIDIV),
	/*12590*/ uint16(xArgM16int),
	/*12591*/ uint16(xMatch),
	/*12592*/ uint16(xSetOp), uint16(FIDIVR),
	/*12594*/ uint16(xArgM16int),
	/*12595*/ uint16(xMatch),
	/*12596*/ uint16(xSetOp), uint16(FADDP),
	/*12598*/ uint16(xArgSTi),
	/*12599*/ uint16(xArgST),
	/*12600*/ uint16(xMatch),
	/*12601*/ uint16(xSetOp), uint16(FMULP),
	/*12603*/ uint16(xArgSTi),
	/*12604*/ uint16(xArgST),
	/*12605*/ uint16(xMatch),
	/*12606*/ uint16(xSetOp), uint16(FCOMPP),
	/*12608*/ uint16(xMatch),
	/*12609*/ uint16(xSetOp), uint16(FSUBRP),
	/*12611*/ uint16(xArgSTi),
	/*12612*/ uint16(xArgST),
	/*12613*/ uint16(xMatch),
	/*12614*/ uint16(xSetOp), uint16(FSUBP),
	/*12616*/ uint16(xArgSTi),
	/*12617*/ uint16(xArgST),
	/*12618*/ uint16(xMatch),
	/*12619*/ uint16(xSetOp), uint16(FDIVRP),
	/*12621*/ uint16(xArgSTi),
	/*12622*/ uint16(xArgST),
	/*12623*/ uint16(xMatch),
	/*12624*/ uint16(xSetOp), uint16(FDIVP),
	/*12626*/ uint16(xArgSTi),
	/*12627*/ uint16(xArgST),
	/*12628*/ uint16(xMatch),
	/*12629*/ uint16(xCondByte), 25,
	0xc0, 12722,
	0xc1, 12722,
	0xc2, 12722,
	0xc3, 12722,
	0xc4, 12722,
	0xc5, 12722,
	0xc6, 12722,
	0xc7, 12722,
	0xE0, 12726,
	0xe8, 12730,
	0xe9, 12730,
	0xea, 12730,
	0xeb, 12730,
	0xec, 12730,
	0xed, 12730,
	0xee, 12730,
	0xef, 12730,
	0xf0, 12735,
	0xf1, 12735,
	0xf2, 12735,
	0xf3, 12735,
	0xf4, 12735,
	0xf5, 12735,
	0xf6, 12735,
	0xf7, 12735,
	/*12681*/ uint16(xCondSlashR),
	12690, // 0
	12694, // 1
	12698, // 2
	12702, // 3
	12706, // 4
	12710, // 5
	12714, // 6
	12718, // 7
	/*12690*/ uint16(xSetOp), uint16(FILD),
	/*12692*/ uint16(xArgM16int),
	/*12693*/ uint16(xMatch),
	/*12694*/ uint16(xSetOp), uint16(FISTTP),
	/*12696*/ uint16(xArgM16int),
	/*12697*/ uint16(xMatch),
	/*12698*/ uint16(xSetOp), uint16(FIST),
	/*12700*/ uint16(xArgM16int),
	/*12701*/ uint16(xMatch),
	/*12702*/ uint16(xSetOp), uint16(FISTP),
	/*12704*/ uint16(xArgM16int),
	/*12705*/ uint16(xMatch),
	/*12706*/ uint16(xSetOp), uint16(FBLD),
	/*12708*/ uint16(xArgM80dec),
	/*12709*/ uint16(xMatch),
	/*12710*/ uint16(xSetOp), uint16(FILD),
	/*12712*/ uint16(xArgM64int),
	/*12713*/ uint16(xMatch),
	/*12714*/ uint16(xSetOp), uint16(FBSTP),
	/*12716*/ uint16(xArgM80bcd),
	/*12717*/ uint16(xMatch),
	/*12718*/ uint16(xSetOp), uint16(FISTP),
	/*12720*/ uint16(xArgM64int),
	/*12721*/ uint16(xMatch),
	/*12722*/ uint16(xSetOp), uint16(FFREEP),
	/*12724*/ uint16(xArgSTi),
	/*12725*/ uint16(xMatch),
	/*12726*/ uint16(xSetOp), uint16(FNSTSW),
	/*12728*/ uint16(xArgAX),
	/*12729*/ uint16(xMatch),
	/*12730*/ uint16(xSetOp), uint16(FUCOMIP),
	/*12732*/ uint16(xArgST),
	/*12733*/ uint16(xArgSTi),
	/*12734*/ uint16(xMatch),
	/*12735*/ uint16(xSetOp), uint16(FCOMIP),
	/*12737*/ uint16(xArgST),
	/*12738*/ uint16(xArgSTi),
	/*12739*/ uint16(xMatch),
	/*12740*/ uint16(xSetOp), uint16(LOOPNE),
	/*12742*/ uint16(xReadCb),
	/*12743*/ uint16(xArgRel8),
	/*12744*/ uint16(xMatch),
	/*12745*/ uint16(xSetOp), uint16(LOOPE),
	/*12747*/ uint16(xReadCb),
	/*12748*/ uint16(xArgRel8),
	/*12749*/ uint16(xMatch),
	/*12750*/ uint16(xSetOp), uint16(LOOP),
	/*12752*/ uint16(xReadCb),
	/*12753*/ uint16(xArgRel8),
	/*12754*/ uint16(xMatch),
	/*12755*/ uint16(xCondIs64), 12758, 12772,
	/*12758*/ uint16(xCondAddrSize), 12762, 12767, 0,
	/*12762*/ uint16(xSetOp), uint16(JCXZ),
	/*12764*/ uint16(xReadCb),
	/*12765*/ uint16(xArgRel8),
	/*12766*/ uint16(xMatch),
	/*12767*/ uint16(xSetOp), uint16(JECXZ),
	/*12769*/ uint16(xReadCb),
	/*12770*/ uint16(xArgRel8),
	/*12771*/ uint16(xMatch),
	/*12772*/ uint16(xCondAddrSize), 0, 12767, 12776,
	/*12776*/ uint16(xSetOp), uint16(JRCXZ),
	/*12778*/ uint16(xReadCb),
	/*12779*/ uint16(xArgRel8),
	/*12780*/ uint16(xMatch),
	/*12781*/ uint16(xSetOp), uint16(IN),
	/*12783*/ uint16(xReadIb),
	/*12784*/ uint16(xArgAL),
	/*12785*/ uint16(xArgImm8u),
	/*12786*/ uint16(xMatch),
	/*12787*/ uint16(xCondDataSize), 12791, 12797, 12803,
	/*12791*/ uint16(xSetOp), uint16(IN),
	/*12793*/ uint16(xReadIb),
	/*12794*/ uint16(xArgAX),
	/*12795*/ uint16(xArgImm8u),
	/*12796*/ uint16(xMatch),
	/*12797*/ uint16(xSetOp), uint16(IN),
	/*12799*/ uint16(xReadIb),
	/*12800*/ uint16(xArgEAX),
	/*12801*/ uint16(xArgImm8u),
	/*12802*/ uint16(xMatch),
	/*12803*/ uint16(xSetOp), uint16(IN),
	/*12805*/ uint16(xReadIb),
	/*12806*/ uint16(xArgEAX),
	/*12807*/ uint16(xArgImm8u),
	/*12808*/ uint16(xMatch),
	/*12809*/ uint16(xSetOp), uint16(OUT),
	/*12811*/ uint16(xReadIb),
	/*12812*/ uint16(xArgImm8u),
	/*12813*/ uint16(xArgAL),
	/*12814*/ uint16(xMatch),
	/*12815*/ uint16(xCondPrefix), 3,
	0xC5, 12859,
	0xC4, 12845,
	0x0, 12823,
	/*12823*/ uint16(xCondDataSize), 12827, 12833, 12839,
	/*12827*/ uint16(xSetOp), uint16(OUT),
	/*12829*/ uint16(xReadIb),
	/*12830*/ uint16(xArgImm8u),
	/*12831*/ uint16(xArgAX),
	/*12832*/ uint16(xMatch),
	/*12833*/ uint16(xSetOp), uint16(OUT),
	/*12835*/ uint16(xReadIb),
	/*12836*/ uint16(xArgImm8u),
	/*12837*/ uint16(xArgEAX),
	/*12838*/ uint16(xMatch),
	/*12839*/ uint16(xSetOp), uint16(OUT),
	/*12841*/ uint16(xReadIb),
	/*12842*/ uint16(xArgImm8u),
	/*12843*/ uint16(xArgEAX),
	/*12844*/ uint16(xMatch),
	/*12845*/ uint16(xCondPrefix), 1,
	0x66, 12849,
	/*12849*/ uint16(xCondPrefix), 1,
	0x0F, 12853,
	/*12853*/ uint16(xSetOp), uint16(VMOVNTDQ),
	/*12855*/ uint16(xReadSlashR),
	/*12856*/ uint16(xArgM256),
	/*12857*/ uint16(xArgYmm1),
	/*12858*/ uint16(xMatch),
	/*12859*/ uint16(xCondPrefix), 1,
	0x66, 12863,
	/*12863*/ uint16(xCondPrefix), 1,
	0x0F, 12867,
	/*12867*/ uint16(xSetOp), uint16(VMOVNTDQ),
	/*12869*/ uint16(xReadSlashR),
	/*12870*/ uint16(xArgM256),
	/*12871*/ uint16(xArgYmm1),
	/*12872*/ uint16(xMatch),
	/*12873*/ uint16(xCondIs64), 12876, 12890,
	/*12876*/ uint16(xCondDataSize), 12880, 12885, 0,
	/*12880*/ uint16(xSetOp), uint16(CALL),
	/*12882*/ uint16(xReadCw),
	/*12883*/ uint16(xArgRel16),
	/*12884*/ uint16(xMatch),
	/*12885*/ uint16(xSetOp), uint16(CALL),
	/*12887*/ uint16(xReadCd),
	/*12888*/ uint16(xArgRel32),
	/*12889*/ uint16(xMatch),
	/*12890*/ uint16(xCondDataSize), 12894, 12885, 12899,
	/*12894*/ uint16(xSetOp), uint16(CALL),
	/*12896*/ uint16(xReadCd),
	/*12897*/ uint16(xArgRel32),
	/*12898*/ uint16(xMatch),
	/*12899*/ uint16(xSetOp), uint16(CALL),
	/*12901*/ uint16(xReadCd),
	/*12902*/ uint16(xArgRel32),
	/*12903*/ uint16(xMatch),
	/*12904*/ uint16(xCondIs64), 12907, 12921,
	/*12907*/ uint16(xCondDataSize), 12911, 12916, 0,
	/*12911*/ uint16(xSetOp), uint16(JMP),
	/*12913*/ uint16(xReadCw),
	/*12914*/ uint16(xArgRel16),
	/*12915*/ uint16(xMatch),
	/*12916*/ uint16(xSetOp), uint16(JMP),
	/*12918*/ uint16(xReadCd),
	/*12919*/ uint16(xArgRel32),
	/*12920*/ uint16(xMatch),
	/*12921*/ uint16(xCondDataSize), 12925, 12916, 12930,
	/*12925*/ uint16(xSetOp), uint16(JMP),
	/*12927*/ uint16(xReadCd),
	/*12928*/ uint16(xArgRel32),
	/*12929*/ uint16(xMatch),
	/*12930*/ uint16(xSetOp), uint16(JMP),
	/*12932*/ uint16(xReadCd),
	/*12933*/ uint16(xArgRel32),
	/*12934*/ uint16(xMatch),
	/*12935*/ uint16(xCondIs64), 12938, 0,
	/*12938*/ uint16(xCondDataSize), 12942, 12947, 0,
	/*12942*/ uint16(xSetOp), uint16(LJMP),
	/*12944*/ uint16(xReadCd),
	/*12945*/ uint16(xArgPtr16colon16),
	/*12946*/ uint16(xMatch),
	/*12947*/ uint16(xSetOp), uint16(LJMP),
	/*12949*/ uint16(xReadCp),
	/*12950*/ uint16(xArgPtr16colon32),
	/*12951*/ uint16(xMatch),
	/*12952*/ uint16(xSetOp), uint16(JMP),
	/*12954*/ uint16(xReadCb),
	/*12955*/ uint16(xArgRel8),
	/*12956*/ uint16(xMatch),
	/*12957*/ uint16(xSetOp), uint16(IN),
	/*12959*/ uint16(xArgAL),
	/*12960*/ uint16(xArgDX),
	/*12961*/ uint16(xMatch),
	/*12962*/ uint16(xCondDataSize), 12966, 12971, 12976,
	/*12966*/ uint16(xSetOp), uint16(IN),
	/*12968*/ uint16(xArgAX),
	/*12969*/ uint16(xArgDX),
	/*12970*/ uint16(xMatch),
	/*12971*/ uint16(xSetOp), uint16(IN),
	/*12973*/ uint16(xArgEAX),
	/*12974*/ uint16(xArgDX),
	/*12975*/ uint16(xMatch),
	/*12976*/ uint16(xSetOp), uint16(IN),
	/*12978*/ uint16(xArgEAX),
	/*12979*/ uint16(xArgDX),
	/*12980*/ uint16(xMatch),
	/*12981*/ uint16(xSetOp), uint16(OUT),
	/*12983*/ uint16(xArgDX),
	/*12984*/ uint16(xArgAL),
	/*12985*/ uint16(xMatch),
	/*12986*/ uint16(xCondDataSize), 12990, 12995, 13000,
	/*12990*/ uint16(xSetOp), uint16(OUT),
	/*12992*/ uint16(xArgDX),
	/*12993*/ uint16(xArgAX),
	/*12994*/ uint16(xMatch),
	/*12995*/ uint16(xSetOp), uint16(OUT),
	/*12997*/ uint16(xArgDX),
	/*12998*/ uint16(xArgEAX),
	/*12999*/ uint16(xMatch),
	/*13000*/ uint16(xSetOp), uint16(OUT),
	/*13002*/ uint16(xArgDX),
	/*13003*/ uint16(xArgEAX),
	/*13004*/ uint16(xMatch),
	/*13005*/ uint16(xSetOp), uint16(ICEBP),
	/*13007*/ uint16(xMatch),
	/*13008*/ uint16(xSetOp), uint16(HLT),
	/*13010*/ uint16(xMatch),
	/*13011*/ uint16(xSetOp), uint16(CMC),
	/*13013*/ uint16(xMatch),
	/*13014*/ uint16(xCondSlashR),
	13023, // 0
	0,     // 1
	13029, // 2
	13033, // 3
	13037, // 4
	13041, // 5
	13045, // 6
	13049, // 7
	/*13023*/ uint16(xSetOp), uint16(TEST),
	/*13025*/ uint16(xReadIb),
	/*13026*/ uint16(xArgRM8),
	/*13027*/ uint16(xArgImm8u),
	/*13028*/ uint16(xMatch),
	/*13029*/ uint16(xSetOp), uint16(NOT),
	/*13031*/ uint16(xArgRM8),
	/*13032*/ uint16(xMatch),
	/*13033*/ uint16(xSetOp), uint16(NEG),
	/*13035*/ uint16(xArgRM8),
	/*13036*/ uint16(xMatch),
	/*13037*/ uint16(xSetOp), uint16(MUL),
	/*13039*/ uint16(xArgRM8),
	/*13040*/ uint16(xMatch),
	/*13041*/ uint16(xSetOp), uint16(IMUL),
	/*13043*/ uint16(xArgRM8),
	/*13044*/ uint16(xMatch),
	/*13045*/ uint16(xSetOp), uint16(DIV),
	/*13047*/ uint16(xArgRM8),
	/*13048*/ uint16(xMatch),
	/*13049*/ uint16(xSetOp), uint16(IDIV),
	/*13051*/ uint16(xArgRM8),
	/*13052*/ uint16(xMatch),
	/*13053*/ uint16(xCondSlashR),
	13062, // 0
	0,     // 1
	13091, // 2
	13114, // 3
	13137, // 4
	13160, // 5
	13183, // 6
	13206, // 7
	/*13062*/ uint16(xCondIs64), 13065, 13081,
	/*13065*/ uint16(xCondDataSize), 13069, 13075, 0,
	/*13069*/ uint16(xSetOp), uint16(TEST),
	/*13071*/ uint16(xReadIw),
	/*13072*/ uint16(xArgRM16),
	/*13073*/ uint16(xArgImm16),
	/*13074*/ uint16(xMatch),
	/*13075*/ uint16(xSetOp), uint16(TEST),
	/*13077*/ uint16(xReadId),
	/*13078*/ uint16(xArgRM32),
	/*13079*/ uint16(xArgImm32),
	/*13080*/ uint16(xMatch),
	/*13081*/ uint16(xCondDataSize), 13069, 13075, 13085,
	/*13085*/ uint16(xSetOp), uint16(TEST),
	/*13087*/ uint16(xReadId),
	/*13088*/ uint16(xArgRM64),
	/*13089*/ uint16(xArgImm32),
	/*13090*/ uint16(xMatch),
	/*13091*/ uint16(xCondIs64), 13094, 13106,
	/*13094*/ uint16(xCondDataSize), 13098, 13102, 0,
	/*13098*/ uint16(xSetOp), uint16(NOT),
	/*13100*/ uint16(xArgRM16),
	/*13101*/ uint16(xMatch),
	/*13102*/ uint16(xSetOp), uint16(NOT),
	/*13104*/ uint16(xArgRM32),
	/*13105*/ uint16(xMatch),
	/*13106*/ uint16(xCondDataSize), 13098, 13102, 13110,
	/*13110*/ uint16(xSetOp), uint16(NOT),
	/*13112*/ uint16(xArgRM64),
	/*13113*/ uint16(xMatch),
	/*13114*/ uint16(xCondIs64), 13117, 13129,
	/*13117*/ uint16(xCondDataSize), 13121, 13125, 0,
	/*13121*/ uint16(xSetOp), uint16(NEG),
	/*13123*/ uint16(xArgRM16),
	/*13124*/ uint16(xMatch),
	/*13125*/ uint16(xSetOp), uint16(NEG),
	/*13127*/ uint16(xArgRM32),
	/*13128*/ uint16(xMatch),
	/*13129*/ uint16(xCondDataSize), 13121, 13125, 13133,
	/*13133*/ uint16(xSetOp), uint16(NEG),
	/*13135*/ uint16(xArgRM64),
	/*13136*/ uint16(xMatch),
	/*13137*/ uint16(xCondIs64), 13140, 13152,
	/*13140*/ uint16(xCondDataSize), 13144, 13148, 0,
	/*13144*/ uint16(xSetOp), uint16(MUL),
	/*13146*/ uint16(xArgRM16),
	/*13147*/ uint16(xMatch),
	/*13148*/ uint16(xSetOp), uint16(MUL),
	/*13150*/ uint16(xArgRM32),
	/*13151*/ uint16(xMatch),
	/*13152*/ uint16(xCondDataSize), 13144, 13148, 13156,
	/*13156*/ uint16(xSetOp), uint16(MUL),
	/*13158*/ uint16(xArgRM64),
	/*13159*/ uint16(xMatch),
	/*13160*/ uint16(xCondIs64), 13163, 13175,
	/*13163*/ uint16(xCondDataSize), 13167, 13171, 0,
	/*13167*/ uint16(xSetOp), uint16(IMUL),
	/*13169*/ uint16(xArgRM16),
	/*13170*/ uint16(xMatch),
	/*13171*/ uint16(xSetOp), uint16(IMUL),
	/*13173*/ uint16(xArgRM32),
	/*13174*/ uint16(xMatch),
	/*13175*/ uint16(xCondDataSize), 13167, 13171, 13179,
	/*13179*/ uint16(xSetOp), uint16(IMUL),
	/*13181*/ uint16(xArgRM64),
	/*13182*/ uint16(xMatch),
	/*13183*/ uint16(xCondIs64), 13186, 13198,
	/*13186*/ uint16(xCondDataSize), 13190, 13194, 0,
	/*13190*/ uint16(xSetOp), uint16(DIV),
	/*13192*/ uint16(xArgRM16),
	/*13193*/ uint16(xMatch),
	/*13194*/ uint16(xSetOp), uint16(DIV),
	/*13196*/ uint16(xArgRM32),
	/*13197*/ uint16(xMatch),
	/*13198*/ uint16(xCondDataSize), 13190, 13194, 13202,
	/*13202*/ uint16(xSetOp), uint16(DIV),
	/*13204*/ uint16(xArgRM64),
	/*13205*/ uint16(xMatch),
	/*13206*/ uint16(xCondIs64), 13209, 13221,
	/*13209*/ uint16(xCondDataSize), 13213, 13217, 0,
	/*13213*/ uint16(xSetOp), uint16(IDIV),
	/*13215*/ uint16(xArgRM16),
	/*13216*/ uint16(xMatch),
	/*13217*/ uint16(xSetOp), uint16(IDIV),
	/*13219*/ uint16(xArgRM32),
	/*13220*/ uint16(xMatch),
	/*13221*/ uint16(xCondDataSize), 13213, 13217, 13225,
	/*13225*/ uint16(xSetOp), uint16(IDIV),
	/*13227*/ uint16(xArgRM64),
	/*13228*/ uint16(xMatch),
	/*13229*/ uint16(xSetOp), uint16(CLC),
	/*13231*/ uint16(xMatch),
	/*13232*/ uint16(xSetOp), uint16(STC),
	/*13234*/ uint16(xMatch),
	/*13235*/ uint16(xSetOp), uint16(CLI),
	/*13237*/ uint16(xMatch),
	/*13238*/ uint16(xSetOp), uint16(STI),
	/*13240*/ uint16(xMatch),
	/*13241*/ uint16(xSetOp), uint16(CLD),
	/*13243*/ uint16(xMatch),
	/*13244*/ uint16(xSetOp), uint16(STD),
	/*13246*/ uint16(xMatch),
	/*13247*/ uint16(xCondSlashR),
	13256, // 0
	13260, // 1
	0,     // 2
	0,     // 3
	0,     // 4
	0,     // 5
	0,     // 6
	0,     // 7
	/*13256*/ uint16(xSetOp), uint16(INC),
	/*13258*/ uint16(xArgRM8),
	/*13259*/ uint16(xMatch),
	/*13260*/ uint16(xSetOp), uint16(DEC),
	/*13262*/ uint16(xArgRM8),
	/*13263*/ uint16(xMatch),
	/*13264*/ uint16(xCondSlashR),
	13273, // 0
	13296, // 1
	13319, // 2
	13338, // 3
	13361, // 4
	13380, // 5
	13403, // 6
	0,     // 7
	/*13273*/ uint16(xCondIs64), 13276, 13288,
	/*13276*/ uint16(xCondDataSize), 13280, 13284, 0,
	/*13280*/ uint16(xSetOp), uint16(INC),
	/*13282*/ uint16(xArgRM16),
	/*13283*/ uint16(xMatch),
	/*13284*/ uint16(xSetOp), uint16(INC),
	/*13286*/ uint16(xArgRM32),
	/*13287*/ uint16(xMatch),
	/*13288*/ uint16(xCondDataSize), 13280, 13284, 13292,
	/*13292*/ uint16(xSetOp), uint16(INC),
	/*13294*/ uint16(xArgRM64),
	/*13295*/ uint16(xMatch),
	/*13296*/ uint16(xCondIs64), 13299, 13311,
	/*13299*/ uint16(xCondDataSize), 13303, 13307, 0,
	/*13303*/ uint16(xSetOp), uint16(DEC),
	/*13305*/ uint16(xArgRM16),
	/*13306*/ uint16(xMatch),
	/*13307*/ uint16(xSetOp), uint16(DEC),
	/*13309*/ uint16(xArgRM32),
	/*13310*/ uint16(xMatch),
	/*13311*/ uint16(xCondDataSize), 13303, 13307, 13315,
	/*13315*/ uint16(xSetOp), uint16(DEC),
	/*13317*/ uint16(xArgRM64),
	/*13318*/ uint16(xMatch),
	/*13319*/ uint16(xCondIs64), 13322, 13334,
	/*13322*/ uint16(xCondDataSize), 13326, 13330, 0,
	/*13326*/ uint16(xSetOp), uint16(CALL),
	/*13328*/ uint16(xArgRM16),
	/*13329*/ uint16(xMatch),
	/*13330*/ uint16(xSetOp), uint16(CALL),
	/*13332*/ uint16(xArgRM32),
	/*13333*/ uint16(xMatch),
	/*13334*/ uint16(xSetOp), uint16(CALL),
	/*13336*/ uint16(xArgRM64),
	/*13337*/ uint16(xMatch),
	/*13338*/ uint16(xCondIs64), 13341, 13353,
	/*13341*/ uint16(xCondDataSize), 13345, 13349, 0,
	/*13345*/ uint16(xSetOp), uint16(LCALL),
	/*13347*/ uint16(xArgM16colon16),
	/*13348*/ uint16(xMatch),
	/*13349*/ uint16(xSetOp), uint16(LCALL),
	/*13351*/ uint16(xArgM16colon32),
	/*13352*/ uint16(xMatch),
	/*13353*/ uint16(xCondDataSize), 13345, 13349, 13357,
	/*13357*/ uint16(xSetOp), uint16(LCALL),
	/*13359*/ uint16(xArgM16colon64),
	/*13360*/ uint16(xMatch),
	/*13361*/ uint16(xCondIs64), 13364, 13376,
	/*13364*/ uint16(xCondDataSize), 13368, 13372, 0,
	/*13368*/ uint16(xSetOp), uint16(JMP),
	/*13370*/ uint16(xArgRM16),
	/*13371*/ uint16(xMatch),
	/*13372*/ uint16(xSetOp), uint16(JMP),
	/*13374*/ uint16(xArgRM32),
	/*13375*/ uint16(xMatch),
	/*13376*/ uint16(xSetOp), uint16(JMP),
	/*13378*/ uint16(xArgRM64),
	/*13379*/ uint16(xMatch),
	/*13380*/ uint16(xCondIs64), 13383, 13395,
	/*13383*/ uint16(xCondDataSize), 13387, 13391, 0,
	/*13387*/ uint16(xSetOp), uint16(LJMP),
	/*13389*/ uint16(xArgM16colon16),
	/*13390*/ uint16(xMatch),
	/*13391*/ uint16(xSetOp), uint16(LJMP),
	/*13393*/ uint16(xArgM16colon32),
	/*13394*/ uint16(xMatch),
	/*13395*/ uint16(xCondDataSize), 13387, 13391, 13399,
	/*13399*/ uint16(xSetOp), uint16(LJMP),
	/*13401*/ uint16(xArgM16colon64),
	/*13402*/ uint16(xMatch),
	/*13403*/ uint16(xCondIs64), 13406, 13418,
	/*13406*/ uint16(xCondDataSize), 13410, 13414, 0,
	/*13410*/ uint16(xSetOp), uint16(PUSH),
	/*13412*/ uint16(xArgRM16),
	/*13413*/ uint16(xMatch),
	/*13414*/ uint16(xSetOp), uint16(PUSH),
	/*13416*/ uint16(xArgRM32),
	/*13417*/ uint16(xMatch),
	/*13418*/ uint16(xCondDataSize), 13410, 13422, 13426,
	/*13422*/ uint16(xSetOp), uint16(PUSH),
	/*13424*/ uint16(xArgRM64),
	/*13425*/ uint16(xMatch),
	/*13426*/ uint16(xSetOp), uint16(PUSH),
	/*13428*/ uint16(xArgRM64),
	/*13429*/ uint16(xMatch),
}

const (
	_ Op = iota

	AAA
	AAD
	AAM
	AAS
	ADC
	ADD
	ADDPD
	ADDPS
	ADDSD
	ADDSS
	ADDSUBPD
	ADDSUBPS
	AESDEC
	AESDECLAST
	AESENC
	AESENCLAST
	AESIMC
	AESKEYGENASSIST
	AND
	ANDNPD
	ANDNPS
	ANDPD
	ANDPS
	ARPL
	BLENDPD
	BLENDPS
	BLENDVPD
	BLENDVPS
	BOUND
	BSF
	BSR
	BSWAP
	BT
	BTC
	BTR
	BTS
	CALL
	CBW
	CDQ
	CDQE
	CLC
	CLD
	CLFLUSH
	CLI
	CLTS
	CMC
	CMOVA
	CMOVAE
	CMOVB
	CMOVBE
	CMOVE
	CMOVG
	CMOVGE
	CMOVL
	CMOVLE
	CMOVNE
	CMOVNO
	CMOVNP
	CMOVNS
	CMOVO
	CMOVP
	CMOVS
	CMP
	CMPPD
	CMPPS
	CMPSB
	CMPSD
	CMPSD_XMM
	CMPSQ
	CMPSS
	CMPSW
	CMPXCHG
	CMPXCHG16B
	CMPXCHG8B
	COMISD
	COMISS
	CPUID
	CQO
	CRC32
	CVTDQ2PD
	CVTDQ2PS
	CVTPD2DQ
	CVTPD2PI
	CVTPD2PS
	CVTPI2PD
	CVTPI2PS
	CVTPS2DQ
	CVTPS2PD
	CVTPS2PI
	CVTSD2SI
	CVTSD2SS
	CVTSI2SD
	CVTSI2SS
	CVTSS2SD
	CVTSS2SI
	CVTTPD2DQ
	CVTTPD2PI
	CVTTPS2DQ
	CVTTPS2PI
	CVTTSD2SI
	CVTTSS2SI
	CWD
	CWDE
	DAA
	DAS
	DEC
	DIV
	DIVPD
	DIVPS
	DIVSD
	DIVSS
	DPPD
	DPPS
	EMMS
	ENTER
	EXTRACTPS
	F2XM1
	FABS
	FADD
	FADDP
	FBLD
	FBSTP
	FCHS
	FCMOVB
	FCMOVBE
	FCMOVE
	FCMOVNB
	FCMOVNBE
	FCMOVNE
	FCMOVNU
	FCMOVU
	FCOM
	FCOMI
	FCOMIP
	FCOMP
	FCOMPP
	FCOS
	FDECSTP
	FDIV
	FDIVP
	FDIVR
	FDIVRP
	FFREE
	FFREEP
	FIADD
	FICOM
	FICOMP
	FIDIV
	FIDIVR
	FILD
	FIMUL
	FINCSTP
	FIST
	FISTP
	FISTTP
	FISUB
	FISUBR
	FLD
	FLD1
	FLDCW
	FLDENV
	FLDL2E
	FLDL2T
	FLDLG2
	FLDLN2
	FLDPI
	FLDZ
	FMUL
	FMULP
	FNCLEX
	FNINIT
	FNOP
	FNSAVE
	FNSTCW
	FNSTENV
	FNSTSW
	FPATAN
	FPREM
	FPREM1
	FPTAN
	FRNDINT
	FRSTOR
	FSCALE
	FSIN
	FSINCOS
	FSQRT
	FST
	FSTP
	FSUB
	FSUBP
	FSUBR
	FSUBRP
	FTST
	FUCOM
	FUCOMI
	FUCOMIP
	FUCOMP
	FUCOMPP
	FWAIT
	FXAM
	FXCH
	FXRSTOR
	FXRSTOR64
	FXSAVE
	FXSAVE64
	FXTRACT
	FYL2X
	FYL2XP1
	HADDPD
	HADDPS
	HLT
	HSUBPD
	HSUBPS
	ICEBP
	IDIV
	IMUL
	IN
	INC
	INSB
	INSD
	INSERTPS
	INSW
	INT
	INTO
	INVD
	INVLPG
	INVPCID
	IRET
	IRETD
	IRETQ
	JA
	JAE
	JB
	JBE
	JCXZ
	JE
	JECXZ
	JG
	JGE
	JL
	JLE
	JMP
	JNE
	JNO
	JNP
	JNS
	JO
	JP
	JRCXZ
	JS
	LAHF
	LAR
	LCALL
	LDDQU
	LDMXCSR
	LDS
	LEA
	LEAVE
	LES
	LFENCE
	LFS
	LGDT
	LGS
	LIDT
	LJMP
	LLDT
	LMSW
	LODSB
	LODSD
	LODSQ
	LODSW
	LOOP
	LOOPE
	LOOPNE
	LRET
	LSL
	LSS
	LTR
	LZCNT
	MASKMOVDQU
	MASKMOVQ
	MAXPD
	MAXPS
	MAXSD
	MAXSS
	MFENCE
	MINPD
	MINPS
	MINSD
	MINSS
	MONITOR
	MOV
	MOVAPD
	MOVAPS
	MOVBE
	MOVD
	MOVDDUP
	MOVDQ2Q
	MOVDQA
	MOVDQU
	MOVHLPS
	MOVHPD
	MOVHPS
	MOVLHPS
	MOVLPD
	MOVLPS
	MOVMSKPD
	MOVMSKPS
	MOVNTDQ
	MOVNTDQA
	MOVNTI
	MOVNTPD
	MOVNTPS
	MOVNTQ
	MOVNTSD
	MOVNTSS
	MOVQ
	MOVQ2DQ
	MOVSB
	MOVSD
	MOVSD_XMM
	MOVSHDUP
	MOVSLDUP
	MOVSQ
	MOVSS
	MOVSW
	MOVSX
	MOVSXD
	MOVUPD
	MOVUPS
	MOVZX
	MPSADBW
	MUL
	MULPD
	MULPS
	MULSD
	MULSS
	MWAIT
	NEG
	NOP
	NOT
	OR
	ORPD
	ORPS
	OUT
	OUTSB
	OUTSD
	OUTSW
	PABSB
	PABSD
	PABSW
	PACKSSDW
	PACKSSWB
	PACKUSDW
	PACKUSWB
	PADDB
	PADDD
	PADDQ
	PADDSB
	PADDSW
	PADDUSB
	PADDUSW
	PADDW
	PALIGNR
	PAND
	PANDN
	PAUSE
	PAVGB
	PAVGW
	PBLENDVB
	PBLENDW
	PCLMULQDQ
	PCMPEQB
	PCMPEQD
	PCMPEQQ
	PCMPEQW
	PCMPESTRI
	PCMPESTRM
	PCMPGTB
	PCMPGTD
	PCMPGTQ
	PCMPGTW
	PCMPISTRI
	PCMPISTRM
	PEXTRB
	PEXTRD
	PEXTRQ
	PEXTRW
	PHADDD
	PHADDSW
	PHADDW
	PHMINPOSUW
	PHSUBD
	PHSUBSW
	PHSUBW
	PINSRB
	PINSRD
	PINSRQ
	PINSRW
	PMADDUBSW
	PMADDWD
	PMAXSB
	PMAXSD
	PMAXSW
	PMAXUB
	PMAXUD
	PMAXUW
	PMINSB
	PMINSD
	PMINSW
	PMINUB
	PMINUD
	PMINUW
	PMOVMSKB
	PMOVSXBD
	PMOVSXBQ
	PMOVSXBW
	PMOVSXDQ
	PMOVSXWD
	PMOVSXWQ
	PMOVZXBD
	PMOVZXBQ
	PMOVZXBW
	PMOVZXDQ
	PMOVZXWD
	PMOVZXWQ
	PMULDQ
	PMULHRSW
	PMULHUW
	PMULHW
	PMULLD
	PMULLW
	PMULUDQ
	POP
	POPA
	POPAD
	POPCNT
	POPF
	POPFD
	POPFQ
	POR
	PREFETCHNTA
	PREFETCHT0
	PREFETCHT1
	PREFETCHT2
	PREFETCHW
	PSADBW
	PSHUFB
	PSHUFD
	PSHUFHW
	PSHUFLW
	PSHUFW
	PSIGNB
	PSIGND
	PSIGNW
	PSLLD
	PSLLDQ
	PSLLQ
	PSLLW
	PSRAD
	PSRAW
	PSRLD
	PSRLDQ
	PSRLQ
	PSRLW
	PSUBB
	PSUBD
	PSUBQ
	PSUBSB
	PSUBSW
	PSUBUSB
	PSUBUSW
	PSUBW
	PTEST
	PUNPCKHBW
	PUNPCKHDQ
	PUNPCKHQDQ
	PUNPCKHWD
	PUNPCKLBW
	PUNPCKLDQ
	PUNPCKLQDQ
	PUNPCKLWD
	PUSH
	PUSHA
	PUSHAD
	PUSHF
	PUSHFD
	PUSHFQ
	PXOR
	RCL
	RCPPS
	RCPSS
	RCR
	RDFSBASE
	RDGSBASE
	RDMSR
	RDPMC
	RDRAND
	RDTSC
	RDTSCP
	RET
	ROL
	ROR
	ROUNDPD
	ROUNDPS
	ROUNDSD
	ROUNDSS
	RSM
	RSQRTPS
	RSQRTSS
	SAHF
	SAR
	SBB
	SCASB
	SCASD
	SCASQ
	SCASW
	SETA
	SETAE
	SETB
	SETBE
	SETE
	SETG
	SETGE
	SETL
	SETLE
	SETNE
	SETNO
	SETNP
	SETNS
	SETO
	SETP
	SETS
	SFENCE
	SGDT
	SHL
	SHLD
	SHR
	SHRD
	SHUFPD
	SHUFPS
	SIDT
	SLDT
	SMSW
	SQRTPD
	SQRTPS
	SQRTSD
	SQRTSS
	STC
	STD
	STI
	STMXCSR
	STOSB
	STOSD
	STOSQ
	STOSW
	STR
	SUB
	SUBPD
	SUBPS
	SUBSD
	SUBSS
	SWAPGS
	SYSCALL
	SYSENTER
	SYSEXIT
	SYSRET
	TEST
	TZCNT
	UCOMISD
	UCOMISS
	UD0
	UD1
	UD2
	UNPCKHPD
	UNPCKHPS
	UNPCKLPD
	UNPCKLPS
	VERR
	VERW
	VMOVDQA
	VMOVDQU
	VMOVNTDQ
	VMOVNTDQA
	VZEROUPPER
	WBINVD
	WRFSBASE
	WRGSBASE
	WRMSR
	XABORT
	XADD
	XBEGIN
	XCHG
	XEND
	XGETBV
	XLATB
	XOR
	XORPD
	XORPS
	XRSTOR
	XRSTOR64
	XRSTORS
	XRSTORS64
	XSAVE
	XSAVE64
	XSAVEC
	XSAVEC64
	XSAVEOPT
	XSAVEOPT64
	XSAVES
	XSAVES64
	XSETBV
	XTEST
)

const maxOp = XTEST

var opNames = [...]string{
	AAA:             "AAA",
	AAD:             "AAD",
	AAM:             "AAM",
	AAS:             "AAS",
	ADC:             "ADC",
	ADD:             "ADD",
	ADDPD:           "ADDPD",
	ADDPS:           "ADDPS",
	ADDSD:           "ADDSD",
	ADDSS:           "ADDSS",
	ADDSUBPD:        "ADDSUBPD",
	ADDSUBPS:        "ADDSUBPS",
	AESDEC:          "AESDEC",
	AESDECLAST:      "AESDECLAST",
	AESENC:          "AESENC",
	AESENCLAST:      "AESENCLAST",
	AESIMC:          "AESIMC",
	AESKEYGENASSIST: "AESKEYGENASSIST",
	AND:             "AND",
	ANDNPD:          "ANDNPD",
	ANDNPS:          "ANDNPS",
	ANDPD:           "ANDPD",
	ANDPS:           "ANDPS",
	ARPL:            "ARPL",
	BLENDPD:         "BLENDPD",
	BLENDPS:         "BLENDPS",
	BLENDVPD:        "BLENDVPD",
	BLENDVPS:        "BLENDVPS",
	BOUND:           "BOUND",
	BSF:             "BSF",
	BSR:             "BSR",
	BSWAP:           "BSWAP",
	BT:              "BT",
	BTC:             "BTC",
	BTR:             "BTR",
	BTS:             "BTS",
	CALL:            "CALL",
	CBW:             "CBW",
	CDQ:             "CDQ",
	CDQE:            "CDQE",
	CLC:             "CLC",
	CLD:             "CLD",
	CLFLUSH:         "CLFLUSH",
	CLI:             "CLI",
	CLTS:            "CLTS",
	CMC:             "CMC",
	CMOVA:           "CMOVA",
	CMOVAE:          "CMOVAE",
	CMOVB:           "CMOVB",
	CMOVBE:          "CMOVBE",
	CMOVE:           "CMOVE",
	CMOVG:           "CMOVG",
	CMOVGE:          "CMOVGE",
	CMOVL:           "CMOVL",
	CMOVLE:          "CMOVLE",
	CMOVNE:          "CMOVNE",
	CMOVNO:          "CMOVNO",
	CMOVNP:          "CMOVNP",
	CMOVNS:          "CMOVNS",
	CMOVO:           "CMOVO",
	CMOVP:           "CMOVP",
	CMOVS:           "CMOVS",
	CMP:             "CMP",
	CMPPD:           "CMPPD",
	CMPPS:           "CMPPS",
	CMPSB:           "CMPSB",
	CMPSD:           "CMPSD",
	CMPSD_XMM:       "CMPSD_XMM",
	CMPSQ:           "CMPSQ",
	CMPSS:           "CMPSS",
	CMPSW:           "CMPSW",
	CMPXCHG:         "CMPXCHG",
	CMPXCHG16B:      "CMPXCHG16B",
	CMPXCHG8B:       "CMPXCHG8B",
	COMISD:          "COMISD",
	COMISS:          "COMISS",
	CPUID:           "CPUID",
	CQO:             "CQO",
	CRC32:           "CRC32",
	CVTDQ2PD:        "CVTDQ2PD",
	CVTDQ2PS:        "CVTDQ2PS",
	CVTPD2DQ:        "CVTPD2DQ",
	CVTPD2PI:        "CVTPD2PI",
	CVTPD2PS:        "CVTPD2PS",
	CVTPI2PD:        "CVTPI2PD",
	CVTPI2PS:        "CVTPI2PS",
	CVTPS2DQ:        "CVTPS2DQ",
	CVTPS2PD:        "CVTPS2PD",
	CVTPS2PI:        "CVTPS2PI",
	CVTSD2SI:        "CVTSD2SI",
	CVTSD2SS:        "CVTSD2SS",
	CVTSI2SD:        "CVTSI2SD",
	CVTSI2SS:        "CVTSI2SS",
	CVTSS2SD:        "CVTSS2SD",
	CVTSS2SI:        "CVTSS2SI",
	CVTTPD2DQ:       "CVTTPD2DQ",
	CVTTPD2PI:       "CVTTPD2PI",
	CVTTPS2DQ:       "CVTTPS2DQ",
	CVTTPS2PI:       "CVTTPS2PI",
	CVTTSD2SI:       "CVTTSD2SI",
	CVTTSS2SI:       "CVTTSS2SI",
	CWD:             "CWD",
	CWDE:            "CWDE",
	DAA:             "DAA",
	DAS:             "DAS",
	DEC:             "DEC",
	DIV:             "DIV",
	DIVPD:           "DIVPD",
	DIVPS:           "DIVPS",
	DIVSD:           "DIVSD",
	DIVSS:           "DIVSS",
	DPPD:            "DPPD",
	DPPS:            "DPPS",
	EMMS:            "EMMS",
	ENTER:           "ENTER",
	EXTRACTPS:       "EXTRACTPS",
	F2XM1:           "F2XM1",
	FABS:            "FABS",
	FADD:            "FADD",
	FADDP:           "FADDP",
	FBLD:            "FBLD",
	FBSTP:           "FBSTP",
	FCHS:            "FCHS",
	FCMOVB:          "FCMOVB",
	FCMOVBE:         "FCMOVBE",
	FCMOVE:          "FCMOVE",
	FCMOVNB:         "FCMOVNB",
	FCMOVNBE:        "FCMOVNBE",
	FCMOVNE:         "FCMOVNE",
	FCMOVNU:         "FCMOVNU",
	FCMOVU:          "FCMOVU",
	FCOM:            "FCOM",
	FCOMI:           "FCOMI",
	FCOMIP:          "FCOMIP",
	FCOMP:           "FCOMP",
	FCOMPP:          "FCOMPP",
	FCOS:            "FCOS",
	FDECSTP:         "FDECSTP",
	FDIV:            "FDIV",
	FDIVP:           "FDIVP",
	FDIVR:           "FDIVR",
	FDIVRP:          "FDIVRP",
	FFREE:           "FFREE",
	FFREEP:          "FFREEP",
	FIADD:           "FIADD",
	FICOM:           "FICOM",
	FICOMP:          "FICOMP",
	FIDIV:           "FIDIV",
	FIDIVR:          "FIDIVR",
	FILD:            "FILD",
	FIMUL:           "FIMUL",
	FINCSTP:         "FINCSTP",
	FIST:            "FIST",
	FISTP:           "FISTP",
	FISTTP:          "FISTTP",
	FISUB:           "FISUB",
	FISUBR:          "FISUBR",
	FLD:             "FLD",
	FLD1:            "FLD1",
	FLDCW:           "FLDCW",
	FLDENV:          "FLDENV",
	FLDL2E:          "FLDL2E",
	FLDL2T:          "FLDL2T",
	FLDLG2:          "FLDLG2",
	FLDLN2:          "FLDLN2",
	FLDPI:           "FLDPI",
	FLDZ:            "FLDZ",
	FMUL:            "FMUL",
	FMULP:           "FMULP",
	FNCLEX:          "FNCLEX",
	FNINIT:          "FNINIT",
	FNOP:            "FNOP",
	FNSAVE:          "FNSAVE",
	FNSTCW:          "FNSTCW",
	FNSTENV:         "FNSTENV",
	FNSTSW:          "FNSTSW",
	FPATAN:          "FPATAN",
	FPREM:           "FPREM",
	FPREM1:          "FPREM1",
	FPTAN:           "FPTAN",
	FRNDINT:         "FRNDINT",
	FRSTOR:          "FRSTOR",
	FSCALE:          "FSCALE",
	FSIN:            "FSIN",
	FSINCOS:         "FSINCOS",
	FSQRT:           "FSQRT",
	FST:             "FST",
	FSTP:            "FSTP",
	FSUB:            "FSUB",
	FSUBP:           "FSUBP",
	FSUBR:           "FSUBR",
	FSUBRP:          "FSUBRP",
	FTST:            "FTST",
	FUCOM:           "FUCOM",
	FUCOMI:          "FUCOMI",
	FUCOMIP:         "FUCOMIP",
	FUCOMP:          "FUCOMP",
	FUCOMPP:         "FUCOMPP",
	FWAIT:           "FWAIT",
	FXAM:            "FXAM",
	FXCH:            "FXCH",
	FXRSTOR:         "FXRSTOR",
	FXRSTOR64:       "FXRSTOR64",
	FXSAVE:          "FXSAVE",
	FXSAVE64:        "FXSAVE64",
	FXTRACT:         "FXTRACT",
	FYL2X:           "FYL2X",
	FYL2XP1:         "FYL2XP1",
	HADDPD:          "HADDPD",
	HADDPS:          "HADDPS",
	HLT:             "HLT",
	HSUBPD:          "HSUBPD",
	HSUBPS:          "HSUBPS",
	ICEBP:           "ICEBP",
	IDIV:            "IDIV",
	IMUL:            "IMUL",
	IN:              "IN",
	INC:             "INC",
	INSB:            "INSB",
	INSD:            "INSD",
	INSERTPS:        "INSERTPS",
	INSW:            "INSW",
	INT:             "INT",
	INTO:            "INTO",
	INVD:            "INVD",
	INVLPG:          "INVLPG",
	INVPCID:         "INVPCID",
	IRET:            "IRET",
	IRETD:           "IRETD",
	IRETQ:           "IRETQ",
	JA:              "JA",
	JAE:             "JAE",
	JB:              "JB",
	JBE:             "JBE",
	JCXZ:            "JCXZ",
	JE:              "JE",
	JECXZ:           "JECXZ",
	JG:              "JG",
	JGE:             "JGE",
	JL:              "JL",
	JLE:             "JLE",
	JMP:             "JMP",
	JNE:             "JNE",
	JNO:             "JNO",
	JNP:             "JNP",
	JNS:             "JNS",
	JO:              "JO",
	JP:              "JP",
	JRCXZ:           "JRCXZ",
	JS:              "JS",
	LAHF:            "LAHF",
	LAR:             "LAR",
	LCALL:           "LCALL",
	LDDQU:           "LDDQU",
	LDMXCSR:         "LDMXCSR",
	LDS:             "LDS",
	LEA:             "LEA",
	LEAVE:           "LEAVE",
	LES:             "LES",
	LFENCE:          "LFENCE",
	LFS:             "LFS",
	LGDT:            "LGDT",
	LGS:             "LGS",
	LIDT:            "LIDT",
	LJMP:            "LJMP",
	LLDT:            "LLDT",
	LMSW:            "LMSW",
	LODSB:           "LODSB",
	LODSD:           "LODSD",
	LODSQ:           "LODSQ",
	LODSW:           "LODSW",
	LOOP:            "LOOP",
	LOOPE:           "LOOPE",
	LOOPNE:          "LOOPNE",
	LRET:            "LRET",
	LSL:             "LSL",
	LSS:             "LSS",
	LTR:             "LTR",
	LZCNT:           "LZCNT",
	MASKMOVDQU:      "MASKMOVDQU",
	MASKMOVQ:        "MASKMOVQ",
	MAXPD:           "MAXPD",
	MAXPS:           "MAXPS",
	MAXSD:           "MAXSD",
	MAXSS:           "MAXSS",
	MFENCE:          "MFENCE",
	MINPD:           "MINPD",
	MINPS:           "MINPS",
	MINSD:           "MINSD",
	MINSS:           "MINSS",
	MONITOR:         "MONITOR",
	MOV:             "MOV",
	MOVAPD:          "MOVAPD",
	MOVAPS:          "MOVAPS",
	MOVBE:           "MOVBE",
	MOVD:            "MOVD",
	MOVDDUP:         "MOVDDUP",
	MOVDQ2Q:         "MOVDQ2Q",
	MOVDQA:          "MOVDQA",
	MOVDQU:          "MOVDQU",
	MOVHLPS:         "MOVHLPS",
	MOVHPD:          "MOVHPD",
	MOVHPS:          "MOVHPS",
	MOVLHPS:         "MOVLHPS",
	MOVLPD:          "MOVLPD",
	MOVLPS:          "MOVLPS",
	MOVMSKPD:        "MOVMSKPD",
	MOVMSKPS:        "MOVMSKPS",
	MOVNTDQ:         "MOVNTDQ",
	MOVNTDQA:        "MOVNTDQA",
	MOVNTI:          "MOVNTI",
	MOVNTPD:         "MOVNTPD",
	MOVNTPS:         "MOVNTPS",
	MOVNTQ:          "MOVNTQ",
	MOVNTSD:         "MOVNTSD",
	MOVNTSS:         "MOVNTSS",
	MOVQ:            "MOVQ",
	MOVQ2DQ:         "MOVQ2DQ",
	MOVSB:           "MOVSB",
	MOVSD:           "MOVSD",
	MOVSD_XMM:       "MOVSD_XMM",
	MOVSHDUP:        "MOVSHDUP",
	MOVSLDUP:        "MOVSLDUP",
	MOVSQ:           "MOVSQ",
	MOVSS:           "MOVSS",
	MOVSW:           "MOVSW",
	MOVSX:           "MOVSX",
	MOVSXD:          "MOVSXD",
	MOVUPD:          "MOVUPD",
	MOVUPS:          "MOVUPS",
	MOVZX:           "MOVZX",
	MPSADBW:         "MPSADBW",
	MUL:             "MUL",
	MULPD:           "MULPD",
	MULPS:           "MULPS",
	MULSD:           "MULSD",
	MULSS:           "MULSS",
	MWAIT:           "MWAIT",
	NEG:             "NEG",
	NOP:             "NOP",
	NOT:             "NOT",
	OR:              "OR",
	ORPD:            "ORPD",
	ORPS:            "ORPS",
	OUT:             "OUT",
	OUTSB:           "OUTSB",
	OUTSD:           "OUTSD",
	OUTSW:           "OUTSW",
	PABSB:           "PABSB",
	PABSD:           "PABSD",
	PABSW:           "PABSW",
	PACKSSDW:        "PACKSSDW",
	PACKSSWB:        "PACKSSWB",
	PACKUSDW:        "PACKUSDW",
	PACKUSWB:        "PACKUSWB",
	PADDB:           "PADDB",
	PADDD:           "PADDD",
	PADDQ:           "PADDQ",
	PADDSB:          "PADDSB",
	PADDSW:          "PADDSW",
	PADDUSB:         "PADDUSB",
	PADDUSW:         "PADDUSW",
	PADDW:           "PADDW",
	PALIGNR:         "PALIGNR",
	PAND:            "PAND",
	PANDN:           "PANDN",
	PAUSE:           "PAUSE",
	PAVGB:           "PAVGB",
	PAVGW:           "PAVGW",
	PBLENDVB:        "PBLENDVB",
	PBLENDW:         "PBLENDW",
	PCLMULQDQ:       "PCLMULQDQ",
	PCMPEQB:         "PCMPEQB",
	PCMPEQD:         "PCMPEQD",
	PCMPEQQ:         "PCMPEQQ",
	PCMPEQW:         "PCMPEQW",
	PCMPESTRI:       "PCMPESTRI",
	PCMPESTRM:       "PCMPESTRM",
	PCMPGTB:         "PCMPGTB",
	PCMPGTD:         "PCMPGTD",
	PCMPGTQ:         "PCMPGTQ",
	PCMPGTW:         "PCMPGTW",
	PCMPISTRI:       "PCMPISTRI",
	PCMPISTRM:       "PCMPISTRM",
	PEXTRB:          "PEXTRB",
	PEXTRD:          "PEXTRD",
	PEXTRQ:          "PEXTRQ",
	PEXTRW:          "PEXTRW",
	PHADDD:          "PHADDD",
	PHADDSW:         "PHADDSW",
	PHADDW:          "PHADDW",
	PHMINPOSUW:      "PHMINPOSUW",
	PHSUBD:          "PHSUBD",
	PHSUBSW:         "PHSUBSW",
	PHSUBW:          "PHSUBW",
	PINSRB:          "PINSRB",
	PINSRD:          "PINSRD",
	PINSRQ:          "PINSRQ",
	PINSRW:          "PINSRW",
	PMADDUBSW:       "PMADDUBSW",
	PMADDWD:         "PMADDWD",
	PMAXSB:          "PMAXSB",
	PMAXSD:          "PMAXSD",
	PMAXSW:          "PMAXSW",
	PMAXUB:          "PMAXUB",
	PMAXUD:          "PMAXUD",
	PMAXUW:          "PMAXUW",
	PMINSB:          "PMINSB",
	PMINSD:          "PMINSD",
	PMINSW:          "PMINSW",
	PMINUB:          "PMINUB",
	PMINUD:          "PMINUD",
	PMINUW:          "PMINUW",
	PMOVMSKB:        "PMOVMSKB",
	PMOVSXBD:        "PMOVSXBD",
	PMOVSXBQ:        "PMOVSXBQ",
	PMOVSXBW:        "PMOVSXBW",
	PMOVSXDQ:        "PMOVSXDQ",
	PMOVSXWD:        "PMOVSXWD",
	PMOVSXWQ:        "PMOVSXWQ",
	PMOVZXBD:        "PMOVZXBD",
	PMOVZXBQ:        "PMOVZXBQ",
	PMOVZXBW:        "PMOVZXBW",
	PMOVZXDQ:        "PMOVZXDQ",
	PMOVZXWD:        "PMOVZXWD",
	PMOVZXWQ:        "PMOVZXWQ",
	PMULDQ:          "PMULDQ",
	PMULHRSW:        "PMULHRSW",
	PMULHUW:         "PMULHUW",
	PMULHW:          "PMULHW",
	PMULLD:          "PMULLD",
	PMULLW:          "PMULLW",
	PMULUDQ:         "PMULUDQ",
	POP:             "POP",
	POPA:            "POPA",
	POPAD:           "POPAD",
	POPCNT:          "POPCNT",
	POPF:            "POPF",
	POPFD:           "POPFD",
	POPFQ:           "POPFQ",
	POR:             "POR",
	PREFETCHNTA:     "PREFETCHNTA",
	PREFETCHT0:      "PREFETCHT0",
	PREFETCHT1:      "PREFETCHT1",
	PREFETCHT2:      "PREFETCHT2",
	PREFETCHW:       "PREFETCHW",
	PSADBW:          "PSADBW",
	PSHUFB:          "PSHUFB",
	PSHUFD:          "PSHUFD",
	PSHUFHW:         "PSHUFHW",
	PSHUFLW:         "PSHUFLW",
	PSHUFW:          "PSHUFW",
	PSIGNB:          "PSIGNB",
	PSIGND:          "PSIGND",
	PSIGNW:          "PSIGNW",
	PSLLD:           "PSLLD",
	PSLLDQ:          "PSLLDQ",
	PSLLQ:           "PSLLQ",
	PSLLW:           "PSLLW",
	PSRAD:           "PSRAD",
	PSRAW:           "PSRAW",
	PSRLD:           "PSRLD",
	PSRLDQ:          "PSRLDQ",
	PSRLQ:           "PSRLQ",
	PSRLW:           "PSRLW",
	PSUBB:           "PSUBB",
	PSUBD:           "PSUBD",
	PSUBQ:           "PSUBQ",
	PSUBSB:          "PSUBSB",
	PSUBSW:          "PSUBSW",
	PSUBUSB:         "PSUBUSB",
	PSUBUSW:         "PSUBUSW",
	PSUBW:           "PSUBW",
	PTEST:           "PTEST",
	PUNPCKHBW:       "PUNPCKHBW",
	PUNPCKHDQ:       "PUNPCKHDQ",
	PUNPCKHQDQ:      "PUNPCKHQDQ",
	PUNPCKHWD:       "PUNPCKHWD",
	PUNPCKLBW:       "PUNPCKLBW",
	PUNPCKLDQ:       "PUNPCKLDQ",
	PUNPCKLQDQ:      "PUNPCKLQDQ",
	PUNPCKLWD:       "PUNPCKLWD",
	PUSH:            "PUSH",
	PUSHA:           "PUSHA",
	PUSHAD:          "PUSHAD",
	PUSHF:           "PUSHF",
	PUSHFD:          "PUSHFD",
	PUSHFQ:          "PUSHFQ",
	PXOR:            "PXOR",
	RCL:             "RCL",
	RCPPS:           "RCPPS",
	RCPSS:           "RCPSS",
	RCR:             "RCR",
	RDFSBASE:        "RDFSBASE",
	RDGSBASE:        "RDGSBASE",
	RDMSR:           "RDMSR",
	RDPMC:           "RDPMC",
	RDRAND:          "RDRAND",
	RDTSC:           "RDTSC",
	RDTSCP:          "RDTSCP",
	RET:             "RET",
	ROL:             "ROL",
	ROR:             "ROR",
	ROUNDPD:         "ROUNDPD",
	ROUNDPS:         "ROUNDPS",
	ROUNDSD:         "ROUNDSD",
	ROUNDSS:         "ROUNDSS",
	RSM:             "RSM",
	RSQRTPS:         "RSQRTPS",
	RSQRTSS:         "RSQRTSS",
	SAHF:            "SAHF",
	SAR:             "SAR",
	SBB:             "SBB",
	SCASB:           "SCASB",
	SCASD:           "SCASD",
	SCASQ:           "SCASQ",
	SCASW:           "SCASW",
	SETA:            "SETA",
	SETAE:           "SETAE",
	SETB:            "SETB",
	SETBE:           "SETBE",
	SETE:            "SETE",
	SETG:            "SETG",
	SETGE:           "SETGE",
	SETL:            "SETL",
	SETLE:           "SETLE",
	SETNE:           "SETNE",
	SETNO:           "SETNO",
	SETNP:           "SETNP",
	SETNS:           "SETNS",
	SETO:            "SETO",
	SETP:            "SETP",
	SETS:            "SETS",
	SFENCE:          "SFENCE",
	SGDT:            "SGDT",
	SHL:             "SHL",
	SHLD:            "SHLD",
	SHR:             "SHR",
	SHRD:            "SHRD",
	SHUFPD:          "SHUFPD",
	SHUFPS:          "SHUFPS",
	SIDT:            "SIDT",
	SLDT:            "SLDT",
	SMSW:            "SMSW",
	SQRTPD:          "SQRTPD",
	SQRTPS:          "SQRTPS",
	SQRTSD:          "SQRTSD",
	SQRTSS:          "SQRTSS",
	STC:             "STC",
	STD:             "STD",
	STI:             "STI",
	STMXCSR:         "STMXCSR",
	STOSB:           "STOSB",
	STOSD:           "STOSD",
	STOSQ:           "STOSQ",
	STOSW:           "STOSW",
	STR:             "STR",
	SUB:             "SUB",
	SUBPD:           "SUBPD",
	SUBPS:           "SUBPS",
	SUBSD:           "SUBSD",
	SUBSS:           "SUBSS",
	SWAPGS:          "SWAPGS",
	SYSCALL:         "SYSCALL",
	SYSENTER:        "SYSENTER",
	SYSEXIT:         "SYSEXIT",
	SYSRET:          "SYSRET",
	TEST:            "TEST",
	TZCNT:           "TZCNT",
	UCOMISD:         "UCOMISD",
	UCOMISS:         "UCOMISS",
	UD0:             "UD0",
	UD1:             "UD1",
	UD2:             "UD2",
	UNPCKHPD:        "UNPCKHPD",
	UNPCKHPS:        "UNPCKHPS",
	UNPCKLPD:        "UNPCKLPD",
	UNPCKLPS:        "UNPCKLPS",
	VERR:            "VERR",
	VERW:            "VERW",
	VMOVDQA:         "VMOVDQA",
	VMOVDQU:         "VMOVDQU",
	VMOVNTDQ:        "VMOVNTDQ",
	VMOVNTDQA:       "VMOVNTDQA",
	VZEROUPPER:      "VZEROUPPER",
	WBINVD:          "WBINVD",
	WRFSBASE:        "WRFSBASE",
	WRGSBASE:        "WRGSBASE",
	WRMSR:           "WRMSR",
	XABORT:          "XABORT",
	XADD:            "XADD",
	XBEGIN:          "XBEGIN",
	XCHG:            "XCHG",
	XEND:            "XEND",
	XGETBV:          "XGETBV",
	XLATB:           "XLATB",
	XOR:             "XOR",
	XORPD:           "XORPD",
	XORPS:           "XORPS",
	XRSTOR:          "XRSTOR",
	XRSTOR64:        "XRSTOR64",
	XRSTORS:         "XRSTORS",
	XRSTORS64:       "XRSTORS64",
	XSAVE:           "XSAVE",
	XSAVE64:         "XSAVE64",
	XSAVEC:          "XSAVEC",
	XSAVEC64:        "XSAVEC64",
	XSAVEOPT:        "XSAVEOPT",
	XSAVEOPT64:      "XSAVEOPT64",
	XSAVES:          "XSAVES",
	XSAVES64:        "XSAVES64",
	XSETBV:          "XSETBV",
	XTEST:           "XTEST",
}
