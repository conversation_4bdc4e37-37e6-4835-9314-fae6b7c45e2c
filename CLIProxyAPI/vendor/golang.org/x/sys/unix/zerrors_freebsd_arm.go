// mkerrors.sh
// Code generated by the command above; see README.md. DO NOT EDIT.

//go:build arm && freebsd

// Code generated by cmd/cgo -godefs; DO NOT EDIT.
// cgo -godefs -- _const.go

package unix

import "syscall"

const (
	AF_APPLETALK                   = 0x10
	AF_ARP                         = 0x23
	AF_ATM                         = 0x1e
	AF_BLUETOOTH                   = 0x24
	AF_CCITT                       = 0xa
	AF_CHAOS                       = 0x5
	AF_CNT                         = 0x15
	AF_COIP                        = 0x14
	AF_DATAKIT                     = 0x9
	AF_DECnet                      = 0xc
	AF_DLI                         = 0xd
	AF_E164                        = 0x1a
	AF_ECMA                        = 0x8
	AF_HYLINK                      = 0xf
	AF_IEEE80211                   = 0x25
	AF_IMPLINK                     = 0x3
	AF_INET                        = 0x2
	AF_INET6                       = 0x1c
	AF_INET6_SDP                   = 0x2a
	AF_INET_SDP                    = 0x28
	AF_IPX                         = 0x17
	AF_ISDN                        = 0x1a
	AF_ISO                         = 0x7
	AF_LAT                         = 0xe
	AF_LINK                        = 0x12
	AF_LOCAL                       = 0x1
	AF_MAX                         = 0x2a
	AF_NATM                        = 0x1d
	AF_NETBIOS                     = 0x6
	AF_NETGRAPH                    = 0x20
	AF_OSI                         = 0x7
	AF_PUP                         = 0x4
	AF_ROUTE                       = 0x11
	AF_SCLUSTER                    = 0x22
	AF_SIP                         = 0x18
	AF_SLOW                        = 0x21
	AF_SNA                         = 0xb
	AF_UNIX                        = 0x1
	AF_UNSPEC                      = 0x0
	AF_VENDOR00                    = 0x27
	AF_VENDOR01                    = 0x29
	AF_VENDOR02                    = 0x2b
	AF_VENDOR03                    = 0x2d
	AF_VENDOR04                    = 0x2f
	AF_VENDOR05                    = 0x31
	AF_VENDOR06                    = 0x33
	AF_VENDOR07                    = 0x35
	AF_VENDOR08                    = 0x37
	AF_VENDOR09                    = 0x39
	AF_VENDOR10                    = 0x3b
	AF_VENDOR11                    = 0x3d
	AF_VENDOR12                    = 0x3f
	AF_VENDOR13                    = 0x41
	AF_VENDOR14                    = 0x43
	AF_VENDOR15                    = 0x45
	AF_VENDOR16                    = 0x47
	AF_VENDOR17                    = 0x49
	AF_VENDOR18                    = 0x4b
	AF_VENDOR19                    = 0x4d
	AF_VENDOR20                    = 0x4f
	AF_VENDOR21                    = 0x51
	AF_VENDOR22                    = 0x53
	AF_VENDOR23                    = 0x55
	AF_VENDOR24                    = 0x57
	AF_VENDOR25                    = 0x59
	AF_VENDOR26                    = 0x5b
	AF_VENDOR27                    = 0x5d
	AF_VENDOR28                    = 0x5f
	AF_VENDOR29                    = 0x61
	AF_VENDOR30                    = 0x63
	AF_VENDOR31                    = 0x65
	AF_VENDOR32                    = 0x67
	AF_VENDOR33                    = 0x69
	AF_VENDOR34                    = 0x6b
	AF_VENDOR35                    = 0x6d
	AF_VENDOR36                    = 0x6f
	AF_VENDOR37                    = 0x71
	AF_VENDOR38                    = 0x73
	AF_VENDOR39                    = 0x75
	AF_VENDOR40                    = 0x77
	AF_VENDOR41                    = 0x79
	AF_VENDOR42                    = 0x7b
	AF_VENDOR43                    = 0x7d
	AF_VENDOR44                    = 0x7f
	AF_VENDOR45                    = 0x81
	AF_VENDOR46                    = 0x83
	AF_VENDOR47                    = 0x85
	ALTWERASE                      = 0x200
	B0                             = 0x0
	B110                           = 0x6e
	B115200                        = 0x1c200
	B1200                          = 0x4b0
	B134                           = 0x86
	B14400                         = 0x3840
	B150                           = 0x96
	B1800                          = 0x708
	B19200                         = 0x4b00
	B200                           = 0xc8
	B230400                        = 0x38400
	B2400                          = 0x960
	B28800                         = 0x7080
	B300                           = 0x12c
	B38400                         = 0x9600
	B460800                        = 0x70800
	B4800                          = 0x12c0
	B50                            = 0x32
	B57600                         = 0xe100
	B600                           = 0x258
	B7200                          = 0x1c20
	B75                            = 0x4b
	B76800                         = 0x12c00
	B921600                        = 0xe1000
	B9600                          = 0x2580
	BIOCFEEDBACK                   = 0x8004427c
	BIOCFLUSH                      = 0x20004268
	BIOCGBLEN                      = 0x40044266
	BIOCGDIRECTION                 = 0x40044276
	BIOCGDLT                       = 0x4004426a
	BIOCGDLTLIST                   = 0xc0084279
	BIOCGETBUFMODE                 = 0x4004427d
	BIOCGETIF                      = 0x4020426b
	BIOCGETZMAX                    = 0x4004427f
	BIOCGHDRCMPLT                  = 0x40044274
	BIOCGRSIG                      = 0x40044272
	BIOCGRTIMEOUT                  = 0x4010426e
	BIOCGSEESENT                   = 0x40044276
	BIOCGSTATS                     = 0x4008426f
	BIOCGTSTAMP                    = 0x40044283
	BIOCIMMEDIATE                  = 0x80044270
	BIOCLOCK                       = 0x2000427a
	BIOCPROMISC                    = 0x20004269
	BIOCROTZBUF                    = 0x400c4280
	BIOCSBLEN                      = 0xc0044266
	BIOCSDIRECTION                 = 0x80044277
	BIOCSDLT                       = 0x80044278
	BIOCSETBUFMODE                 = 0x8004427e
	BIOCSETF                       = 0x80084267
	BIOCSETFNR                     = 0x80084282
	BIOCSETIF                      = 0x8020426c
	BIOCSETVLANPCP                 = 0x80044285
	BIOCSETWF                      = 0x8008427b
	BIOCSETZBUF                    = 0x800c4281
	BIOCSHDRCMPLT                  = 0x80044275
	BIOCSRSIG                      = 0x80044273
	BIOCSRTIMEOUT                  = 0x8010426d
	BIOCSSEESENT                   = 0x80044277
	BIOCSTSTAMP                    = 0x80044284
	BIOCVERSION                    = 0x40044271
	BPF_A                          = 0x10
	BPF_ABS                        = 0x20
	BPF_ADD                        = 0x0
	BPF_ALIGNMENT                  = 0x4
	BPF_ALU                        = 0x4
	BPF_AND                        = 0x50
	BPF_B                          = 0x10
	BPF_BUFMODE_BUFFER             = 0x1
	BPF_BUFMODE_ZBUF               = 0x2
	BPF_DIV                        = 0x30
	BPF_H                          = 0x8
	BPF_IMM                        = 0x0
	BPF_IND                        = 0x40
	BPF_JA                         = 0x0
	BPF_JEQ                        = 0x10
	BPF_JGE                        = 0x30
	BPF_JGT                        = 0x20
	BPF_JMP                        = 0x5
	BPF_JSET                       = 0x40
	BPF_K                          = 0x0
	BPF_LD                         = 0x0
	BPF_LDX                        = 0x1
	BPF_LEN                        = 0x80
	BPF_LSH                        = 0x60
	BPF_MAJOR_VERSION              = 0x1
	BPF_MAXBUFSIZE                 = 0x80000
	BPF_MAXINSNS                   = 0x200
	BPF_MEM                        = 0x60
	BPF_MEMWORDS                   = 0x10
	BPF_MINBUFSIZE                 = 0x20
	BPF_MINOR_VERSION              = 0x1
	BPF_MISC                       = 0x7
	BPF_MOD                        = 0x90
	BPF_MSH                        = 0xa0
	BPF_MUL                        = 0x20
	BPF_NEG                        = 0x80
	BPF_OR                         = 0x40
	BPF_RELEASE                    = 0x30bb6
	BPF_RET                        = 0x6
	BPF_RSH                        = 0x70
	BPF_ST                         = 0x2
	BPF_STX                        = 0x3
	BPF_SUB                        = 0x10
	BPF_TAX                        = 0x0
	BPF_TXA                        = 0x80
	BPF_T_BINTIME                  = 0x2
	BPF_T_BINTIME_FAST             = 0x102
	BPF_T_BINTIME_MONOTONIC        = 0x202
	BPF_T_BINTIME_MONOTONIC_FAST   = 0x302
	BPF_T_FAST                     = 0x100
	BPF_T_FLAG_MASK                = 0x300
	BPF_T_FORMAT_MASK              = 0x3
	BPF_T_MICROTIME                = 0x0
	BPF_T_MICROTIME_FAST           = 0x100
	BPF_T_MICROTIME_MONOTONIC      = 0x200
	BPF_T_MICROTIME_MONOTONIC_FAST = 0x300
	BPF_T_MONOTONIC                = 0x200
	BPF_T_MONOTONIC_FAST           = 0x300
	BPF_T_NANOTIME                 = 0x1
	BPF_T_NANOTIME_FAST            = 0x101
	BPF_T_NANOTIME_MONOTONIC       = 0x201
	BPF_T_NANOTIME_MONOTONIC_FAST  = 0x301
	BPF_T_NONE                     = 0x3
	BPF_T_NORMAL                   = 0x0
	BPF_W                          = 0x0
	BPF_X                          = 0x8
	BPF_XOR                        = 0xa0
	BRKINT                         = 0x2
	CAP_ACCEPT                     = 0x200000020000000
	CAP_ACL_CHECK                  = 0x400000000010000
	CAP_ACL_DELETE                 = 0x400000000020000
	CAP_ACL_GET                    = 0x400000000040000
	CAP_ACL_SET                    = 0x400000000080000
	CAP_ALL0                       = 0x20007ffffffffff
	CAP_ALL1                       = 0x4000000001fffff
	CAP_BIND                       = 0x200000040000000
	CAP_BINDAT                     = 0x200008000000400
	CAP_CHFLAGSAT                  = 0x200000000001400
	CAP_CONNECT                    = 0x200000080000000
	CAP_CONNECTAT                  = 0x200010000000400
	CAP_CREATE                     = 0x200000000000040
	CAP_EVENT                      = 0x400000000000020
	CAP_EXTATTR_DELETE             = 0x400000000001000
	CAP_EXTATTR_GET                = 0x400000000002000
	CAP_EXTATTR_LIST               = 0x400000000004000
	CAP_EXTATTR_SET                = 0x400000000008000
	CAP_FCHDIR                     = 0x200000000000800
	CAP_FCHFLAGS                   = 0x200000000001000
	CAP_FCHMOD                     = 0x200000000002000
	CAP_FCHMODAT                   = 0x200000000002400
	CAP_FCHOWN                     = 0x200000000004000
	CAP_FCHOWNAT                   = 0x200000000004400
	CAP_FCNTL                      = 0x200000000008000
	CAP_FCNTL_ALL                  = 0x78
	CAP_FCNTL_GETFL                = 0x8
	CAP_FCNTL_GETOWN               = 0x20
	CAP_FCNTL_SETFL                = 0x10
	CAP_FCNTL_SETOWN               = 0x40
	CAP_FEXECVE                    = 0x200000000000080
	CAP_FLOCK                      = 0x200000000010000
	CAP_FPATHCONF                  = 0x200000000020000
	CAP_FSCK                       = 0x200000000040000
	CAP_FSTAT                      = 0x200000000080000
	CAP_FSTATAT                    = 0x200000000080400
	CAP_FSTATFS                    = 0x200000000100000
	CAP_FSYNC                      = 0x200000000000100
	CAP_FTRUNCATE                  = 0x200000000000200
	CAP_FUTIMES                    = 0x200000000200000
	CAP_FUTIMESAT                  = 0x200000000200400
	CAP_GETPEERNAME                = 0x200000100000000
	CAP_GETSOCKNAME                = 0x200000200000000
	CAP_GETSOCKOPT                 = 0x200000400000000
	CAP_IOCTL                      = 0x400000000000080
	CAP_IOCTLS_ALL                 = 0x7fffffff
	CAP_KQUEUE                     = 0x400000000100040
	CAP_KQUEUE_CHANGE              = 0x400000000100000
	CAP_KQUEUE_EVENT               = 0x400000000000040
	CAP_LINKAT_SOURCE              = 0x200020000000400
	CAP_LINKAT_TARGET              = 0x200000000400400
	CAP_LISTEN                     = 0x200000800000000
	CAP_LOOKUP                     = 0x200000000000400
	CAP_MAC_GET                    = 0x400000000000001
	CAP_MAC_SET                    = 0x400000000000002
	CAP_MKDIRAT                    = 0x200000000800400
	CAP_MKFIFOAT                   = 0x200000001000400
	CAP_MKNODAT                    = 0x200000002000400
	CAP_MMAP                       = 0x200000000000010
	CAP_MMAP_R                     = 0x20000000000001d
	CAP_MMAP_RW                    = 0x20000000000001f
	CAP_MMAP_RWX                   = 0x20000000000003f
	CAP_MMAP_RX                    = 0x20000000000003d
	CAP_MMAP_W                     = 0x20000000000001e
	CAP_MMAP_WX                    = 0x20000000000003e
	CAP_MMAP_X                     = 0x20000000000003c
	CAP_PDGETPID                   = 0x400000000000200
	CAP_PDKILL                     = 0x400000000000800
	CAP_PDWAIT                     = 0x400000000000400
	CAP_PEELOFF                    = 0x200001000000000
	CAP_POLL_EVENT                 = 0x400000000000020
	CAP_PREAD                      = 0x20000000000000d
	CAP_PWRITE                     = 0x20000000000000e
	CAP_READ                       = 0x200000000000001
	CAP_RECV                       = 0x200000000000001
	CAP_RENAMEAT_SOURCE            = 0x200000004000400
	CAP_RENAMEAT_TARGET            = 0x200040000000400
	CAP_RIGHTS_VERSION             = 0x0
	CAP_RIGHTS_VERSION_00          = 0x0
	CAP_SEEK                       = 0x20000000000000c
	CAP_SEEK_TELL                  = 0x200000000000004
	CAP_SEM_GETVALUE               = 0x400000000000004
	CAP_SEM_POST                   = 0x400000000000008
	CAP_SEM_WAIT                   = 0x400000000000010
	CAP_SEND                       = 0x200000000000002
	CAP_SETSOCKOPT                 = 0x200002000000000
	CAP_SHUTDOWN                   = 0x200004000000000
	CAP_SOCK_CLIENT                = 0x200007780000003
	CAP_SOCK_SERVER                = 0x200007f60000003
	CAP_SYMLINKAT                  = 0x200000008000400
	CAP_TTYHOOK                    = 0x400000000000100
	CAP_UNLINKAT                   = 0x200000010000400
	CAP_UNUSED0_44                 = 0x200080000000000
	CAP_UNUSED0_57                 = 0x300000000000000
	CAP_UNUSED1_22                 = 0x400000000200000
	CAP_UNUSED1_57                 = 0x500000000000000
	CAP_WRITE                      = 0x200000000000002
	CFLUSH                         = 0xf
	CLOCAL                         = 0x8000
	CLOCK_MONOTONIC                = 0x4
	CLOCK_MONOTONIC_FAST           = 0xc
	CLOCK_MONOTONIC_PRECISE        = 0xb
	CLOCK_PROCESS_CPUTIME_ID       = 0xf
	CLOCK_PROF                     = 0x2
	CLOCK_REALTIME                 = 0x0
	CLOCK_REALTIME_FAST            = 0xa
	CLOCK_REALTIME_PRECISE         = 0x9
	CLOCK_SECOND                   = 0xd
	CLOCK_THREAD_CPUTIME_ID        = 0xe
	CLOCK_UPTIME                   = 0x5
	CLOCK_UPTIME_FAST              = 0x8
	CLOCK_UPTIME_PRECISE           = 0x7
	CLOCK_VIRTUAL                  = 0x1
	CPUSTATES                      = 0x5
	CP_IDLE                        = 0x4
	CP_INTR                        = 0x3
	CP_NICE                        = 0x1
	CP_SYS                         = 0x2
	CP_USER                        = 0x0
	CREAD                          = 0x800
	CRTSCTS                        = 0x30000
	CS5                            = 0x0
	CS6                            = 0x100
	CS7                            = 0x200
	CS8                            = 0x300
	CSIZE                          = 0x300
	CSTART                         = 0x11
	CSTATUS                        = 0x14
	CSTOP                          = 0x13
	CSTOPB                         = 0x400
	CSUSP                          = 0x1a
	CTL_HW                         = 0x6
	CTL_KERN                       = 0x1
	CTL_MAXNAME                    = 0x18
	CTL_NET                        = 0x4
	DIOCGATTR                      = 0xc148648e
	DIOCGDELETE                    = 0x80106488
	DIOCGFLUSH                     = 0x20006487
	DIOCGFRONTSTUFF                = 0x40086486
	DIOCGFWHEADS                   = 0x40046483
	DIOCGFWSECTORS                 = 0x40046482
	DIOCGIDENT                     = 0x41006489
	DIOCGMEDIASIZE                 = 0x40086481
	DIOCGPHYSPATH                  = 0x4400648d
	DIOCGPROVIDERNAME              = 0x4400648a
	DIOCGSECTORSIZE                = 0x40046480
	DIOCGSTRIPEOFFSET              = 0x4008648c
	DIOCGSTRIPESIZE                = 0x4008648b
	DIOCSKERNELDUMP                = 0x804c6490
	DIOCSKERNELDUMP_FREEBSD11      = 0x80046485
	DIOCZONECMD                    = 0xc078648f
	DLT_A429                       = 0xb8
	DLT_A653_ICM                   = 0xb9
	DLT_AIRONET_HEADER             = 0x78
	DLT_AOS                        = 0xde
	DLT_APPLE_IP_OVER_IEEE1394     = 0x8a
	DLT_ARCNET                     = 0x7
	DLT_ARCNET_LINUX               = 0x81
	DLT_ATM_CLIP                   = 0x13
	DLT_ATM_RFC1483                = 0xb
	DLT_AURORA                     = 0x7e
	DLT_AX25                       = 0x3
	DLT_AX25_KISS                  = 0xca
	DLT_BACNET_MS_TP               = 0xa5
	DLT_BLUETOOTH_BREDR_BB         = 0xff
	DLT_BLUETOOTH_HCI_H4           = 0xbb
	DLT_BLUETOOTH_HCI_H4_WITH_PHDR = 0xc9
	DLT_BLUETOOTH_LE_LL            = 0xfb
	DLT_BLUETOOTH_LE_LL_WITH_PHDR  = 0x100
	DLT_BLUETOOTH_LINUX_MONITOR    = 0xfe
	DLT_CAN20B                     = 0xbe
	DLT_CAN_SOCKETCAN              = 0xe3
	DLT_CHAOS                      = 0x5
	DLT_CHDLC                      = 0x68
	DLT_CISCO_IOS                  = 0x76
	DLT_CLASS_NETBSD_RAWAF         = 0x2240000
	DLT_C_HDLC                     = 0x68
	DLT_C_HDLC_WITH_DIR            = 0xcd
	DLT_DBUS                       = 0xe7
	DLT_DECT                       = 0xdd
	DLT_DISPLAYPORT_AUX            = 0x113
	DLT_DOCSIS                     = 0x8f
	DLT_DOCSIS31_XRA31             = 0x111
	DLT_DVB_CI                     = 0xeb
	DLT_ECONET                     = 0x73
	DLT_EN10MB                     = 0x1
	DLT_EN3MB                      = 0x2
	DLT_ENC                        = 0x6d
	DLT_EPON                       = 0x103
	DLT_ERF                        = 0xc5
	DLT_ERF_ETH                    = 0xaf
	DLT_ERF_POS                    = 0xb0
	DLT_ETHERNET_MPACKET           = 0x112
	DLT_FC_2                       = 0xe0
	DLT_FC_2_WITH_FRAME_DELIMS     = 0xe1
	DLT_FDDI                       = 0xa
	DLT_FLEXRAY                    = 0xd2
	DLT_FRELAY                     = 0x6b
	DLT_FRELAY_WITH_DIR            = 0xce
	DLT_GCOM_SERIAL                = 0xad
	DLT_GCOM_T1E1                  = 0xac
	DLT_GPF_F                      = 0xab
	DLT_GPF_T                      = 0xaa
	DLT_GPRS_LLC                   = 0xa9
	DLT_GSMTAP_ABIS                = 0xda
	DLT_GSMTAP_UM                  = 0xd9
	DLT_IBM_SN                     = 0x92
	DLT_IBM_SP                     = 0x91
	DLT_IEEE802                    = 0x6
	DLT_IEEE802_11                 = 0x69
	DLT_IEEE802_11_RADIO           = 0x7f
	DLT_IEEE802_11_RADIO_AVS       = 0xa3
	DLT_IEEE802_15_4               = 0xc3
	DLT_IEEE802_15_4_LINUX         = 0xbf
	DLT_IEEE802_15_4_NOFCS         = 0xe6
	DLT_IEEE802_15_4_NONASK_PHY    = 0xd7
	DLT_IEEE802_16_MAC_CPS         = 0xbc
	DLT_IEEE802_16_MAC_CPS_RADIO   = 0xc1
	DLT_INFINIBAND                 = 0xf7
	DLT_IPFILTER                   = 0x74
	DLT_IPMB_KONTRON               = 0xc7
	DLT_IPMB_LINUX                 = 0xd1
	DLT_IPMI_HPM_2                 = 0x104
	DLT_IPNET                      = 0xe2
	DLT_IPOIB                      = 0xf2
	DLT_IPV4                       = 0xe4
	DLT_IPV6                       = 0xe5
	DLT_IP_OVER_FC                 = 0x7a
	DLT_ISO_14443                  = 0x108
	DLT_JUNIPER_ATM1               = 0x89
	DLT_JUNIPER_ATM2               = 0x87
	DLT_JUNIPER_ATM_CEMIC          = 0xee
	DLT_JUNIPER_CHDLC              = 0xb5
	DLT_JUNIPER_ES                 = 0x84
	DLT_JUNIPER_ETHER              = 0xb2
	DLT_JUNIPER_FIBRECHANNEL       = 0xea
	DLT_JUNIPER_FRELAY             = 0xb4
	DLT_JUNIPER_GGSN               = 0x85
	DLT_JUNIPER_ISM                = 0xc2
	DLT_JUNIPER_MFR                = 0x86
	DLT_JUNIPER_MLFR               = 0x83
	DLT_JUNIPER_MLPPP              = 0x82
	DLT_JUNIPER_MONITOR            = 0xa4
	DLT_JUNIPER_PIC_PEER           = 0xae
	DLT_JUNIPER_PPP                = 0xb3
	DLT_JUNIPER_PPPOE              = 0xa7
	DLT_JUNIPER_PPPOE_ATM          = 0xa8
	DLT_JUNIPER_SERVICES           = 0x88
	DLT_JUNIPER_SRX_E2E            = 0xe9
	DLT_JUNIPER_ST                 = 0xc8
	DLT_JUNIPER_VP                 = 0xb7
	DLT_JUNIPER_VS                 = 0xe8
	DLT_LAPB_WITH_DIR              = 0xcf
	DLT_LAPD                       = 0xcb
	DLT_LIN                        = 0xd4
	DLT_LINUX_EVDEV                = 0xd8
	DLT_LINUX_IRDA                 = 0x90
	DLT_LINUX_LAPD                 = 0xb1
	DLT_LINUX_PPP_WITHDIRECTION    = 0xa6
	DLT_LINUX_SLL                  = 0x71
	DLT_LINUX_SLL2                 = 0x114
	DLT_LOOP                       = 0x6c
	DLT_LORATAP                    = 0x10e
	DLT_LTALK                      = 0x72
	DLT_MATCHING_MAX               = 0x114
	DLT_MATCHING_MIN               = 0x68
	DLT_MFR                        = 0xb6
	DLT_MOST                       = 0xd3
	DLT_MPEG_2_TS                  = 0xf3
	DLT_MPLS                       = 0xdb
	DLT_MTP2                       = 0x8c
	DLT_MTP2_WITH_PHDR             = 0x8b
	DLT_MTP3                       = 0x8d
	DLT_MUX27010                   = 0xec
	DLT_NETANALYZER                = 0xf0
	DLT_NETANALYZER_TRANSPARENT    = 0xf1
	DLT_NETLINK                    = 0xfd
	DLT_NFC_LLCP                   = 0xf5
	DLT_NFLOG                      = 0xef
	DLT_NG40                       = 0xf4
	DLT_NORDIC_BLE                 = 0x110
	DLT_NULL                       = 0x0
	DLT_OPENFLOW                   = 0x10b
	DLT_PCI_EXP                    = 0x7d
	DLT_PFLOG                      = 0x75
	DLT_PFSYNC                     = 0x79
	DLT_PKTAP                      = 0x102
	DLT_PPI                        = 0xc0
	DLT_PPP                        = 0x9
	DLT_PPP_BSDOS                  = 0xe
	DLT_PPP_ETHER                  = 0x33
	DLT_PPP_PPPD                   = 0xa6
	DLT_PPP_SERIAL                 = 0x32
	DLT_PPP_WITH_DIR               = 0xcc
	DLT_PPP_WITH_DIRECTION         = 0xa6
	DLT_PRISM_HEADER               = 0x77
	DLT_PROFIBUS_DL                = 0x101
	DLT_PRONET                     = 0x4
	DLT_RAIF1                      = 0xc6
	DLT_RAW                        = 0xc
	DLT_RDS                        = 0x109
	DLT_REDBACK_SMARTEDGE          = 0x20
	DLT_RIO                        = 0x7c
	DLT_RTAC_SERIAL                = 0xfa
	DLT_SCCP                       = 0x8e
	DLT_SCTP                       = 0xf8
	DLT_SDLC                       = 0x10c
	DLT_SITA                       = 0xc4
	DLT_SLIP                       = 0x8
	DLT_SLIP_BSDOS                 = 0xd
	DLT_STANAG_5066_D_PDU          = 0xed
	DLT_SUNATM                     = 0x7b
	DLT_SYMANTEC_FIREWALL          = 0x63
	DLT_TI_LLN_SNIFFER             = 0x10d
	DLT_TZSP                       = 0x80
	DLT_USB                        = 0xba
	DLT_USBPCAP                    = 0xf9
	DLT_USB_DARWIN                 = 0x10a
	DLT_USB_FREEBSD                = 0xba
	DLT_USB_LINUX                  = 0xbd
	DLT_USB_LINUX_MMAPPED          = 0xdc
	DLT_USER0                      = 0x93
	DLT_USER1                      = 0x94
	DLT_USER10                     = 0x9d
	DLT_USER11                     = 0x9e
	DLT_USER12                     = 0x9f
	DLT_USER13                     = 0xa0
	DLT_USER14                     = 0xa1
	DLT_USER15                     = 0xa2
	DLT_USER2                      = 0x95
	DLT_USER3                      = 0x96
	DLT_USER4                      = 0x97
	DLT_USER5                      = 0x98
	DLT_USER6                      = 0x99
	DLT_USER7                      = 0x9a
	DLT_USER8                      = 0x9b
	DLT_USER9                      = 0x9c
	DLT_VSOCK                      = 0x10f
	DLT_WATTSTOPPER_DLM            = 0x107
	DLT_WIHART                     = 0xdf
	DLT_WIRESHARK_UPPER_PDU        = 0xfc
	DLT_X2E_SERIAL                 = 0xd5
	DLT_X2E_XORAYA                 = 0xd6
	DLT_ZWAVE_R1_R2                = 0x105
	DLT_ZWAVE_R3                   = 0x106
	DT_BLK                         = 0x6
	DT_CHR                         = 0x2
	DT_DIR                         = 0x4
	DT_FIFO                        = 0x1
	DT_LNK                         = 0xa
	DT_REG                         = 0x8
	DT_SOCK                        = 0xc
	DT_UNKNOWN                     = 0x0
	DT_WHT                         = 0xe
	ECHO                           = 0x8
	ECHOCTL                        = 0x40
	ECHOE                          = 0x2
	ECHOK                          = 0x4
	ECHOKE                         = 0x1
	ECHONL                         = 0x10
	ECHOPRT                        = 0x20
	EVFILT_AIO                     = -0x3
	EVFILT_EMPTY                   = -0xd
	EVFILT_FS                      = -0x9
	EVFILT_LIO                     = -0xa
	EVFILT_PROC                    = -0x5
	EVFILT_PROCDESC                = -0x8
	EVFILT_READ                    = -0x1
	EVFILT_SENDFILE                = -0xc
	EVFILT_SIGNAL                  = -0x6
	EVFILT_SYSCOUNT                = 0xd
	EVFILT_TIMER                   = -0x7
	EVFILT_USER                    = -0xb
	EVFILT_VNODE                   = -0x4
	EVFILT_WRITE                   = -0x2
	EVNAMEMAP_NAME_SIZE            = 0x40
	EV_ADD                         = 0x1
	EV_CLEAR                       = 0x20
	EV_DELETE                      = 0x2
	EV_DISABLE                     = 0x8
	EV_DISPATCH                    = 0x80
	EV_DROP                        = 0x1000
	EV_ENABLE                      = 0x4
	EV_EOF                         = 0x8000
	EV_ERROR                       = 0x4000
	EV_FLAG1                       = 0x2000
	EV_FLAG2                       = 0x4000
	EV_FORCEONESHOT                = 0x100
	EV_ONESHOT                     = 0x10
	EV_RECEIPT                     = 0x40
	EV_SYSFLAGS                    = 0xf000
	EXTA                           = 0x4b00
	EXTATTR_MAXNAMELEN             = 0xff
	EXTATTR_NAMESPACE_EMPTY        = 0x0
	EXTATTR_NAMESPACE_SYSTEM       = 0x2
	EXTATTR_NAMESPACE_USER         = 0x1
	EXTB                           = 0x9600
	EXTPROC                        = 0x800
	FD_CLOEXEC                     = 0x1
	FD_SETSIZE                     = 0x400
	FLUSHO                         = 0x800000
	F_CANCEL                       = 0x5
	F_DUP2FD                       = 0xa
	F_DUP2FD_CLOEXEC               = 0x12
	F_DUPFD                        = 0x0
	F_DUPFD_CLOEXEC                = 0x11
	F_GETFD                        = 0x1
	F_GETFL                        = 0x3
	F_GETLK                        = 0xb
	F_GETOWN                       = 0x5
	F_OGETLK                       = 0x7
	F_OK                           = 0x0
	F_OSETLK                       = 0x8
	F_OSETLKW                      = 0x9
	F_RDAHEAD                      = 0x10
	F_RDLCK                        = 0x1
	F_READAHEAD                    = 0xf
	F_SETFD                        = 0x2
	F_SETFL                        = 0x4
	F_SETLK                        = 0xc
	F_SETLKW                       = 0xd
	F_SETLK_REMOTE                 = 0xe
	F_SETOWN                       = 0x6
	F_UNLCK                        = 0x2
	F_UNLCKSYS                     = 0x4
	F_WRLCK                        = 0x3
	HUPCL                          = 0x4000
	HW_MACHINE                     = 0x1
	ICANON                         = 0x100
	ICMP6_FILTER                   = 0x12
	ICRNL                          = 0x100
	IEXTEN                         = 0x400
	IFAN_ARRIVAL                   = 0x0
	IFAN_DEPARTURE                 = 0x1
	IFCAP_WOL_MAGIC                = 0x2000
	IFF_ALLMULTI                   = 0x200
	IFF_ALTPHYS                    = 0x4000
	IFF_BROADCAST                  = 0x2
	IFF_CANTCHANGE                 = 0x218f52
	IFF_CANTCONFIG                 = 0x10000
	IFF_DEBUG                      = 0x4
	IFF_DRV_OACTIVE                = 0x400
	IFF_DRV_RUNNING                = 0x40
	IFF_DYING                      = 0x200000
	IFF_LINK0                      = 0x1000
	IFF_LINK1                      = 0x2000
	IFF_LINK2                      = 0x4000
	IFF_LOOPBACK                   = 0x8
	IFF_MONITOR                    = 0x40000
	IFF_MULTICAST                  = 0x8000
	IFF_NOARP                      = 0x80
	IFF_NOGROUP                    = 0x800000
	IFF_OACTIVE                    = 0x400
	IFF_POINTOPOINT                = 0x10
	IFF_PPROMISC                   = 0x20000
	IFF_PROMISC                    = 0x100
	IFF_RENAMING                   = 0x400000
	IFF_RUNNING                    = 0x40
	IFF_SIMPLEX                    = 0x800
	IFF_STATICARP                  = 0x80000
	IFF_UP                         = 0x1
	IFNAMSIZ                       = 0x10
	IFT_BRIDGE                     = 0xd1
	IFT_CARP                       = 0xf8
	IFT_IEEE1394                   = 0x90
	IFT_INFINIBAND                 = 0xc7
	IFT_L2VLAN                     = 0x87
	IFT_L3IPVLAN                   = 0x88
	IFT_PPP                        = 0x17
	IFT_PROPVIRTUAL                = 0x35
	IGNBRK                         = 0x1
	IGNCR                          = 0x80
	IGNPAR                         = 0x4
	IMAXBEL                        = 0x2000
	INLCR                          = 0x40
	INPCK                          = 0x10
	IN_CLASSA_HOST                 = 0xffffff
	IN_CLASSA_MAX                  = 0x80
	IN_CLASSA_NET                  = 0xff000000
	IN_CLASSA_NSHIFT               = 0x18
	IN_CLASSB_HOST                 = 0xffff
	IN_CLASSB_MAX                  = 0x10000
	IN_CLASSB_NET                  = 0xffff0000
	IN_CLASSB_NSHIFT               = 0x10
	IN_CLASSC_HOST                 = 0xff
	IN_CLASSC_NET                  = 0xffffff00
	IN_CLASSC_NSHIFT               = 0x8
	IN_CLASSD_HOST                 = 0xfffffff
	IN_CLASSD_NET                  = 0xf0000000
	IN_CLASSD_NSHIFT               = 0x1c
	IN_LOOPBACKNET                 = 0x7f
	IN_RFC3021_MASK                = 0xfffffffe
	IPPROTO_3PC                    = 0x22
	IPPROTO_ADFS                   = 0x44
	IPPROTO_AH                     = 0x33
	IPPROTO_AHIP                   = 0x3d
	IPPROTO_APES                   = 0x63
	IPPROTO_ARGUS                  = 0xd
	IPPROTO_AX25                   = 0x5d
	IPPROTO_BHA                    = 0x31
	IPPROTO_BLT                    = 0x1e
	IPPROTO_BRSATMON               = 0x4c
	IPPROTO_CARP                   = 0x70
	IPPROTO_CFTP                   = 0x3e
	IPPROTO_CHAOS                  = 0x10
	IPPROTO_CMTP                   = 0x26
	IPPROTO_CPHB                   = 0x49
	IPPROTO_CPNX                   = 0x48
	IPPROTO_DCCP                   = 0x21
	IPPROTO_DDP                    = 0x25
	IPPROTO_DGP                    = 0x56
	IPPROTO_DIVERT                 = 0x102
	IPPROTO_DONE                   = 0x101
	IPPROTO_DSTOPTS                = 0x3c
	IPPROTO_EGP                    = 0x8
	IPPROTO_EMCON                  = 0xe
	IPPROTO_ENCAP                  = 0x62
	IPPROTO_EON                    = 0x50
	IPPROTO_ESP                    = 0x32
	IPPROTO_ETHERIP                = 0x61
	IPPROTO_FRAGMENT               = 0x2c
	IPPROTO_GGP                    = 0x3
	IPPROTO_GMTP                   = 0x64
	IPPROTO_GRE                    = 0x2f
	IPPROTO_HELLO                  = 0x3f
	IPPROTO_HIP                    = 0x8b
	IPPROTO_HMP                    = 0x14
	IPPROTO_HOPOPTS                = 0x0
	IPPROTO_ICMP                   = 0x1
	IPPROTO_ICMPV6                 = 0x3a
	IPPROTO_IDP                    = 0x16
	IPPROTO_IDPR                   = 0x23
	IPPROTO_IDRP                   = 0x2d
	IPPROTO_IGMP                   = 0x2
	IPPROTO_IGP                    = 0x55
	IPPROTO_IGRP                   = 0x58
	IPPROTO_IL                     = 0x28
	IPPROTO_INLSP                  = 0x34
	IPPROTO_INP                    = 0x20
	IPPROTO_IP                     = 0x0
	IPPROTO_IPCOMP                 = 0x6c
	IPPROTO_IPCV                   = 0x47
	IPPROTO_IPEIP                  = 0x5e
	IPPROTO_IPIP                   = 0x4
	IPPROTO_IPPC                   = 0x43
	IPPROTO_IPV4                   = 0x4
	IPPROTO_IPV6                   = 0x29
	IPPROTO_IRTP                   = 0x1c
	IPPROTO_KRYPTOLAN              = 0x41
	IPPROTO_LARP                   = 0x5b
	IPPROTO_LEAF1                  = 0x19
	IPPROTO_LEAF2                  = 0x1a
	IPPROTO_MAX                    = 0x100
	IPPROTO_MEAS                   = 0x13
	IPPROTO_MH                     = 0x87
	IPPROTO_MHRP                   = 0x30
	IPPROTO_MICP                   = 0x5f
	IPPROTO_MOBILE                 = 0x37
	IPPROTO_MPLS                   = 0x89
	IPPROTO_MTP                    = 0x5c
	IPPROTO_MUX                    = 0x12
	IPPROTO_ND                     = 0x4d
	IPPROTO_NHRP                   = 0x36
	IPPROTO_NONE                   = 0x3b
	IPPROTO_NSP                    = 0x1f
	IPPROTO_NVPII                  = 0xb
	IPPROTO_OLD_DIVERT             = 0xfe
	IPPROTO_OSPFIGP                = 0x59
	IPPROTO_PFSYNC                 = 0xf0
	IPPROTO_PGM                    = 0x71
	IPPROTO_PIGP                   = 0x9
	IPPROTO_PIM                    = 0x67
	IPPROTO_PRM                    = 0x15
	IPPROTO_PUP                    = 0xc
	IPPROTO_PVP                    = 0x4b
	IPPROTO_RAW                    = 0xff
	IPPROTO_RCCMON                 = 0xa
	IPPROTO_RDP                    = 0x1b
	IPPROTO_RESERVED_253           = 0xfd
	IPPROTO_RESERVED_254           = 0xfe
	IPPROTO_ROUTING                = 0x2b
	IPPROTO_RSVP                   = 0x2e
	IPPROTO_RVD                    = 0x42
	IPPROTO_SATEXPAK               = 0x40
	IPPROTO_SATMON                 = 0x45
	IPPROTO_SCCSP                  = 0x60
	IPPROTO_SCTP                   = 0x84
	IPPROTO_SDRP                   = 0x2a
	IPPROTO_SEND                   = 0x103
	IPPROTO_SHIM6                  = 0x8c
	IPPROTO_SKIP                   = 0x39
	IPPROTO_SPACER                 = 0x7fff
	IPPROTO_SRPC                   = 0x5a
	IPPROTO_ST                     = 0x7
	IPPROTO_SVMTP                  = 0x52
	IPPROTO_SWIPE                  = 0x35
	IPPROTO_TCF                    = 0x57
	IPPROTO_TCP                    = 0x6
	IPPROTO_TLSP                   = 0x38
	IPPROTO_TP                     = 0x1d
	IPPROTO_TPXX                   = 0x27
	IPPROTO_TRUNK1                 = 0x17
	IPPROTO_TRUNK2                 = 0x18
	IPPROTO_TTP                    = 0x54
	IPPROTO_UDP                    = 0x11
	IPPROTO_UDPLITE                = 0x88
	IPPROTO_VINES                  = 0x53
	IPPROTO_VISA                   = 0x46
	IPPROTO_VMTP                   = 0x51
	IPPROTO_WBEXPAK                = 0x4f
	IPPROTO_WBMON                  = 0x4e
	IPPROTO_WSN                    = 0x4a
	IPPROTO_XNET                   = 0xf
	IPPROTO_XTP                    = 0x24
	IPV6_AUTOFLOWLABEL             = 0x3b
	IPV6_BINDANY                   = 0x40
	IPV6_BINDMULTI                 = 0x41
	IPV6_BINDV6ONLY                = 0x1b
	IPV6_CHECKSUM                  = 0x1a
	IPV6_DEFAULT_MULTICAST_HOPS    = 0x1
	IPV6_DEFAULT_MULTICAST_LOOP    = 0x1
	IPV6_DEFHLIM                   = 0x40
	IPV6_DONTFRAG                  = 0x3e
	IPV6_DSTOPTS                   = 0x32
	IPV6_FLOWID                    = 0x43
	IPV6_FLOWINFO_MASK             = 0xffffff0f
	IPV6_FLOWLABEL_LEN             = 0x14
	IPV6_FLOWLABEL_MASK            = 0xffff0f00
	IPV6_FLOWTYPE                  = 0x44
	IPV6_FRAGTTL                   = 0x78
	IPV6_FW_ADD                    = 0x1e
	IPV6_FW_DEL                    = 0x1f
	IPV6_FW_FLUSH                  = 0x20
	IPV6_FW_GET                    = 0x22
	IPV6_FW_ZERO                   = 0x21
	IPV6_HLIMDEC                   = 0x1
	IPV6_HOPLIMIT                  = 0x2f
	IPV6_HOPOPTS                   = 0x31
	IPV6_IPSEC_POLICY              = 0x1c
	IPV6_JOIN_GROUP                = 0xc
	IPV6_LEAVE_GROUP               = 0xd
	IPV6_MAXHLIM                   = 0xff
	IPV6_MAXOPTHDR                 = 0x800
	IPV6_MAXPACKET                 = 0xffff
	IPV6_MAX_GROUP_SRC_FILTER      = 0x200
	IPV6_MAX_MEMBERSHIPS           = 0xfff
	IPV6_MAX_SOCK_SRC_FILTER       = 0x80
	IPV6_MMTU                      = 0x500
	IPV6_MSFILTER                  = 0x4a
	IPV6_MULTICAST_HOPS            = 0xa
	IPV6_MULTICAST_IF              = 0x9
	IPV6_MULTICAST_LOOP            = 0xb
	IPV6_NEXTHOP                   = 0x30
	IPV6_ORIGDSTADDR               = 0x48
	IPV6_PATHMTU                   = 0x2c
	IPV6_PKTINFO                   = 0x2e
	IPV6_PORTRANGE                 = 0xe
	IPV6_PORTRANGE_DEFAULT         = 0x0
	IPV6_PORTRANGE_HIGH            = 0x1
	IPV6_PORTRANGE_LOW             = 0x2
	IPV6_PREFER_TEMPADDR           = 0x3f
	IPV6_RECVDSTOPTS               = 0x28
	IPV6_RECVFLOWID                = 0x46
	IPV6_RECVHOPLIMIT              = 0x25
	IPV6_RECVHOPOPTS               = 0x27
	IPV6_RECVORIGDSTADDR           = 0x48
	IPV6_RECVPATHMTU               = 0x2b
	IPV6_RECVPKTINFO               = 0x24
	IPV6_RECVRSSBUCKETID           = 0x47
	IPV6_RECVRTHDR                 = 0x26
	IPV6_RECVTCLASS                = 0x39
	IPV6_RSSBUCKETID               = 0x45
	IPV6_RSS_LISTEN_BUCKET         = 0x42
	IPV6_RTHDR                     = 0x33
	IPV6_RTHDRDSTOPTS              = 0x23
	IPV6_RTHDR_LOOSE               = 0x0
	IPV6_RTHDR_STRICT              = 0x1
	IPV6_RTHDR_TYPE_0              = 0x0
	IPV6_SOCKOPT_RESERVED1         = 0x3
	IPV6_TCLASS                    = 0x3d
	IPV6_UNICAST_HOPS              = 0x4
	IPV6_USE_MIN_MTU               = 0x2a
	IPV6_V6ONLY                    = 0x1b
	IPV6_VERSION                   = 0x60
	IPV6_VERSION_MASK              = 0xf0
	IPV6_VLAN_PCP                  = 0x4b
	IP_ADD_MEMBERSHIP              = 0xc
	IP_ADD_SOURCE_MEMBERSHIP       = 0x46
	IP_BINDANY                     = 0x18
	IP_BINDMULTI                   = 0x19
	IP_BLOCK_SOURCE                = 0x48
	IP_DEFAULT_MULTICAST_LOOP      = 0x1
	IP_DEFAULT_MULTICAST_TTL       = 0x1
	IP_DF                          = 0x4000
	IP_DONTFRAG                    = 0x43
	IP_DROP_MEMBERSHIP             = 0xd
	IP_DROP_SOURCE_MEMBERSHIP      = 0x47
	IP_DUMMYNET3                   = 0x31
	IP_DUMMYNET_CONFIGURE          = 0x3c
	IP_DUMMYNET_DEL                = 0x3d
	IP_DUMMYNET_FLUSH              = 0x3e
	IP_DUMMYNET_GET                = 0x40
	IP_FLOWID                      = 0x5a
	IP_FLOWTYPE                    = 0x5b
	IP_FW3                         = 0x30
	IP_FW_ADD                      = 0x32
	IP_FW_DEL                      = 0x33
	IP_FW_FLUSH                    = 0x34
	IP_FW_GET                      = 0x36
	IP_FW_NAT_CFG                  = 0x38
	IP_FW_NAT_DEL                  = 0x39
	IP_FW_NAT_GET_CONFIG           = 0x3a
	IP_FW_NAT_GET_LOG              = 0x3b
	IP_FW_RESETLOG                 = 0x37
	IP_FW_TABLE_ADD                = 0x28
	IP_FW_TABLE_DEL                = 0x29
	IP_FW_TABLE_FLUSH              = 0x2a
	IP_FW_TABLE_GETSIZE            = 0x2b
	IP_FW_TABLE_LIST               = 0x2c
	IP_FW_ZERO                     = 0x35
	IP_HDRINCL                     = 0x2
	IP_IPSEC_POLICY                = 0x15
	IP_MAXPACKET                   = 0xffff
	IP_MAX_GROUP_SRC_FILTER        = 0x200
	IP_MAX_MEMBERSHIPS             = 0xfff
	IP_MAX_SOCK_MUTE_FILTER        = 0x80
	IP_MAX_SOCK_SRC_FILTER         = 0x80
	IP_MF                          = 0x2000
	IP_MINTTL                      = 0x42
	IP_MSFILTER                    = 0x4a
	IP_MSS                         = 0x240
	IP_MULTICAST_IF                = 0x9
	IP_MULTICAST_LOOP              = 0xb
	IP_MULTICAST_TTL               = 0xa
	IP_MULTICAST_VIF               = 0xe
	IP_OFFMASK                     = 0x1fff
	IP_ONESBCAST                   = 0x17
	IP_OPTIONS                     = 0x1
	IP_ORIGDSTADDR                 = 0x1b
	IP_PORTRANGE                   = 0x13
	IP_PORTRANGE_DEFAULT           = 0x0
	IP_PORTRANGE_HIGH              = 0x1
	IP_PORTRANGE_LOW               = 0x2
	IP_RECVDSTADDR                 = 0x7
	IP_RECVFLOWID                  = 0x5d
	IP_RECVIF                      = 0x14
	IP_RECVOPTS                    = 0x5
	IP_RECVORIGDSTADDR             = 0x1b
	IP_RECVRETOPTS                 = 0x6
	IP_RECVRSSBUCKETID             = 0x5e
	IP_RECVTOS                     = 0x44
	IP_RECVTTL                     = 0x41
	IP_RETOPTS                     = 0x8
	IP_RF                          = 0x8000
	IP_RSSBUCKETID                 = 0x5c
	IP_RSS_LISTEN_BUCKET           = 0x1a
	IP_RSVP_OFF                    = 0x10
	IP_RSVP_ON                     = 0xf
	IP_RSVP_VIF_OFF                = 0x12
	IP_RSVP_VIF_ON                 = 0x11
	IP_SENDSRCADDR                 = 0x7
	IP_TOS                         = 0x3
	IP_TTL                         = 0x4
	IP_UNBLOCK_SOURCE              = 0x49
	IP_VLAN_PCP                    = 0x4b
	ISIG                           = 0x80
	ISTRIP                         = 0x20
	ITIMER_PROF                    = 0x2
	ITIMER_REAL                    = 0x0
	ITIMER_VIRTUAL                 = 0x1
	IXANY                          = 0x800
	IXOFF                          = 0x400
	IXON                           = 0x200
	KERN_HOSTNAME                  = 0xa
	KERN_OSRELEASE                 = 0x2
	KERN_OSTYPE                    = 0x1
	KERN_VERSION                   = 0x4
	LOCAL_CONNWAIT                 = 0x4
	LOCAL_CREDS                    = 0x2
	LOCAL_PEERCRED                 = 0x1
	LOCAL_VENDOR                   = 0x80000000
	LOCK_EX                        = 0x2
	LOCK_NB                        = 0x4
	LOCK_SH                        = 0x1
	LOCK_UN                        = 0x8
	MADV_AUTOSYNC                  = 0x7
	MADV_CORE                      = 0x9
	MADV_DONTNEED                  = 0x4
	MADV_FREE                      = 0x5
	MADV_NOCORE                    = 0x8
	MADV_NORMAL                    = 0x0
	MADV_NOSYNC                    = 0x6
	MADV_PROTECT                   = 0xa
	MADV_RANDOM                    = 0x1
	MADV_SEQUENTIAL                = 0x2
	MADV_WILLNEED                  = 0x3
	MAP_ALIGNED_SUPER              = 0x1000000
	MAP_ALIGNMENT_MASK             = -0x1000000
	MAP_ALIGNMENT_SHIFT            = 0x18
	MAP_ANON                       = 0x1000
	MAP_ANONYMOUS                  = 0x1000
	MAP_COPY                       = 0x2
	MAP_EXCL                       = 0x4000
	MAP_FILE                       = 0x0
	MAP_FIXED                      = 0x10
	MAP_GUARD                      = 0x2000
	MAP_HASSEMAPHORE               = 0x200
	MAP_NOCORE                     = 0x20000
	MAP_NOSYNC                     = 0x800
	MAP_PREFAULT_READ              = 0x40000
	MAP_PRIVATE                    = 0x2
	MAP_RESERVED0020               = 0x20
	MAP_RESERVED0040               = 0x40
	MAP_RESERVED0080               = 0x80
	MAP_RESERVED0100               = 0x100
	MAP_SHARED                     = 0x1
	MAP_STACK                      = 0x400
	MCAST_BLOCK_SOURCE             = 0x54
	MCAST_EXCLUDE                  = 0x2
	MCAST_INCLUDE                  = 0x1
	MCAST_JOIN_GROUP               = 0x50
	MCAST_JOIN_SOURCE_GROUP        = 0x52
	MCAST_LEAVE_GROUP              = 0x51
	MCAST_LEAVE_SOURCE_GROUP       = 0x53
	MCAST_UNBLOCK_SOURCE           = 0x55
	MCAST_UNDEFINED                = 0x0
	MCL_CURRENT                    = 0x1
	MCL_FUTURE                     = 0x2
	MNT_ACLS                       = 0x8000000
	MNT_ASYNC                      = 0x40
	MNT_AUTOMOUNTED                = 0x200000000
	MNT_BYFSID                     = 0x8000000
	MNT_CMDFLAGS                   = 0xd0f0000
	MNT_DEFEXPORTED                = 0x200
	MNT_DELEXPORT                  = 0x20000
	MNT_EXKERB                     = 0x800
	MNT_EXPORTANON                 = 0x400
	MNT_EXPORTED                   = 0x100
	MNT_EXPUBLIC                   = 0x20000000
	MNT_EXRDONLY                   = 0x80
	MNT_FORCE                      = 0x80000
	MNT_GJOURNAL                   = 0x2000000
	MNT_IGNORE                     = 0x800000
	MNT_LAZY                       = 0x3
	MNT_LOCAL                      = 0x1000
	MNT_MULTILABEL                 = 0x4000000
	MNT_NFS4ACLS                   = 0x10
	MNT_NOATIME                    = 0x10000000
	MNT_NOCLUSTERR                 = 0x40000000
	MNT_NOCLUSTERW                 = 0x80000000
	MNT_NOEXEC                     = 0x4
	MNT_NONBUSY                    = 0x4000000
	MNT_NOSUID                     = 0x8
	MNT_NOSYMFOLLOW                = 0x400000
	MNT_NOWAIT                     = 0x2
	MNT_QUOTA                      = 0x2000
	MNT_RDONLY                     = 0x1
	MNT_RELOAD                     = 0x40000
	MNT_ROOTFS                     = 0x4000
	MNT_SNAPSHOT                   = 0x1000000
	MNT_SOFTDEP                    = 0x200000
	MNT_SUIDDIR                    = 0x100000
	MNT_SUJ                        = 0x100000000
	MNT_SUSPEND                    = 0x4
	MNT_SYNCHRONOUS                = 0x2
	MNT_UNION                      = 0x20
	MNT_UNTRUSTED                  = 0x800000000
	MNT_UPDATE                     = 0x10000
	MNT_UPDATEMASK                 = 0xad8d0807e
	MNT_USER                       = 0x8000
	MNT_VERIFIED                   = 0x400000000
	MNT_VISFLAGMASK                = 0xffef0ffff
	MNT_WAIT                       = 0x1
	MSG_CMSG_CLOEXEC               = 0x40000
	MSG_COMPAT                     = 0x8000
	MSG_CTRUNC                     = 0x20
	MSG_DONTROUTE                  = 0x4
	MSG_DONTWAIT                   = 0x80
	MSG_EOF                        = 0x100
	MSG_EOR                        = 0x8
	MSG_NBIO                       = 0x4000
	MSG_NOSIGNAL                   = 0x20000
	MSG_NOTIFICATION               = 0x2000
	MSG_OOB                        = 0x1
	MSG_PEEK                       = 0x2
	MSG_TRUNC                      = 0x10
	MSG_WAITALL                    = 0x40
	MSG_WAITFORONE                 = 0x80000
	MS_ASYNC                       = 0x1
	MS_INVALIDATE                  = 0x2
	MS_SYNC                        = 0x0
	NAME_MAX                       = 0xff
	NET_RT_DUMP                    = 0x1
	NET_RT_FLAGS                   = 0x2
	NET_RT_IFLIST                  = 0x3
	NET_RT_IFLISTL                 = 0x5
	NET_RT_IFMALIST                = 0x4
	NFDBITS                        = 0x20
	NOFLSH                         = 0x80000000
	NOKERNINFO                     = 0x2000000
	NOTE_ABSTIME                   = 0x10
	NOTE_ATTRIB                    = 0x8
	NOTE_CHILD                     = 0x4
	NOTE_CLOSE                     = 0x100
	NOTE_CLOSE_WRITE               = 0x200
	NOTE_DELETE                    = 0x1
	NOTE_EXEC                      = 0x20000000
	NOTE_EXIT                      = 0x80000000
	NOTE_EXTEND                    = 0x4
	NOTE_FFAND                     = 0x40000000
	NOTE_FFCOPY                    = 0xc0000000
	NOTE_FFCTRLMASK                = 0xc0000000
	NOTE_FFLAGSMASK                = 0xffffff
	NOTE_FFNOP                     = 0x0
	NOTE_FFOR                      = 0x80000000
	NOTE_FILE_POLL                 = 0x2
	NOTE_FORK                      = 0x40000000
	NOTE_LINK                      = 0x10
	NOTE_LOWAT                     = 0x1
	NOTE_MSECONDS                  = 0x2
	NOTE_NSECONDS                  = 0x8
	NOTE_OPEN                      = 0x80
	NOTE_PCTRLMASK                 = 0xf0000000
	NOTE_PDATAMASK                 = 0xfffff
	NOTE_READ                      = 0x400
	NOTE_RENAME                    = 0x20
	NOTE_REVOKE                    = 0x40
	NOTE_SECONDS                   = 0x1
	NOTE_TRACK                     = 0x1
	NOTE_TRACKERR                  = 0x2
	NOTE_TRIGGER                   = 0x1000000
	NOTE_USECONDS                  = 0x4
	NOTE_WRITE                     = 0x2
	OCRNL                          = 0x10
	ONLCR                          = 0x2
	ONLRET                         = 0x40
	ONOCR                          = 0x20
	ONOEOT                         = 0x8
	OPOST                          = 0x1
	OXTABS                         = 0x4
	O_ACCMODE                      = 0x3
	O_APPEND                       = 0x8
	O_ASYNC                        = 0x40
	O_CLOEXEC                      = 0x100000
	O_CREAT                        = 0x200
	O_DIRECT                       = 0x10000
	O_DIRECTORY                    = 0x20000
	O_EXCL                         = 0x800
	O_EXEC                         = 0x40000
	O_EXLOCK                       = 0x20
	O_FSYNC                        = 0x80
	O_NDELAY                       = 0x4
	O_NOCTTY                       = 0x8000
	O_NOFOLLOW                     = 0x100
	O_NONBLOCK                     = 0x4
	O_RDONLY                       = 0x0
	O_RDWR                         = 0x2
	O_RESOLVE_BENEATH              = 0x800000
	O_SEARCH                       = 0x40000
	O_SHLOCK                       = 0x10
	O_SYNC                         = 0x80
	O_TRUNC                        = 0x400
	O_TTY_INIT                     = 0x80000
	O_VERIFY                       = 0x200000
	O_WRONLY                       = 0x1
	PARENB                         = 0x1000
	PARMRK                         = 0x8
	PARODD                         = 0x2000
	PENDIN                         = 0x20000000
	PIOD_READ_D                    = 0x1
	PIOD_READ_I                    = 0x3
	PIOD_WRITE_D                   = 0x2
	PIOD_WRITE_I                   = 0x4
	PRIO_PGRP                      = 0x1
	PRIO_PROCESS                   = 0x0
	PRIO_USER                      = 0x2
	PROT_EXEC                      = 0x4
	PROT_NONE                      = 0x0
	PROT_READ                      = 0x1
	PROT_WRITE                     = 0x2
	PTRACE_DEFAULT                 = 0x1
	PTRACE_EXEC                    = 0x1
	PTRACE_FORK                    = 0x8
	PTRACE_LWP                     = 0x10
	PTRACE_SCE                     = 0x2
	PTRACE_SCX                     = 0x4
	PTRACE_SYSCALL                 = 0x6
	PTRACE_VFORK                   = 0x20
	PT_ATTACH                      = 0xa
	PT_CLEARSTEP                   = 0x10
	PT_CONTINUE                    = 0x7
	PT_DETACH                      = 0xb
	PT_FIRSTMACH                   = 0x40
	PT_FOLLOW_FORK                 = 0x17
	PT_GETDBREGS                   = 0x25
	PT_GETFPREGS                   = 0x23
	PT_GETLWPLIST                  = 0xf
	PT_GETNUMLWPS                  = 0xe
	PT_GETREGS                     = 0x21
	PT_GETVFPREGS                  = 0x40
	PT_GET_EVENT_MASK              = 0x19
	PT_GET_SC_ARGS                 = 0x1b
	PT_GET_SC_RET                  = 0x1c
	PT_IO                          = 0xc
	PT_KILL                        = 0x8
	PT_LWPINFO                     = 0xd
	PT_LWP_EVENTS                  = 0x18
	PT_READ_D                      = 0x2
	PT_READ_I                      = 0x1
	PT_RESUME                      = 0x13
	PT_SETDBREGS                   = 0x26
	PT_SETFPREGS                   = 0x24
	PT_SETREGS                     = 0x22
	PT_SETSTEP                     = 0x11
	PT_SETVFPREGS                  = 0x41
	PT_SET_EVENT_MASK              = 0x1a
	PT_STEP                        = 0x9
	PT_SUSPEND                     = 0x12
	PT_SYSCALL                     = 0x16
	PT_TO_SCE                      = 0x14
	PT_TO_SCX                      = 0x15
	PT_TRACE_ME                    = 0x0
	PT_VM_ENTRY                    = 0x29
	PT_VM_TIMESTAMP                = 0x28
	PT_WRITE_D                     = 0x5
	PT_WRITE_I                     = 0x4
	P_ZONEID                       = 0xc
	RLIMIT_AS                      = 0xa
	RLIMIT_CORE                    = 0x4
	RLIMIT_CPU                     = 0x0
	RLIMIT_DATA                    = 0x2
	RLIMIT_FSIZE                   = 0x1
	RLIMIT_MEMLOCK                 = 0x6
	RLIMIT_NOFILE                  = 0x8
	RLIMIT_NPROC                   = 0x7
	RLIMIT_RSS                     = 0x5
	RLIMIT_STACK                   = 0x3
	RLIM_INFINITY                  = 0x7fffffffffffffff
	RTAX_AUTHOR                    = 0x6
	RTAX_BRD                       = 0x7
	RTAX_DST                       = 0x0
	RTAX_GATEWAY                   = 0x1
	RTAX_GENMASK                   = 0x3
	RTAX_IFA                       = 0x5
	RTAX_IFP                       = 0x4
	RTAX_MAX                       = 0x8
	RTAX_NETMASK                   = 0x2
	RTA_AUTHOR                     = 0x40
	RTA_BRD                        = 0x80
	RTA_DST                        = 0x1
	RTA_GATEWAY                    = 0x2
	RTA_GENMASK                    = 0x8
	RTA_IFA                        = 0x20
	RTA_IFP                        = 0x10
	RTA_NETMASK                    = 0x4
	RTF_BLACKHOLE                  = 0x1000
	RTF_BROADCAST                  = 0x400000
	RTF_DONE                       = 0x40
	RTF_DYNAMIC                    = 0x10
	RTF_FIXEDMTU                   = 0x80000
	RTF_FMASK                      = 0x1004d808
	RTF_GATEWAY                    = 0x2
	RTF_GWFLAG_COMPAT              = 0x80000000
	RTF_HOST                       = 0x4
	RTF_LLDATA                     = 0x400
	RTF_LLINFO                     = 0x400
	RTF_LOCAL                      = 0x200000
	RTF_MODIFIED                   = 0x20
	RTF_MULTICAST                  = 0x800000
	RTF_PINNED                     = 0x100000
	RTF_PROTO1                     = 0x8000
	RTF_PROTO2                     = 0x4000
	RTF_PROTO3                     = 0x40000
	RTF_REJECT                     = 0x8
	RTF_RNH_LOCKED                 = 0x40000000
	RTF_STATIC                     = 0x800
	RTF_STICKY                     = 0x10000000
	RTF_UP                         = 0x1
	RTF_XRESOLVE                   = 0x200
	RTM_ADD                        = 0x1
	RTM_CHANGE                     = 0x3
	RTM_DELADDR                    = 0xd
	RTM_DELETE                     = 0x2
	RTM_DELMADDR                   = 0x10
	RTM_GET                        = 0x4
	RTM_IEEE80211                  = 0x12
	RTM_IFANNOUNCE                 = 0x11
	RTM_IFINFO                     = 0xe
	RTM_LOCK                       = 0x8
	RTM_LOSING                     = 0x5
	RTM_MISS                       = 0x7
	RTM_NEWADDR                    = 0xc
	RTM_NEWMADDR                   = 0xf
	RTM_REDIRECT                   = 0x6
	RTM_RESOLVE                    = 0xb
	RTM_RTTUNIT                    = 0xf4240
	RTM_VERSION                    = 0x5
	RTV_EXPIRE                     = 0x4
	RTV_HOPCOUNT                   = 0x2
	RTV_MTU                        = 0x1
	RTV_RPIPE                      = 0x8
	RTV_RTT                        = 0x40
	RTV_RTTVAR                     = 0x80
	RTV_SPIPE                      = 0x10
	RTV_SSTHRESH                   = 0x20
	RTV_WEIGHT                     = 0x100
	RT_ALL_FIBS                    = -0x1
	RT_BLACKHOLE                   = 0x40
	RT_DEFAULT_FIB                 = 0x0
	RT_HAS_GW                      = 0x80
	RT_HAS_HEADER                  = 0x10
	RT_HAS_HEADER_BIT              = 0x4
	RT_L2_ME                       = 0x4
	RT_L2_ME_BIT                   = 0x2
	RT_LLE_CACHE                   = 0x100
	RT_MAY_LOOP                    = 0x8
	RT_MAY_LOOP_BIT                = 0x3
	RT_REJECT                      = 0x20
	RUSAGE_CHILDREN                = -0x1
	RUSAGE_SELF                    = 0x0
	RUSAGE_THREAD                  = 0x1
	SCM_BINTIME                    = 0x4
	SCM_CREDS                      = 0x3
	SCM_MONOTONIC                  = 0x6
	SCM_REALTIME                   = 0x5
	SCM_RIGHTS                     = 0x1
	SCM_TIMESTAMP                  = 0x2
	SCM_TIME_INFO                  = 0x7
	SEEK_CUR                       = 0x1
	SEEK_DATA                      = 0x3
	SEEK_END                       = 0x2
	SEEK_HOLE                      = 0x4
	SEEK_SET                       = 0x0
	SHUT_RD                        = 0x0
	SHUT_RDWR                      = 0x2
	SHUT_WR                        = 0x1
	SIOCADDMULTI                   = 0x80206931
	SIOCAIFADDR                    = 0x8040691a
	SIOCAIFGROUP                   = 0x80246987
	SIOCATMARK                     = 0x40047307
	SIOCDELMULTI                   = 0x80206932
	SIOCDIFADDR                    = 0x80206919
	SIOCDIFGROUP                   = 0x80246989
	SIOCDIFPHYADDR                 = 0x80206949
	SIOCGDRVSPEC                   = 0xc01c697b
	SIOCGETSGCNT                   = 0xc0147210
	SIOCGETVIFCNT                  = 0xc014720f
	SIOCGHIWAT                     = 0x40047301
	SIOCGHWADDR                    = 0xc020693e
	SIOCGI2C                       = 0xc020693d
	SIOCGIFADDR                    = 0xc0206921
	SIOCGIFALIAS                   = 0xc044692d
	SIOCGIFBRDADDR                 = 0xc0206923
	SIOCGIFCAP                     = 0xc020691f
	SIOCGIFCONF                    = 0xc0086924
	SIOCGIFDESCR                   = 0xc020692a
	SIOCGIFDOWNREASON              = 0xc058699a
	SIOCGIFDSTADDR                 = 0xc0206922
	SIOCGIFFIB                     = 0xc020695c
	SIOCGIFFLAGS                   = 0xc0206911
	SIOCGIFGENERIC                 = 0xc020693a
	SIOCGIFGMEMB                   = 0xc024698a
	SIOCGIFGROUP                   = 0xc0246988
	SIOCGIFINDEX                   = 0xc0206920
	SIOCGIFMAC                     = 0xc0206926
	SIOCGIFMEDIA                   = 0xc0286938
	SIOCGIFMETRIC                  = 0xc0206917
	SIOCGIFMTU                     = 0xc0206933
	SIOCGIFNETMASK                 = 0xc0206925
	SIOCGIFPDSTADDR                = 0xc0206948
	SIOCGIFPHYS                    = 0xc0206935
	SIOCGIFPSRCADDR                = 0xc0206947
	SIOCGIFRSSHASH                 = 0xc0186997
	SIOCGIFRSSKEY                  = 0xc0946996
	SIOCGIFSTATUS                  = 0xc331693b
	SIOCGIFXMEDIA                  = 0xc028698b
	SIOCGLANPCP                    = 0xc0206998
	SIOCGLOWAT                     = 0x40047303
	SIOCGPGRP                      = 0x40047309
	SIOCGPRIVATE_0                 = 0xc0206950
	SIOCGPRIVATE_1                 = 0xc0206951
	SIOCGTUNFIB                    = 0xc020695e
	SIOCIFCREATE                   = 0xc020697a
	SIOCIFCREATE2                  = 0xc020697c
	SIOCIFDESTROY                  = 0x80206979
	SIOCIFGCLONERS                 = 0xc00c6978
	SIOCSDRVSPEC                   = 0x801c697b
	SIOCSHIWAT                     = 0x80047300
	SIOCSIFADDR                    = 0x8020690c
	SIOCSIFBRDADDR                 = 0x80206913
	SIOCSIFCAP                     = 0x8020691e
	SIOCSIFDESCR                   = 0x80206929
	SIOCSIFDSTADDR                 = 0x8020690e
	SIOCSIFFIB                     = 0x8020695d
	SIOCSIFFLAGS                   = 0x80206910
	SIOCSIFGENERIC                 = 0x80206939
	SIOCSIFLLADDR                  = 0x8020693c
	SIOCSIFMAC                     = 0x80206927
	SIOCSIFMEDIA                   = 0xc0206937
	SIOCSIFMETRIC                  = 0x80206918
	SIOCSIFMTU                     = 0x80206934
	SIOCSIFNAME                    = 0x80206928
	SIOCSIFNETMASK                 = 0x80206916
	SIOCSIFPHYADDR                 = 0x80406946
	SIOCSIFPHYS                    = 0x80206936
	SIOCSIFRVNET                   = 0xc020695b
	SIOCSIFVNET                    = 0xc020695a
	SIOCSLANPCP                    = 0x80206999
	SIOCSLOWAT                     = 0x80047302
	SIOCSPGRP                      = 0x80047308
	SIOCSTUNFIB                    = 0x8020695f
	SOCK_CLOEXEC                   = 0x10000000
	SOCK_DGRAM                     = 0x2
	SOCK_MAXADDRLEN                = 0xff
	SOCK_NONBLOCK                  = 0x20000000
	SOCK_RAW                       = 0x3
	SOCK_RDM                       = 0x4
	SOCK_SEQPACKET                 = 0x5
	SOCK_STREAM                    = 0x1
	SOL_LOCAL                      = 0x0
	SOL_SOCKET                     = 0xffff
	SOMAXCONN                      = 0x80
	SO_ACCEPTCONN                  = 0x2
	SO_ACCEPTFILTER                = 0x1000
	SO_BINTIME                     = 0x2000
	SO_BROADCAST                   = 0x20
	SO_DEBUG                       = 0x1
	SO_DOMAIN                      = 0x1019
	SO_DONTROUTE                   = 0x10
	SO_ERROR                       = 0x1007
	SO_KEEPALIVE                   = 0x8
	SO_LABEL                       = 0x1009
	SO_LINGER                      = 0x80
	SO_LISTENINCQLEN               = 0x1013
	SO_LISTENQLEN                  = 0x1012
	SO_LISTENQLIMIT                = 0x1011
	SO_MAX_PACING_RATE             = 0x1018
	SO_NOSIGPIPE                   = 0x800
	SO_NO_DDP                      = 0x8000
	SO_NO_OFFLOAD                  = 0x4000
	SO_OOBINLINE                   = 0x100
	SO_PEERLABEL                   = 0x1010
	SO_PROTOCOL                    = 0x1016
	SO_PROTOTYPE                   = 0x1016
	SO_RCVBUF                      = 0x1002
	SO_RCVLOWAT                    = 0x1004
	SO_RCVTIMEO                    = 0x1006
	SO_RERROR                      = 0x20000
	SO_REUSEADDR                   = 0x4
	SO_REUSEPORT                   = 0x200
	SO_REUSEPORT_LB                = 0x10000
	SO_SETFIB                      = 0x1014
	SO_SNDBUF                      = 0x1001
	SO_SNDLOWAT                    = 0x1003
	SO_SNDTIMEO                    = 0x1005
	SO_TIMESTAMP                   = 0x400
	SO_TS_BINTIME                  = 0x1
	SO_TS_CLOCK                    = 0x1017
	SO_TS_CLOCK_MAX                = 0x3
	SO_TS_DEFAULT                  = 0x0
	SO_TS_MONOTONIC                = 0x3
	SO_TS_REALTIME                 = 0x2
	SO_TS_REALTIME_MICRO           = 0x0
	SO_TYPE                        = 0x1008
	SO_USELOOPBACK                 = 0x40
	SO_USER_COOKIE                 = 0x1015
	SO_VENDOR                      = 0x80000000
	S_BLKSIZE                      = 0x200
	S_IEXEC                        = 0x40
	S_IFBLK                        = 0x6000
	S_IFCHR                        = 0x2000
	S_IFDIR                        = 0x4000
	S_IFIFO                        = 0x1000
	S_IFLNK                        = 0xa000
	S_IFMT                         = 0xf000
	S_IFREG                        = 0x8000
	S_IFSOCK                       = 0xc000
	S_IFWHT                        = 0xe000
	S_IREAD                        = 0x100
	S_IRGRP                        = 0x20
	S_IROTH                        = 0x4
	S_IRUSR                        = 0x100
	S_IRWXG                        = 0x38
	S_IRWXO                        = 0x7
	S_IRWXU                        = 0x1c0
	S_ISGID                        = 0x400
	S_ISTXT                        = 0x200
	S_ISUID                        = 0x800
	S_ISVTX                        = 0x200
	S_IWGRP                        = 0x10
	S_IWOTH                        = 0x2
	S_IWRITE                       = 0x80
	S_IWUSR                        = 0x80
	S_IXGRP                        = 0x8
	S_IXOTH                        = 0x1
	S_IXUSR                        = 0x40
	TAB0                           = 0x0
	TAB3                           = 0x4
	TABDLY                         = 0x4
	TCIFLUSH                       = 0x1
	TCIOFF                         = 0x3
	TCIOFLUSH                      = 0x3
	TCION                          = 0x4
	TCOFLUSH                       = 0x2
	TCOOFF                         = 0x1
	TCOON                          = 0x2
	TCPOPT_EOL                     = 0x0
	TCPOPT_FAST_OPEN               = 0x22
	TCPOPT_MAXSEG                  = 0x2
	TCPOPT_NOP                     = 0x1
	TCPOPT_PAD                     = 0x0
	TCPOPT_SACK                    = 0x5
	TCPOPT_SACK_PERMITTED          = 0x4
	TCPOPT_SIGNATURE               = 0x13
	TCPOPT_TIMESTAMP               = 0x8
	TCPOPT_WINDOW                  = 0x3
	TCP_BBR_ACK_COMP_ALG           = 0x448
	TCP_BBR_ALGORITHM              = 0x43b
	TCP_BBR_DRAIN_INC_EXTRA        = 0x43c
	TCP_BBR_DRAIN_PG               = 0x42e
	TCP_BBR_EXTRA_GAIN             = 0x449
	TCP_BBR_EXTRA_STATE            = 0x453
	TCP_BBR_FLOOR_MIN_TSO          = 0x454
	TCP_BBR_HDWR_PACE              = 0x451
	TCP_BBR_HOLD_TARGET            = 0x436
	TCP_BBR_IWINTSO                = 0x42b
	TCP_BBR_LOWGAIN_FD             = 0x436
	TCP_BBR_LOWGAIN_HALF           = 0x435
	TCP_BBR_LOWGAIN_THRESH         = 0x434
	TCP_BBR_MAX_RTO                = 0x439
	TCP_BBR_MIN_RTO                = 0x438
	TCP_BBR_MIN_TOPACEOUT          = 0x455
	TCP_BBR_ONE_RETRAN             = 0x431
	TCP_BBR_PACE_CROSS             = 0x442
	TCP_BBR_PACE_DEL_TAR           = 0x43f
	TCP_BBR_PACE_OH                = 0x435
	TCP_BBR_PACE_PER_SEC           = 0x43e
	TCP_BBR_PACE_SEG_MAX           = 0x440
	TCP_BBR_PACE_SEG_MIN           = 0x441
	TCP_BBR_POLICER_DETECT         = 0x457
	TCP_BBR_PROBE_RTT_GAIN         = 0x44d
	TCP_BBR_PROBE_RTT_INT          = 0x430
	TCP_BBR_PROBE_RTT_LEN          = 0x44e
	TCP_BBR_RACK_RTT_USE           = 0x44a
	TCP_BBR_RECFORCE               = 0x42c
	TCP_BBR_REC_OVER_HPTS          = 0x43a
	TCP_BBR_RETRAN_WTSO            = 0x44b
	TCP_BBR_RWND_IS_APP            = 0x42f
	TCP_BBR_SEND_IWND_IN_TSO       = 0x44f
	TCP_BBR_STARTUP_EXIT_EPOCH     = 0x43d
	TCP_BBR_STARTUP_LOSS_EXIT      = 0x432
	TCP_BBR_STARTUP_PG             = 0x42d
	TCP_BBR_TMR_PACE_OH            = 0x448
	TCP_BBR_TSLIMITS               = 0x434
	TCP_BBR_TSTMP_RAISES           = 0x456
	TCP_BBR_UNLIMITED              = 0x43b
	TCP_BBR_USEDEL_RATE            = 0x437
	TCP_BBR_USE_LOWGAIN            = 0x433
	TCP_BBR_USE_RACK_CHEAT         = 0x450
	TCP_BBR_UTTER_MAX_TSO          = 0x452
	TCP_CA_NAME_MAX                = 0x10
	TCP_CCALGOOPT                  = 0x41
	TCP_CONGESTION                 = 0x40
	TCP_DATA_AFTER_CLOSE           = 0x44c
	TCP_DELACK                     = 0x48
	TCP_FASTOPEN                   = 0x401
	TCP_FASTOPEN_MAX_COOKIE_LEN    = 0x10
	TCP_FASTOPEN_MIN_COOKIE_LEN    = 0x4
	TCP_FASTOPEN_PSK_LEN           = 0x10
	TCP_FUNCTION_BLK               = 0x2000
	TCP_FUNCTION_NAME_LEN_MAX      = 0x20
	TCP_INFO                       = 0x20
	TCP_KEEPCNT                    = 0x400
	TCP_KEEPIDLE                   = 0x100
	TCP_KEEPINIT                   = 0x80
	TCP_KEEPINTVL                  = 0x200
	TCP_LOG                        = 0x22
	TCP_LOGBUF                     = 0x23
	TCP_LOGDUMP                    = 0x25
	TCP_LOGDUMPID                  = 0x26
	TCP_LOGID                      = 0x24
	TCP_LOG_ID_LEN                 = 0x40
	TCP_MAXBURST                   = 0x4
	TCP_MAXHLEN                    = 0x3c
	TCP_MAXOLEN                    = 0x28
	TCP_MAXSEG                     = 0x2
	TCP_MAXWIN                     = 0xffff
	TCP_MAX_SACK                   = 0x4
	TCP_MAX_WINSHIFT               = 0xe
	TCP_MD5SIG                     = 0x10
	TCP_MINMSS                     = 0xd8
	TCP_MSS                        = 0x218
	TCP_NODELAY                    = 0x1
	TCP_NOOPT                      = 0x8
	TCP_NOPUSH                     = 0x4
	TCP_PCAP_IN                    = 0x1000
	TCP_PCAP_OUT                   = 0x800
	TCP_RACK_EARLY_RECOV           = 0x423
	TCP_RACK_EARLY_SEG             = 0x424
	TCP_RACK_GP_INCREASE           = 0x446
	TCP_RACK_IDLE_REDUCE_HIGH      = 0x444
	TCP_RACK_MIN_PACE              = 0x445
	TCP_RACK_MIN_PACE_SEG          = 0x446
	TCP_RACK_MIN_TO                = 0x422
	TCP_RACK_PACE_ALWAYS           = 0x41f
	TCP_RACK_PACE_MAX_SEG          = 0x41e
	TCP_RACK_PACE_REDUCE           = 0x41d
	TCP_RACK_PKT_DELAY             = 0x428
	TCP_RACK_PROP                  = 0x41b
	TCP_RACK_PROP_RATE             = 0x420
	TCP_RACK_PRR_SENDALOT          = 0x421
	TCP_RACK_REORD_FADE            = 0x426
	TCP_RACK_REORD_THRESH          = 0x425
	TCP_RACK_TLP_INC_VAR           = 0x429
	TCP_RACK_TLP_REDUCE            = 0x41c
	TCP_RACK_TLP_THRESH            = 0x427
	TCP_RACK_TLP_USE               = 0x447
	TCP_VENDOR                     = 0x80000000
	TCSAFLUSH                      = 0x2
	TIMER_ABSTIME                  = 0x1
	TIMER_RELTIME                  = 0x0
	TIOCCBRK                       = 0x2000747a
	TIOCCDTR                       = 0x20007478
	TIOCCONS                       = 0x80047462
	TIOCDRAIN                      = 0x2000745e
	TIOCEXCL                       = 0x2000740d
	TIOCEXT                        = 0x80047460
	TIOCFLUSH                      = 0x80047410
	TIOCGDRAINWAIT                 = 0x40047456
	TIOCGETA                       = 0x402c7413
	TIOCGETD                       = 0x4004741a
	TIOCGPGRP                      = 0x40047477
	TIOCGPTN                       = 0x4004740f
	TIOCGSID                       = 0x40047463
	TIOCGWINSZ                     = 0x40087468
	TIOCMBIC                       = 0x8004746b
	TIOCMBIS                       = 0x8004746c
	TIOCMGDTRWAIT                  = 0x4004745a
	TIOCMGET                       = 0x4004746a
	TIOCMSDTRWAIT                  = 0x8004745b
	TIOCMSET                       = 0x8004746d
	TIOCM_CAR                      = 0x40
	TIOCM_CD                       = 0x40
	TIOCM_CTS                      = 0x20
	TIOCM_DCD                      = 0x40
	TIOCM_DSR                      = 0x100
	TIOCM_DTR                      = 0x2
	TIOCM_LE                       = 0x1
	TIOCM_RI                       = 0x80
	TIOCM_RNG                      = 0x80
	TIOCM_RTS                      = 0x4
	TIOCM_SR                       = 0x10
	TIOCM_ST                       = 0x8
	TIOCNOTTY                      = 0x20007471
	TIOCNXCL                       = 0x2000740e
	TIOCOUTQ                       = 0x40047473
	TIOCPKT                        = 0x80047470
	TIOCPKT_DATA                   = 0x0
	TIOCPKT_DOSTOP                 = 0x20
	TIOCPKT_FLUSHREAD              = 0x1
	TIOCPKT_FLUSHWRITE             = 0x2
	TIOCPKT_IOCTL                  = 0x40
	TIOCPKT_NOSTOP                 = 0x10
	TIOCPKT_START                  = 0x8
	TIOCPKT_STOP                   = 0x4
	TIOCPTMASTER                   = 0x2000741c
	TIOCSBRK                       = 0x2000747b
	TIOCSCTTY                      = 0x20007461
	TIOCSDRAINWAIT                 = 0x80047457
	TIOCSDTR                       = 0x20007479
	TIOCSETA                       = 0x802c7414
	TIOCSETAF                      = 0x802c7416
	TIOCSETAW                      = 0x802c7415
	TIOCSETD                       = 0x8004741b
	TIOCSIG                        = 0x2004745f
	TIOCSPGRP                      = 0x80047476
	TIOCSTART                      = 0x2000746e
	TIOCSTAT                       = 0x20007465
	TIOCSTI                        = 0x80017472
	TIOCSTOP                       = 0x2000746f
	TIOCSWINSZ                     = 0x80087467
	TIOCTIMESTAMP                  = 0x40107459
	TIOCUCNTL                      = 0x80047466
	TOSTOP                         = 0x400000
	UTIME_NOW                      = -0x1
	UTIME_OMIT                     = -0x2
	VDISCARD                       = 0xf
	VDSUSP                         = 0xb
	VEOF                           = 0x0
	VEOL                           = 0x1
	VEOL2                          = 0x2
	VERASE                         = 0x3
	VERASE2                        = 0x7
	VINTR                          = 0x8
	VKILL                          = 0x5
	VLNEXT                         = 0xe
	VMIN                           = 0x10
	VQUIT                          = 0x9
	VREPRINT                       = 0x6
	VSTART                         = 0xc
	VSTATUS                        = 0x12
	VSTOP                          = 0xd
	VSUSP                          = 0xa
	VTIME                          = 0x11
	VWERASE                        = 0x4
	WCONTINUED                     = 0x4
	WCOREFLAG                      = 0x80
	WEXITED                        = 0x10
	WLINUXCLONE                    = 0x80000000
	WNOHANG                        = 0x1
	WNOWAIT                        = 0x8
	WSTOPPED                       = 0x2
	WTRAPPED                       = 0x20
	WUNTRACED                      = 0x2
)

// Errors
const (
	E2BIG           = syscall.Errno(0x7)
	EACCES          = syscall.Errno(0xd)
	EADDRINUSE      = syscall.Errno(0x30)
	EADDRNOTAVAIL   = syscall.Errno(0x31)
	EAFNOSUPPORT    = syscall.Errno(0x2f)
	EAGAIN          = syscall.Errno(0x23)
	EALREADY        = syscall.Errno(0x25)
	EAUTH           = syscall.Errno(0x50)
	EBADF           = syscall.Errno(0x9)
	EBADMSG         = syscall.Errno(0x59)
	EBADRPC         = syscall.Errno(0x48)
	EBUSY           = syscall.Errno(0x10)
	ECANCELED       = syscall.Errno(0x55)
	ECAPMODE        = syscall.Errno(0x5e)
	ECHILD          = syscall.Errno(0xa)
	ECONNABORTED    = syscall.Errno(0x35)
	ECONNREFUSED    = syscall.Errno(0x3d)
	ECONNRESET      = syscall.Errno(0x36)
	EDEADLK         = syscall.Errno(0xb)
	EDESTADDRREQ    = syscall.Errno(0x27)
	EDOM            = syscall.Errno(0x21)
	EDOOFUS         = syscall.Errno(0x58)
	EDQUOT          = syscall.Errno(0x45)
	EEXIST          = syscall.Errno(0x11)
	EFAULT          = syscall.Errno(0xe)
	EFBIG           = syscall.Errno(0x1b)
	EFTYPE          = syscall.Errno(0x4f)
	EHOSTDOWN       = syscall.Errno(0x40)
	EHOSTUNREACH    = syscall.Errno(0x41)
	EIDRM           = syscall.Errno(0x52)
	EILSEQ          = syscall.Errno(0x56)
	EINPROGRESS     = syscall.Errno(0x24)
	EINTEGRITY      = syscall.Errno(0x61)
	EINTR           = syscall.Errno(0x4)
	EINVAL          = syscall.Errno(0x16)
	EIO             = syscall.Errno(0x5)
	EISCONN         = syscall.Errno(0x38)
	EISDIR          = syscall.Errno(0x15)
	ELAST           = syscall.Errno(0x61)
	ELOOP           = syscall.Errno(0x3e)
	EMFILE          = syscall.Errno(0x18)
	EMLINK          = syscall.Errno(0x1f)
	EMSGSIZE        = syscall.Errno(0x28)
	EMULTIHOP       = syscall.Errno(0x5a)
	ENAMETOOLONG    = syscall.Errno(0x3f)
	ENEEDAUTH       = syscall.Errno(0x51)
	ENETDOWN        = syscall.Errno(0x32)
	ENETRESET       = syscall.Errno(0x34)
	ENETUNREACH     = syscall.Errno(0x33)
	ENFILE          = syscall.Errno(0x17)
	ENOATTR         = syscall.Errno(0x57)
	ENOBUFS         = syscall.Errno(0x37)
	ENODEV          = syscall.Errno(0x13)
	ENOENT          = syscall.Errno(0x2)
	ENOEXEC         = syscall.Errno(0x8)
	ENOLCK          = syscall.Errno(0x4d)
	ENOLINK         = syscall.Errno(0x5b)
	ENOMEM          = syscall.Errno(0xc)
	ENOMSG          = syscall.Errno(0x53)
	ENOPROTOOPT     = syscall.Errno(0x2a)
	ENOSPC          = syscall.Errno(0x1c)
	ENOSYS          = syscall.Errno(0x4e)
	ENOTBLK         = syscall.Errno(0xf)
	ENOTCAPABLE     = syscall.Errno(0x5d)
	ENOTCONN        = syscall.Errno(0x39)
	ENOTDIR         = syscall.Errno(0x14)
	ENOTEMPTY       = syscall.Errno(0x42)
	ENOTRECOVERABLE = syscall.Errno(0x5f)
	ENOTSOCK        = syscall.Errno(0x26)
	ENOTSUP         = syscall.Errno(0x2d)
	ENOTTY          = syscall.Errno(0x19)
	ENXIO           = syscall.Errno(0x6)
	EOPNOTSUPP      = syscall.Errno(0x2d)
	EOVERFLOW       = syscall.Errno(0x54)
	EOWNERDEAD      = syscall.Errno(0x60)
	EPERM           = syscall.Errno(0x1)
	EPFNOSUPPORT    = syscall.Errno(0x2e)
	EPIPE           = syscall.Errno(0x20)
	EPROCLIM        = syscall.Errno(0x43)
	EPROCUNAVAIL    = syscall.Errno(0x4c)
	EPROGMISMATCH   = syscall.Errno(0x4b)
	EPROGUNAVAIL    = syscall.Errno(0x4a)
	EPROTO          = syscall.Errno(0x5c)
	EPROTONOSUPPORT = syscall.Errno(0x2b)
	EPROTOTYPE      = syscall.Errno(0x29)
	ERANGE          = syscall.Errno(0x22)
	EREMOTE         = syscall.Errno(0x47)
	EROFS           = syscall.Errno(0x1e)
	ERPCMISMATCH    = syscall.Errno(0x49)
	ESHUTDOWN       = syscall.Errno(0x3a)
	ESOCKTNOSUPPORT = syscall.Errno(0x2c)
	ESPIPE          = syscall.Errno(0x1d)
	ESRCH           = syscall.Errno(0x3)
	ESTALE          = syscall.Errno(0x46)
	ETIMEDOUT       = syscall.Errno(0x3c)
	ETOOMANYREFS    = syscall.Errno(0x3b)
	ETXTBSY         = syscall.Errno(0x1a)
	EUSERS          = syscall.Errno(0x44)
	EWOULDBLOCK     = syscall.Errno(0x23)
	EXDEV           = syscall.Errno(0x12)
)

// Signals
const (
	SIGABRT   = syscall.Signal(0x6)
	SIGALRM   = syscall.Signal(0xe)
	SIGBUS    = syscall.Signal(0xa)
	SIGCHLD   = syscall.Signal(0x14)
	SIGCONT   = syscall.Signal(0x13)
	SIGEMT    = syscall.Signal(0x7)
	SIGFPE    = syscall.Signal(0x8)
	SIGHUP    = syscall.Signal(0x1)
	SIGILL    = syscall.Signal(0x4)
	SIGINFO   = syscall.Signal(0x1d)
	SIGINT    = syscall.Signal(0x2)
	SIGIO     = syscall.Signal(0x17)
	SIGIOT    = syscall.Signal(0x6)
	SIGKILL   = syscall.Signal(0x9)
	SIGLIBRT  = syscall.Signal(0x21)
	SIGLWP    = syscall.Signal(0x20)
	SIGPIPE   = syscall.Signal(0xd)
	SIGPROF   = syscall.Signal(0x1b)
	SIGQUIT   = syscall.Signal(0x3)
	SIGSEGV   = syscall.Signal(0xb)
	SIGSTOP   = syscall.Signal(0x11)
	SIGSYS    = syscall.Signal(0xc)
	SIGTERM   = syscall.Signal(0xf)
	SIGTHR    = syscall.Signal(0x20)
	SIGTRAP   = syscall.Signal(0x5)
	SIGTSTP   = syscall.Signal(0x12)
	SIGTTIN   = syscall.Signal(0x15)
	SIGTTOU   = syscall.Signal(0x16)
	SIGURG    = syscall.Signal(0x10)
	SIGUSR1   = syscall.Signal(0x1e)
	SIGUSR2   = syscall.Signal(0x1f)
	SIGVTALRM = syscall.Signal(0x1a)
	SIGWINCH  = syscall.Signal(0x1c)
	SIGXCPU   = syscall.Signal(0x18)
	SIGXFSZ   = syscall.Signal(0x19)
)

// Error table
var errorList = [...]struct {
	num  syscall.Errno
	name string
	desc string
}{
	{1, "EPERM", "operation not permitted"},
	{2, "ENOENT", "no such file or directory"},
	{3, "ESRCH", "no such process"},
	{4, "EINTR", "interrupted system call"},
	{5, "EIO", "input/output error"},
	{6, "ENXIO", "device not configured"},
	{7, "E2BIG", "argument list too long"},
	{8, "ENOEXEC", "exec format error"},
	{9, "EBADF", "bad file descriptor"},
	{10, "ECHILD", "no child processes"},
	{11, "EDEADLK", "resource deadlock avoided"},
	{12, "ENOMEM", "cannot allocate memory"},
	{13, "EACCES", "permission denied"},
	{14, "EFAULT", "bad address"},
	{15, "ENOTBLK", "block device required"},
	{16, "EBUSY", "device busy"},
	{17, "EEXIST", "file exists"},
	{18, "EXDEV", "cross-device link"},
	{19, "ENODEV", "operation not supported by device"},
	{20, "ENOTDIR", "not a directory"},
	{21, "EISDIR", "is a directory"},
	{22, "EINVAL", "invalid argument"},
	{23, "ENFILE", "too many open files in system"},
	{24, "EMFILE", "too many open files"},
	{25, "ENOTTY", "inappropriate ioctl for device"},
	{26, "ETXTBSY", "text file busy"},
	{27, "EFBIG", "file too large"},
	{28, "ENOSPC", "no space left on device"},
	{29, "ESPIPE", "illegal seek"},
	{30, "EROFS", "read-only file system"},
	{31, "EMLINK", "too many links"},
	{32, "EPIPE", "broken pipe"},
	{33, "EDOM", "numerical argument out of domain"},
	{34, "ERANGE", "result too large"},
	{35, "EWOULDBLOCK", "resource temporarily unavailable"},
	{36, "EINPROGRESS", "operation now in progress"},
	{37, "EALREADY", "operation already in progress"},
	{38, "ENOTSOCK", "socket operation on non-socket"},
	{39, "EDESTADDRREQ", "destination address required"},
	{40, "EMSGSIZE", "message too long"},
	{41, "EPROTOTYPE", "protocol wrong type for socket"},
	{42, "ENOPROTOOPT", "protocol not available"},
	{43, "EPROTONOSUPPORT", "protocol not supported"},
	{44, "ESOCKTNOSUPPORT", "socket type not supported"},
	{45, "EOPNOTSUPP", "operation not supported"},
	{46, "EPFNOSUPPORT", "protocol family not supported"},
	{47, "EAFNOSUPPORT", "address family not supported by protocol family"},
	{48, "EADDRINUSE", "address already in use"},
	{49, "EADDRNOTAVAIL", "can't assign requested address"},
	{50, "ENETDOWN", "network is down"},
	{51, "ENETUNREACH", "network is unreachable"},
	{52, "ENETRESET", "network dropped connection on reset"},
	{53, "ECONNABORTED", "software caused connection abort"},
	{54, "ECONNRESET", "connection reset by peer"},
	{55, "ENOBUFS", "no buffer space available"},
	{56, "EISCONN", "socket is already connected"},
	{57, "ENOTCONN", "socket is not connected"},
	{58, "ESHUTDOWN", "can't send after socket shutdown"},
	{59, "ETOOMANYREFS", "too many references: can't splice"},
	{60, "ETIMEDOUT", "operation timed out"},
	{61, "ECONNREFUSED", "connection refused"},
	{62, "ELOOP", "too many levels of symbolic links"},
	{63, "ENAMETOOLONG", "file name too long"},
	{64, "EHOSTDOWN", "host is down"},
	{65, "EHOSTUNREACH", "no route to host"},
	{66, "ENOTEMPTY", "directory not empty"},
	{67, "EPROCLIM", "too many processes"},
	{68, "EUSERS", "too many users"},
	{69, "EDQUOT", "disc quota exceeded"},
	{70, "ESTALE", "stale NFS file handle"},
	{71, "EREMOTE", "too many levels of remote in path"},
	{72, "EBADRPC", "RPC struct is bad"},
	{73, "ERPCMISMATCH", "RPC version wrong"},
	{74, "EPROGUNAVAIL", "RPC prog. not avail"},
	{75, "EPROGMISMATCH", "program version wrong"},
	{76, "EPROCUNAVAIL", "bad procedure for program"},
	{77, "ENOLCK", "no locks available"},
	{78, "ENOSYS", "function not implemented"},
	{79, "EFTYPE", "inappropriate file type or format"},
	{80, "EAUTH", "authentication error"},
	{81, "ENEEDAUTH", "need authenticator"},
	{82, "EIDRM", "identifier removed"},
	{83, "ENOMSG", "no message of desired type"},
	{84, "EOVERFLOW", "value too large to be stored in data type"},
	{85, "ECANCELED", "operation canceled"},
	{86, "EILSEQ", "illegal byte sequence"},
	{87, "ENOATTR", "attribute not found"},
	{88, "EDOOFUS", "programming error"},
	{89, "EBADMSG", "bad message"},
	{90, "EMULTIHOP", "multihop attempted"},
	{91, "ENOLINK", "link has been severed"},
	{92, "EPROTO", "protocol error"},
	{93, "ENOTCAPABLE", "capabilities insufficient"},
	{94, "ECAPMODE", "not permitted in capability mode"},
	{95, "ENOTRECOVERABLE", "state not recoverable"},
	{96, "EOWNERDEAD", "previous owner died"},
	{97, "EINTEGRITY", "integrity check failed"},
}

// Signal table
var signalList = [...]struct {
	num  syscall.Signal
	name string
	desc string
}{
	{1, "SIGHUP", "hangup"},
	{2, "SIGINT", "interrupt"},
	{3, "SIGQUIT", "quit"},
	{4, "SIGILL", "illegal instruction"},
	{5, "SIGTRAP", "trace/BPT trap"},
	{6, "SIGIOT", "abort trap"},
	{7, "SIGEMT", "EMT trap"},
	{8, "SIGFPE", "floating point exception"},
	{9, "SIGKILL", "killed"},
	{10, "SIGBUS", "bus error"},
	{11, "SIGSEGV", "segmentation fault"},
	{12, "SIGSYS", "bad system call"},
	{13, "SIGPIPE", "broken pipe"},
	{14, "SIGALRM", "alarm clock"},
	{15, "SIGTERM", "terminated"},
	{16, "SIGURG", "urgent I/O condition"},
	{17, "SIGSTOP", "suspended (signal)"},
	{18, "SIGTSTP", "suspended"},
	{19, "SIGCONT", "continued"},
	{20, "SIGCHLD", "child exited"},
	{21, "SIGTTIN", "stopped (tty input)"},
	{22, "SIGTTOU", "stopped (tty output)"},
	{23, "SIGIO", "I/O possible"},
	{24, "SIGXCPU", "cputime limit exceeded"},
	{25, "SIGXFSZ", "filesize limit exceeded"},
	{26, "SIGVTALRM", "virtual timer expired"},
	{27, "SIGPROF", "profiling timer expired"},
	{28, "SIGWINCH", "window size changes"},
	{29, "SIGINFO", "information request"},
	{30, "SIGUSR1", "user defined signal 1"},
	{31, "SIGUSR2", "user defined signal 2"},
	{32, "SIGTHR", "unknown signal"},
	{33, "SIGLIBRT", "unknown signal"},
}
