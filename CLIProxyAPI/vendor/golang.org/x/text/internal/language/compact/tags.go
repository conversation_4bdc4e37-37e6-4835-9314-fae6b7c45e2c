// Copyright 2013 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package compact

var (
	und = Tag{}

	Und Tag = Tag{}

	Afrikaans            Tag = Tag{language: afIndex, locale: afIndex}
	Amharic              Tag = Tag{language: amIndex, locale: amIndex}
	Arabic               Tag = Tag{language: arIndex, locale: arIndex}
	ModernStandardArabic Tag = Tag{language: ar001Index, locale: ar001Index}
	Azerbaijani          Tag = Tag{language: azIndex, locale: azIndex}
	Bulgarian            Tag = Tag{language: bgIndex, locale: bgIndex}
	Bengali              Tag = Tag{language: bnIndex, locale: bnIndex}
	Catalan              Tag = Tag{language: caIndex, locale: caIndex}
	Czech                Tag = Tag{language: csIndex, locale: csIndex}
	Danish               Tag = Tag{language: daIndex, locale: daIndex}
	German               Tag = Tag{language: deIndex, locale: deIndex}
	Greek                Tag = Tag{language: elIndex, locale: elIndex}
	English              Tag = Tag{language: enIndex, locale: enIndex}
	AmericanEnglish      Tag = Tag{language: enUSIndex, locale: enUSIndex}
	BritishEnglish       Tag = Tag{language: enGBIndex, locale: enGBIndex}
	Spanish              Tag = Tag{language: esIndex, locale: esIndex}
	EuropeanSpanish      Tag = Tag{language: esESIndex, locale: esESIndex}
	LatinAmericanSpanish Tag = Tag{language: es419Index, locale: es419Index}
	Estonian             Tag = Tag{language: etIndex, locale: etIndex}
	Persian              Tag = Tag{language: faIndex, locale: faIndex}
	Finnish              Tag = Tag{language: fiIndex, locale: fiIndex}
	Filipino             Tag = Tag{language: filIndex, locale: filIndex}
	French               Tag = Tag{language: frIndex, locale: frIndex}
	CanadianFrench       Tag = Tag{language: frCAIndex, locale: frCAIndex}
	Gujarati             Tag = Tag{language: guIndex, locale: guIndex}
	Hebrew               Tag = Tag{language: heIndex, locale: heIndex}
	Hindi                Tag = Tag{language: hiIndex, locale: hiIndex}
	Croatian             Tag = Tag{language: hrIndex, locale: hrIndex}
	Hungarian            Tag = Tag{language: huIndex, locale: huIndex}
	Armenian             Tag = Tag{language: hyIndex, locale: hyIndex}
	Indonesian           Tag = Tag{language: idIndex, locale: idIndex}
	Icelandic            Tag = Tag{language: isIndex, locale: isIndex}
	Italian              Tag = Tag{language: itIndex, locale: itIndex}
	Japanese             Tag = Tag{language: jaIndex, locale: jaIndex}
	Georgian             Tag = Tag{language: kaIndex, locale: kaIndex}
	Kazakh               Tag = Tag{language: kkIndex, locale: kkIndex}
	Khmer                Tag = Tag{language: kmIndex, locale: kmIndex}
	Kannada              Tag = Tag{language: knIndex, locale: knIndex}
	Korean               Tag = Tag{language: koIndex, locale: koIndex}
	Kirghiz              Tag = Tag{language: kyIndex, locale: kyIndex}
	Lao                  Tag = Tag{language: loIndex, locale: loIndex}
	Lithuanian           Tag = Tag{language: ltIndex, locale: ltIndex}
	Latvian              Tag = Tag{language: lvIndex, locale: lvIndex}
	Macedonian           Tag = Tag{language: mkIndex, locale: mkIndex}
	Malayalam            Tag = Tag{language: mlIndex, locale: mlIndex}
	Mongolian            Tag = Tag{language: mnIndex, locale: mnIndex}
	Marathi              Tag = Tag{language: mrIndex, locale: mrIndex}
	Malay                Tag = Tag{language: msIndex, locale: msIndex}
	Burmese              Tag = Tag{language: myIndex, locale: myIndex}
	Nepali               Tag = Tag{language: neIndex, locale: neIndex}
	Dutch                Tag = Tag{language: nlIndex, locale: nlIndex}
	Norwegian            Tag = Tag{language: noIndex, locale: noIndex}
	Punjabi              Tag = Tag{language: paIndex, locale: paIndex}
	Polish               Tag = Tag{language: plIndex, locale: plIndex}
	Portuguese           Tag = Tag{language: ptIndex, locale: ptIndex}
	BrazilianPortuguese  Tag = Tag{language: ptBRIndex, locale: ptBRIndex}
	EuropeanPortuguese   Tag = Tag{language: ptPTIndex, locale: ptPTIndex}
	Romanian             Tag = Tag{language: roIndex, locale: roIndex}
	Russian              Tag = Tag{language: ruIndex, locale: ruIndex}
	Sinhala              Tag = Tag{language: siIndex, locale: siIndex}
	Slovak               Tag = Tag{language: skIndex, locale: skIndex}
	Slovenian            Tag = Tag{language: slIndex, locale: slIndex}
	Albanian             Tag = Tag{language: sqIndex, locale: sqIndex}
	Serbian              Tag = Tag{language: srIndex, locale: srIndex}
	SerbianLatin         Tag = Tag{language: srLatnIndex, locale: srLatnIndex}
	Swedish              Tag = Tag{language: svIndex, locale: svIndex}
	Swahili              Tag = Tag{language: swIndex, locale: swIndex}
	Tamil                Tag = Tag{language: taIndex, locale: taIndex}
	Telugu               Tag = Tag{language: teIndex, locale: teIndex}
	Thai                 Tag = Tag{language: thIndex, locale: thIndex}
	Turkish              Tag = Tag{language: trIndex, locale: trIndex}
	Ukrainian            Tag = Tag{language: ukIndex, locale: ukIndex}
	Urdu                 Tag = Tag{language: urIndex, locale: urIndex}
	Uzbek                Tag = Tag{language: uzIndex, locale: uzIndex}
	Vietnamese           Tag = Tag{language: viIndex, locale: viIndex}
	Chinese              Tag = Tag{language: zhIndex, locale: zhIndex}
	SimplifiedChinese    Tag = Tag{language: zhHansIndex, locale: zhHansIndex}
	TraditionalChinese   Tag = Tag{language: zhHantIndex, locale: zhHantIndex}
	Zulu                 Tag = Tag{language: zuIndex, locale: zuIndex}
)
